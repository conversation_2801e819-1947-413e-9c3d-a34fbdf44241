from contextvars import <PERSON><PERSON><PERSON>ar
import os
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from app.core.Settings import settings
import jwt, json

tenant_context = ContextVar("tenant_id", default=settings.KAVIA_ROOT_TENANT_ID)
figma_access_token = ContextVar("figma_access_token", default="")
opentopublic_context = ContextVar("opentopublic", default=False)
user_id_context= ContextVar("user_id", default=None)
global_user_id = None

KAVIA_ROOT_DB_NAME = f"{settings.STAGE}_kaviaroot"

NON_GATED_PATHS = [
    '/api/node/list_projects/',
    "/api/ws",
    "/api/auth",
    "/api/oauth/github/callback",
    "/api/health",
    "/api/products/list",
    "/api/manage",
    "/api/payment"
]

def update_figma_access_token(token: str):
    figma_access_token.set(token)

def get_opentopublic():
    return opentopublic_context.get()

def set_opentopublic(value: bool):
    opentopublic_context.set(value)

class TenantMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        tenant_id = None
        user_id = None
        authorization_header = request.headers.get("Authorization")
        
        # Try to get tenant_id from JWT token first
        if authorization_header:
            try:
                token = authorization_header.replace('Bearer ', '') if authorization_header.startswith('Bearer ') else authorization_header
                decoded = jwt.decode(token, options={"verify_signature": False})
                tenant_id = decoded.get('custom:tenant_id')
                jwt_tenant_id = tenant_id
                selected_tenant_id = request.headers.get('selected_tenant_id')
                user_id=decoded.get('cognito:username') 
                print("middleware user_id",user_id)               # Check if public tenant is selected from cookies
                # Debug cookies to check if they're empty
                print("request.url.path", request.url.path)
                if request.headers.get('is_public_selected') == 'true' and not any(request.url.path.startswith(path) for path in NON_GATED_PATHS):
                    print("Public tenant is selected")
                    public_tenant_id = selected_tenant_id
                    if public_tenant_id:
                        tenant_id = public_tenant_id
                else:
                    print("Private tenant is selected")
                    tenant_id = jwt_tenant_id
                
                if tenant_id.startswith("default"):
                    tenant_id = settings.KAVIA_B2C_CLIENT_ID
                
            except:
                pass
        
        # Fallback to headers/cookies if not found in JWT
        # tenant_id = tenant_id or request.headers.get("X-Tenant-Id") or request.cookies.get('X-Tenant-Id') or settings.KAVIA_ROOT_TENANT_ID
        # tenant_id = "T0005"
        if tenant_id != tenant_context.get():
            tenant_context.set(tenant_id)
        if user_id != user_id_context.get(): 
            user_id_context.set(user_id) 
        return await call_next(request)
    
def get_tenant_id():
    
    if settings.CODEGEN:
        input_arguments = json.loads(os.environ.get("input_arguments"))
        if input_arguments.get("tenant_id"):
            return input_arguments.get("tenant_id")
        return settings.KAVIA_ROOT_TENANT_ID
    if tenant_context.get() == None:
        input_arguments = json.loads(os.environ.get("input_arguments", "{}"))
        if input_arguments.get("tenant_id"):
            return input_arguments.get("tenant_id")
        return settings.KAVIA_ROOT_TENANT_ID
    input_arguments = json.loads(os.environ.get("input_arguments", "{}"))
    if input_arguments.get("tenant_id"):
        return input_arguments.get("tenant_id") 
    return tenant_context.get()

def set_global_user_id(user_id=None):
    global global_user_id
    if user_id != None:
        global_user_id = user_id

def get_user_id():
    global global_user_id
    
    if os.environ.get("user_id"):
        return os.environ.get("user_id")
    
    if global_user_id != None: 
        return global_user_id
    
    print("get_user_id in function",user_id_context.get())
    if user_id_context.get() == None:
        print("USER ID CONTEXT IS NONE")
        return None
    return user_id_context.get()

def get_tenant_based_name(name, reverse=False):
    if reverse:
        return f'{name}_{get_tenant_id()}'
    else:
        return f'{get_tenant_id()}_{name}'
    
def get_tenant_based_db_name(name):
    '''
    Get the tenant based db name for mongo db
    tenant_id is the tenant id
    stage is the stage of the app
    '''
    # if os.environ.get('LOCAL_DEBUG'):
    #     return f"{settings.STAGE}_{os.environ.get('TENANT_ID')}"

    if name == settings.STAGE:
        name = get_tenant_based_name(name, reverse=True)
    elif name != KAVIA_ROOT_DB_NAME:
        '''
        STAGE_DBNAME_T0000
        '''
        name = f'{settings.STAGE}_{get_tenant_based_name(name, reverse=True)}'
    
    print("Current tenant : ", name)
    return name

def is_system_collection(collection_name: str) -> bool:
    """
    Check if collection is a system-level collection
    """
    system_collections = {
        'tenant_permissions', 
        'tenant_users', 
        'tenant_groups', 
        'tenant_organizations',
        'tenant_plans'
    }
    return collection_name in system_collections

def is_tenant_admin():
    return get_tenant_id() == settings.KAVIA_ROOT_TENANT_ID
