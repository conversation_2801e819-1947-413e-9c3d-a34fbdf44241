import jwt
from functools import lru_cache
import time
import boto3
import requests
from app.core.Settings import settings
import json
import base64
from fastapi import HTTPException, status, Request

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import rsa

region = "us-east-1"  # Replace with your actual AWS region
userpool_id = settings.AWS_COGNITO_USER_POOL_ID  # Replace with your Cognito user pool ID
app_client_id = settings.AWS_COGNITO_APP_CLIENT_ID  # Replace with your app client ID

cognito_idp = boto3.client("cognito-idp", region_name=region, aws_access_key_id=settings.AWS_ACCESS_KEY_ID, aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

@lru_cache(maxsize=1)
def get_jwks():
    keys_url = f"https://cognito-idp.{region}.amazonaws.com/{userpool_id}/.well-known/jwks.json"
    response = requests.get(keys_url)
    return json.loads(response.text)["keys"]

def is_admin(request: Request):
    user = get_current_user(request)
    if user and user.get("custom:is_admin") == "true":
        return True
    return False

def get_current_user(request: Request):
    try:
        user = request.state.user
        if not user:
            return None
            # raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")
        return user
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")

def check_token_and_get_user_id(token, secret_key=None):
    try:
        if token.startswith("Bearer "):
            token = token[7:]  # Strip the "Bearer " prefix if present
            
        decoded = jwt.decode(token, options={"verify_signature": False})
        
        # Check if this is a Google SSO token
        if decoded.get('iss') == 'kavia-google-sso' or decoded.get('auth_provider') == 'google_sso':
            # For Google SSO tokens, verify with our secret
            try:
                verified_decoded = jwt.decode(
                    token, 
                    "kavia-google-sso-secret", 
                    algorithms=["HS256"],
                    options={"verify_exp": True}
                )
                user_id = verified_decoded['cognito:username']
                return user_id
            except jwt.ExpiredSignatureError:
                return "Google SSO token has expired"
            except jwt.InvalidTokenError as e:
                return f"Invalid Google SSO Token: {str(e)}"

        # Regular token handling (no verification)
        if time.time() > decoded['exp']:
            return "Token has expired"
        
        # Extract user ID or any other information you need
        user_id = decoded['cognito:username']  # Assuming 'sub' is the user ID
        return user_id
    
    except jwt.ExpiredSignatureError:
        return "Token has expired"
    except jwt.InvalidTokenError as e:
        return f"Invalid Token: {str(e)}"
    except Exception as e:
        return f"Error: {str(e)}"

    
def decode_token(token: str) -> dict:
    try:
        return jwt.decode(token, options={"verify_signature": False})
    except Exception as e:
        return f"Error: {str(e)}"


def verify_token_and_get_user_details(token: str) -> dict:
    try:
        if token.startswith("Bearer "):
            token = token[7:]

        # Check if this is a Google SSO token first
        try:
            # Decode without verification to check the issuer
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            
            # If it's a Google SSO token, verify with our custom secret
            if unverified_claims.get('iss') == 'kavia-google-sso' or unverified_claims.get('auth_provider') == 'google_sso':
                # Verify Google SSO token with our secret
                claims = jwt.decode(
                    token, 
                    "kavia-google-sso-secret", 
                    algorithms=["HS256"],
                    options={"verify_exp": True}
                )
                return claims
                
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Google SSO token has expired")
        except jwt.InvalidTokenError:
            # Not a Google SSO token, continue with regular Cognito verification
            pass

        # Regular Cognito token verification
        headers = jwt.get_unverified_header(token)
        kid = headers["kid"]

        key = next((k for k in get_jwks() if k["kid"] == kid), None)
        if not key:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

        public_key = rsa.RSAPublicNumbers(
            n=int.from_bytes(base64.urlsafe_b64decode(key["n"] + "=="), byteorder="big"),
            e=int.from_bytes(base64.urlsafe_b64decode(key["e"] + "=="), byteorder="big"),
        ).public_key(backend=default_backend())

        public_key_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        claims = jwt.decode(
            token, 
            public_key_pem, 
            algorithms=["RS256"],
            audience=app_client_id,
            options={"verify_exp": True}
        )
        return claims

    except ( KeyError, IndexError, requests.exceptions.RequestException) as e:
        if isinstance(e):
            # Provide more specific JWTError handling here if needed
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
        else:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while processing the token")