def generate_ecs_config():
   ecs_config = '''resource "aws_ecr_repository" "chat_app_repo" {
  name = var.app_name
  tags = var.tags
}

resource "aws_ecs_cluster" "chat_app_cluster" {
  name = "${var.app_name}-cluster"
  tags = var.tags
}

resource "aws_ecs_task_definition" "chat_app_task" {
  family                   = var.app_name
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.task_cpu
  memory                   = var.task_memory
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn

  container_definitions = jsonencode([
    {
      name  = var.app_name
      image = "${aws_ecr_repository.chat_app_repo.repository_url}:latest"
      portMappings = [
        {
          containerPort = var.container_port
          hostPort      = var.container_port
          protocol      = "tcp"
        }
      ]
    }
  ])

  tags = var.tags
}

resource "aws_ecs_service" "chat_app_service" {
  name            = "${var.app_name}-service"
  cluster         = aws_ecs_cluster.chat_app_cluster.id
  task_definition = aws_ecs_task_definition.chat_app_task.arn
  desired_count   = var.service_desired_count
  launch_type     = "FARGATE"

  network_configuration {
    subnets          = [aws_subnet.public_subnet.id, aws_subnet.public_subnet_2.id]
    security_groups  = [aws_security_group.chat_app_sg.id]
    assign_public_ip = true
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.chat_app.arn
    container_name   = var.app_name
    container_port   = var.container_port
  }

  depends_on = [aws_lb_listener.chat_app]

  tags = var.tags
}'''

   return ecs_config
