from app.utils.aws.permission_manager import MongoPermissionManager
from fastapi import APIRouter, HTTPException, Query, Path
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Optional

# Models
class GroupRequest(BaseModel):
    group_names: List[str] = Field(..., description="List of group names")
    class Config:
        schema_extra = {
            "example": {
                "group_names": ["admin", "user", "manager"]
            }
        }

class PermissionResponse(BaseModel):
    permission_id: str
    permissions: Dict[str, Dict[str, bool]]
    created_at: Optional[str]
    updated_at: Optional[str]

class MultiplePermissionResponse(BaseModel):
    permissions: Dict[str, Dict[str, bool]]

# Router setup
_SHOW_NAME = "Permission Management"
router = APIRouter(
    prefix="/permissions",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

# Permission manager instance
permission_manager = MongoPermissionManager()

def generate_permission_id(tenant_id: str, group_name: str) -> str:
    """Generate permission ID from tenant_id and group_name"""
    return f"{tenant_id}_{group_name}"

@router.get(
    "/{tenant_id}/{group_name}",
    response_model=PermissionResponse,
    summary="Get single permission",
    description="Retrieve permissions for a specific tenant and group"
)
async def get_permission(
    tenant_id: str = Path(..., description="Tenant identifier"),
    group_name: str = Path(..., description="Group name")
):
    try:
        permission_id = generate_permission_id(tenant_id, group_name)
        permission = permission_manager.get_permission(permission_id)
        
        if not permission:
            raise HTTPException(
                status_code=404,
                detail=f"Permission not found for tenant {tenant_id} and group {group_name}"
            )
        
        return PermissionResponse(
            permission_id=permission["_id"],
            permissions=permission["permissions"],
            created_at=permission.get("createdAt", "").isoformat() if permission.get("createdAt") else None,
            updated_at=permission.get("updatedAt", "").isoformat() if permission.get("updatedAt") else None
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post(
    "/{tenant_id}",
    response_model=MultiplePermissionResponse,
    summary="Get multiple permissions",
    description="Retrieve and combine permissions for multiple groups within a tenant"
)
async def get_permissions(
    tenant_id: str = Path(..., description="Tenant identifier"),
    request: GroupRequest = None
):
    try:
        # Generate permission IDs from tenant_id and group names
        permission_ids = [
            generate_permission_id(tenant_id, group_name)
            for group_name in request.group_names
        ]
        
        # Get combined permissions
        combined_permissions = permission_manager.get_permissions(permission_ids)
        
        return MultiplePermissionResponse(
            permissions=combined_permissions["permissions"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))