from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json, os


class WorkItemDependencies(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_node_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_node_id, title, description)
        # Additional attributes specific to WorkItem configurations can be added here

    async def async_initialize(self):
        # Initialization specific to WorkItem dependencies
        await super().async_initialize()


    def get_output_format_for_discussion(self, key):  # overriding this to remove the modifications from the output format.
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        template_path = os.path.join(base_dir, 'discussions', 'prompt_templates')                    
        with open(f"{template_path}/output_formats.txt", "r") as file:
            content = file.read()
            output_formats = content.split("\n\n")
            
            for output_format in output_formats:
                if output_format.startswith(key + ":"):
                    output = output_format.split(":", 1)[1].strip()
                    return ('{' + output  + '}')
        
        return ""
    
    def prompt_for_starting_discussion(self):
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))) )
        templates_dir = os.path.join(base_dir, 'discussions', 'prompt_templates')
        env = Environment(loader=FileSystemLoader(templates_dir))

        discussion_type = self.get_converted_discussion_type(self.discussion_type, self.node_type)

        template_name = f"workitem_dependencies.prompt"
        template = env.get_template(template_name)

        output_format = self.get_output_format_for_discussion(self.discussion_type)
        agents_config_path = os.path.join(base_dir, 'agents', 'agents.json')
        with open(agents_config_path, 'r') as file:
            ai_agents = json.load(file)

        user_prompt = template.render(
            prompt_type = "user",
            details_for_discussion=self.retrieved_info,
            root_node_type=self.root_node_type,
            output_format=output_format,
            ai_agents=ai_agents # this parameter is unique to this configuration discussion, thus this function override.
        )

        system_prompt = template.render(prompt_type="system", root_node_type=self.root_node_type, discussion_type=discussion_type)

        return user_prompt, system_prompt
    
    
    def get_modifications_from_llm_output(self):

        #dependencies = llm_output.get('Dependencies')

        return self.modifications
    
    async def merge_captured_items(self):
        dependencies = self.modifications.get('dependencies')
        if dependencies:
            # update the dependencies
            await self.db.update_dependencies(self.node_id, dependencies)
            self.update_logger.info("Updated dependencies of node {} with {}".format(self.node_id, json.dumps(dependencies, indent=2)))


# Registering the subclass in the factory
DiscussionFactory.register('dependencies', WorkItemDependencies, "WorkItem")