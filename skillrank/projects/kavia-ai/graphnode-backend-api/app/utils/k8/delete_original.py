from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
from app.utils.aws.ssm_loader import load_ssm_dev_param, load_ssm_qa_param, load_ssm_pre_prod_param
# Prompt the user for PROJECT_ID and ENV_NAME (ACTION is fixed to delete)
project_id = "051313"
env_name = "beta"

if not project_id or not env_name:
    raise ValueError("Both PROJECT_ID and ENV_NAME must be provided.")

action = "delete"

# Load in-cluster configuration
kubeconfig_dict = load_ssm_pre_prod_param()
try:
    config.load_kube_config_from_dict(config_dict=kubeconfig_dict)
except config.ConfigException:
    config.load_incluster_config()
except config.ConfigException:
    config.load_kube_config()


# Create Kubernetes API client
batch_v1 = client.BatchV1Api()
networking_v1 = client.NetworkingV1Api()

# Define the job name and namespace
job_name = f"codegen-{project_id}-{env_name}-{action}"
namespace = "duploservices-kavia-beta"

# Define kubectl command for delete action
kubectl_command = f"""
echo "Deleting deployment for {job_name}..."
# Fix the nginx pod selection to only select one pod
nginx_pod=$(kubectl get pods -n {namespace} -l app=nginx -o name | head -1 | cut -d'/' -f2)
echo "Using nginx pod: $nginx_pod"

# Use the defined namespace variable
if [ -n "$nginx_pod" ]; then
    svc_name=$(kubectl exec -n {namespace} "$nginx_pod" -- cat /etc/nginx/conf.d/custom_{project_id}.conf | grep -i "proxy_pass" | head -1 | awk '{{print $2}}' | sed -E 's|http://([^:]+):.*|\\1|')
    echo "Service name: $svc_name"
    
    if [ -n "$svc_name" ]; then
        deployment_name=$(echo $svc_name | cut -d "-" -f2-3)
        echo "Deployment name: $deployment_name"
        svc_name_clusterip="internal-clusterip-$deployment_name"
        echo "Service ClusterIP name: $svc_name_clusterip"
        pvc_name="pvc-$deployment_name"
        echo "PVC NAME: $pvc_name"
        
        echo "Deleting deployment and service"
        kubectl delete deployment "$deployment_name" -n {namespace} --ignore-not-found=true
        kubectl delete service "$svc_name" -n {namespace} --ignore-not-found=true
        kubectl delete service "$svc_name_clusterip" -n {namespace} --ignore-not-found=true
        kubectl delete pvc "$pvc_name" -n {namespace} --ignore-not-found=true
        kubectl exec -n {namespace} "$nginx_pod" -- rm -f /etc/nginx/conf.d/custom_{project_id}.conf
    else
        echo "Error: Could not determine service name"
    fi
else
    echo "Error: No nginx pod found"
fi
"""

# Define the job manifest
job_manifest = {
    "apiVersion": "batch/v1",
    "kind": "Job",
    "metadata": {
        "name": job_name,
        "namespace": namespace,
        "labels": {
            "app": job_name,
            "owner": "duploservices",
            "tenantname": namespace,
        },
    },
    "spec": {
        "parallelism": 1,
        "completions": 1,
        "backoffLimit": 6,
        "template": {
            "metadata": {
                "labels": {
                    "app": job_name,
                    "owner": "duploservices",
                    "tenantname": namespace,
                },
            },
            "spec": {
                "serviceAccountName": "duploservices-kavia-beta-edit-user",
                "restartPolicy": "Never",
                "containers": [
                    {
                        "name": "duplocloudcodegen",
                        "image": "bitnami/kubectl:latest",
                        "command": ["/bin/sh", "-c"],
                        "args": [kubectl_command],
                        "env": [
                            {"name": "ACTION", "value": action},
                            {"name": "PROJECT_ID", "value": project_id},
                            {"name": "ENV_NAME", "value": env_name},
                        ],
                        "volumeMounts": [
                            {"name": "codegenservicedeploymentbeta", "mountPath": "/app"},
                            {"name": "ingressservicebeta", "mountPath": "/app/ingress"},
                            {"name": "codegenpvc", "mountPath": "/app/pvc"},
                            {"name": "nginx", "mountPath": "/app/nginx"},
                        ],
                    }
                ],
                "volumes": [
                    {
                        "name": "codegenservicedeploymentbeta",
                        "configMap": {"name": "codegenservicedeploymentbeta"},
                    },
                    {
                        "name": "ingressservicebeta",
                        "configMap": {"name": "ingressservicebeta"},
                    },
                    {
                        "name": "codegenpvc",
                        "configMap": {"name": "codegenpvc"},
                    },
                    {
                        "name": "nginx",
                        "configMap": {"name": "nginx"},
                    },
                ],
            },
        },
    },
}

# Monitor job completion and delete it upon success
def wait_for_job_completion(job_name, namespace):
    while True:
        job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
        if job.status.succeeded:
            print(f"Job {job_name} completed successfully, deleting...")
            batch_v1.delete_namespaced_job(
                name=job_name,
                namespace=namespace,
                body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
            )
            print(f"Deleted job: {job_name}")
            break
        time.sleep(5)

# Create the delete job
try:
    # Check if job already exists, and delete it if it does
    try:
        existing_job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
        print(f"Job {job_name} already exists, deleting it first...")
        batch_v1.delete_namespaced_job(
            name=job_name,
            namespace=namespace,
            body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
        )
        
        # Wait for the job to be fully deleted
        retry_count = 0
        max_retries = 10
        while retry_count < max_retries:
            try:
                batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
                print(f"Waiting for job {job_name} to be deleted...")
                time.sleep(5)
                retry_count += 1
            except ApiException as e:
                if e.status == 404:
                    print(f"Job {job_name} has been deleted successfully")
                    break
                else:
                    raise
        
        if retry_count >= max_retries:
            print(f"Warning: Timed out waiting for job {job_name} to be deleted")
    
    except ApiException as e:
        if e.status != 404:  # 404 means job doesn't exist, which is fine
            raise
        print(f"Job {job_name} does not exist, proceeding with creation")
    
    # Now create the new job
    response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
    print(f"Job created: {response.metadata.name}")
    wait_for_job_completion(job_name, namespace)

except ApiException as e:
    print(f"An error occurred: {e}")
    raise
