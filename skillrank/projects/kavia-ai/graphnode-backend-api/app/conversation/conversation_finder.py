from datetime import datetime
from app.connection.establish_db_connection import get_node_db
# from app.conversation.codeinspector_agent import CodeinspectorAgent
from app.core.Settings import settings
from app.models.chat_model import ChatContext
from openai import OpenAI
import logging
import json
from llm_wrapper.core.llm_interface import LLMInterface
from app.connection.establish_db_connection import get_mongo_db
import os
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.utils.conversation_utils import ConversationUtils
from app.utils.logs_utils import get_path
from app.connection.establish_db_connection import get_mongo_db
from app.conversation.conversation_session_manager import update_chat_session
from app.utils.datetime_utils import generate_timestamp
from app.connection.tenant_middleware import get_tenant_id
from app.utils.file_utils.upload_utils import get_tenant_bucket
from app.utils.prodefn.projdefn import ProjDefn, ProjDefnReporter, ProjDefnDocSpecifier
from app.utils.prodefn.projdefn_helper import Reporter, Helpers, ProjDefn_Helper
from app.utils.prodefn.docs_tool import DocsTools
from app.discussions.discussion import Discussion
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.kg_inspect.kg_tool import KgTools
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
import boto3

client = OpenAI(api_key=settings.OPENAI_API_KEY)

def setup_logger(name, base_path, level=logging.INFO):
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(base_path, "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # Create file handler
    handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
    handler.setLevel(level)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(handler)
    
    return logger

def get_project_root():
    """Get the root directory of the FastAPI project."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Navigate up until we find the root directory (where main.py or similar exists)
    while current_dir != '/':
        if os.path.exists(os.path.join(current_dir, 'main.py')) or \
           os.path.exists(os.path.join(current_dir, 'app')):
            return current_dir
        current_dir = os.path.dirname(current_dir)
    return None


class ConversationFinder:
    
    def __init__(self, context: ChatContext) -> None:
        self.instance_name = 'conversation_finder'
        self.utils = ConversationUtils(context)
        self.llm = LLMInterface(str(get_path()), self.instance_name, context.user_id, context.project_id, context.agent_name, mongo_handler = get_mongo_db())
        self.context = context
        
        # system prompt for conversation finder
        # template = Environment(loader=FileSystemLoader('app/conversation/prompts')).get_template('agent_finder.prompt')
        # self.system_prompt = template.render(prompt_type="system")
        self.node_db = get_node_db()

        if context.session:
            self.tenant_id = get_tenant_id()
            self.project_id = self.context.project_id
            self.user_node_id = None
            self.session_id = self.context.session['session_id']
            self.doc_tools = None
            self.discussion_tools = None
            self.code_tools = None
            self.project_knowledge = False

    async def _load_project_knowledge(self):

        self.s3_client = boto3.client('s3',
                          aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                          region_name=settings.AWS_REGION
                          )

        project_knowledge = False

        # Construct S3 key for project definition
        s3_key = f"extracted-docs-{self.tenant_id}/project-{self.project_id}/digested-files"
        print(f"Checking S3 path: {s3_key}")

        bucket_name = get_tenant_bucket(self.tenant_id)
        try:
            # List objects in the directory
            response = self.s3_client.list_objects_v2(
                Bucket=bucket_name,
                Prefix=s3_key
            )

            # Check if any files start with doc_
            if 'Contents' in response:
                for item in response['Contents']:
                    file_name = os.path.basename(item['Key'])
                    if file_name.startswith('project_definition.json'):
                        project_knowledge = True
                        print(f"Found project_definition file in S3: {file_name}")
                        break

                if not project_knowledge:
                    print("No doc_ files found in the directory")
            else:
                project_knowledge = False
                print("No files found in the directory")

        except Exception as e:
            project_knowledge = False
            print(f"Error checking S3: {str(e)}")

        self.project_knowledge = project_knowledge

    async def initialize_tools_and_schemas(self):
        """Initialize tools and schemas used by the system context configuration"""
        try:
            base_path = "/tmp/doc_ingestion/dir"
            if not base_path:
                raise ValueError("Could not determine base path for tools")

            doc_schemas = []
            discussion_schemas = []
            code_schemas = []

            self.tenant_id = get_tenant_id()
            if self.project_knowledge:
                bucket_name = get_tenant_bucket(self.tenant_id)

                # Initialize tools with S3 configuration
                s3_config = {
                    "use_s3": True if self.tenant_id else False,
                    "s3_bucket": bucket_name,
                    "s3_base_path": f"extracted-docs-{self.tenant_id}" if self.tenant_id else None,
                    "project_id": self.project_id,
                    "s3_client": self.s3_client
                }
                print(f"Using S3 configuration: {s3_config}")

                # Initialize DocsTools with S3 configuration
                self.doc_tools = DocsTools(
                    base_path=base_path,
                    logger=None,
                    s3_config=s3_config,
                    project_id = self.project_id,
                    tenant_id = self.tenant_id
                )

            # Initialize standard DiscussionTools
            self.discussion_tools = DiscussionTools(
                base_path=None,
                logger=None,
                user_id=self.user_node_id,
                discussion=self.discussion
            )

            session_id = None
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='project_repositories'
            )

            result = await mongo_handler.get_one(
                filter={
                    'project_id': self.context.project_id,
                },
                db=mongo_handler.db
            )
            if result:
                repositories = result.get('repositories', [])
                for repo in repositories:
                    branches = repo.get('branches',[])
                    for branch in branches:
                        builds = branch.get('builds',[])
                        build_session_id = builds['build_session_id']
                        if build_session_id:
                            session_id = build_session_id
                            break
                    if session_id:
                        break

            if session_id:
                general_registry = ToolRegistry()
                general_registry.register_tool("KgTools", KgTools)
                general_factory = DynamicToolFactory(general_registry)
                general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])
                self.code_tools = general_exec_agent('/test', logger=None, user_id=session_id)

            # Get tools' schemas
            if self.doc_tools:
                doc_schemas = self.doc_tools.function_schemas
            if self.discussion_tools:
                discussion_schemas = self.discussion_tools.function_schemas
            if self.code_tools:
                code_schemas = self.code_tools.function_schemas

            # Combined function schemas
            self.function_schemas = doc_schemas + discussion_schemas + code_schemas

            # Create combined function executor
            self.function_executor = {}
            if self.doc_tools:
                self.function_executor.update(self.doc_tools.function_mapping)
            if self.discussion_tools:
                self.function_executor.update(self.discussion_tools.function_mapping)
            if self.code_tools:
                self.function_executor.update(self.code_tools.function_mapping)

            async def combined_executor(function_name, function_args):
                """Enhanced function executor with error handling"""
                try:
                    print(f"Function Name: {function_name}")
                    print(f"Function Arguments: {json.dumps(function_args, indent=2)}")

                    if not isinstance(function_args, dict):
                        raise TypeError(f"Function arguments must be a dictionary, got {type(function_args)}")

                    # Check in doc_tools mapping
                    if self.doc_tools and function_name in self.doc_tools.function_mapping:
                        func = self.doc_tools.function_mapping[function_name]
                        return func(**function_args)
                    # Check in discussion_tools mapping
                    elif self.discussion_tools and function_name in self.discussion_tools.function_mapping:
                        func = self.discussion_tools.function_mapping[function_name]
                        return await func(**function_args)
                    elif self.code_tools and function_name in self.code_tools.function_mapping:
                        func = self.code_tools.function_mapping[function_name]
                        return func(**function_args)
                    else:
                        available_functions = []
                        if self.doc_tools:
                            available_functions.append(list(self.doc_tools.function_mapping.keys()))
                        if self.discussion_tools:
                             available_functions.append(list(self.discussion_tools.function_mapping.keys()))
                        if self.code_tools:
                            available_functions.append(list(self.code_tools.function_mapping.keys()))
                        raise ValueError(f"Unknown function: {function_name}. Available functions: {available_functions}")

                except Exception as e:
                    print(f"Error in function execution: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    raise

            self.function_executor = combined_executor
            self.schemas_initialized = True

        except Exception as e:
            print(f"Failed to initialize tools and schemas: {str(e)}")
            raise
        
    async def _create_conversation(self, _type, user_node_id, actual_username, session_id): 
        print("Creating conversation with the following parameters:")
        print(f"Type: {_type}")
        print(f"User Node ID: {user_node_id}")
        print(f"Actual Username: {actual_username}")
        print(f"Session ID: {session_id}")
    
        build_ids = session_id.split('-')[1:]
        
        print("COGNITO DETAILS: ", )

        today = datetime.now()
        general_discussion = await self.node_db.create_node(
            [_type],
            {
                'Username': str(actual_username),
                'session_name':"untitled",
                'description': "",
                'IsDeleted': False, 
                'CreatedAt': today.strftime('%Y-%m-%d %H:%M:%S'),
                'ProjectId': int(self.context.project_id),
                **({"BuildIds": build_ids} if build_ids is not None else {})
            },
            user_node_id
        )
        
        self.context.discussion_id = general_discussion.get('id')

        if self.context.session:
            self.context.session['discussion_id'] = self.context.discussion_id
            await update_chat_session(self.session_id, self.context.session)
        
        return general_discussion.get('id')
      
    async def _iterate_chat(self,):

        is_new_discussion = False
        messages = []
        user = await self.node_db.get_node_by_property('User', 'Username', self.context.user_id)
        if user:
            self.user_node_id = user.get('id')
            if self.context.discussion_id and self.context.session.get('discussion_id') != self.context.discussion_id:
                self.context.session['discussion_id'] = self.context.discussion_id
                await update_chat_session(self.session_id, self.context.session)
            if self.context.discussion_id:
                discussion = await self.node_db.get_node_by_id(self.context.discussion_id)
                if not discussion:
                    self.context.discussion_id = None
                    self.context.session['discussion_id'] = self.context.discussion_id
                    await update_chat_session(self.session_id, self.context.session)
            if self.context.discussion_id:
                messages = await self.utils._load_discussion(self.context.discussion_id,count=20)
            else:
                await self._create_conversation('Conversation',self.user_node_id, user.get('properties')['Name'], self.session_id)
                is_new_discussion = True

            self.discussion = Discussion('Conversation',self.user_node_id,self.context.discussion_id)
            self.discussion.function_schema_type = self.discussion.discussion_type
            self.discussion.current_user = self.user_node_id

            await self._load_project_knowledge()

            await self.initialize_tools_and_schemas()

            model= 'claude-3-5-sonnet-20241022'
            system_prompt = 'You are an expert systems architect.  You excel at providing detailed answers to questions about projects based on your expertise and available project documentation.' \
                           'A user has asked a question about a project.  Your task is to use your expertise and the available project knowledge and documentation ' \
                           'to provide detailed answers.  You have been provided with a set of tools to access project knowledge, documentation, and architectural design nodes. ' \
                           'Extensive inforamation about this project is available via tools.  This information consists of the results of indepth analysis of ' \
                           'project definition documentation as well as the actual documents.  Use the find_relevant_keys tool to identify keys for requirements analysis ' \
                           'pertinent to your task.  The relevant information can then be obtained using the get_key_values tool.  Use the find_relevant_document_chunks to ' \
                           'identify portions of actual documents pertinent to your task.  The docment chunks can then be obtained using the get_document_chunk tool. Similarly, '\
                           'relevant images can be identified via find_relevant_document_images and get_document_image. '\
                           'Use the find_relevant_nodes tool to look for information about epics, user stories, functional requirements, and non-functional requirements.  ' \
                           'Calling find_relevant_nodes with the project node_id and a set of search terms will return a list of ids of nodes related to these terms.  ' \
                           f'The get_nodes tool can then be used to access the contents of these nodes. Use the project_id value {self.project_id} as the parent_node argument for calls to find_relevant_nodes.' \
                           'Use the tool find_nodes_by_type to get a list of node id and node types.  For example, to get a list of Epics call find_nodes_by_type passing  ' \
                           'the project_id as the parent_node and Epic in node_types.  To get a list of user stories for an epic, call find_nodes_by_type passing ' \
                           'the epic_id as the parent_node and USerStory in node_types. ' \
                           'If you have a node id and want its contents call get_nodes with the desired id in the node_ids list. ' \
                           'In your responses do not use the term "document chunk" or "node".  When referring to a node use its id number and type: eg. Epic 1103 or User Story 29131, etc.' \
                           'Do not refer to tool names in your responses. ' \
                           'When emitting lists of numbered items, make sure each list item has the next successive number - do not number each item as number 1.' \
                           'The user question is in the user_prompt'
            user_prompt =  f'{self.context.message}'
            messages.append({
                    'role': 'system',
                    'created_at': generate_timestamp(),
                    'content': system_prompt
                })
            messages.append({
                    'role': 'user',
                    'timestamp': generate_timestamp(),
                    'content': user_prompt
                })
            try:
                response = await self.llm.llm_interaction_wrapper(
                    messages=messages.copy(),
                    user_prompt=None,
                    system_prompt=None,
                    response_format={'type': 'text'},
                    model= model,
                    stream=True,
                    function_schemas=self.function_schemas,
                    function_executor=self.function_executor
                )
                llm_response = {
                    'role': 'assistant',
                    'content': '',
                    'is_stream_complete': False
                }

                content_buffer = ""
                tool_call_buffer = None
                llm_response = {
                    'role': 'assistant',
                    'content': '',
                    'is_stream_complete': False
                }

                async for chunk in response:
                    try:
                        if isinstance(chunk, dict):
                            print(f"DEBUG - Received chunk type: {type(chunk)}")
                            print(f"DEBUG - Chunk content: {chunk}")

                            # Case 1: Function call
                            if 'function_call' in chunk:
                                tool_call_buffer = {
                                    'id': str(chunk['function_call'].get('id')),
                                    'type': 'function',
                                    'function': {
                                        'name': chunk['function_call'].get('name'),
                                        'arguments': chunk['function_call'].get('arguments')
                                    }
                                }
                                yield f"data: {json.dumps({'function_call': tool_call_buffer})}\n\n"

                            # Case 2: Function response
                            elif 'content' in chunk and isinstance(chunk['content'], dict):
                                if 'modifications' in chunk['content']:
                                    yield f"data: {json.dumps({'modifications': chunk['content']['modifications']})}\n\n"
                                else:
                                    yield f"data: {json.dumps(chunk['content'])}\n\n"
                                content_buffer = '\n\n**If you are okay with the modifications, please proceed with merge.**\n\n'
                                llm_response['content'] = llm_response['content'] + content_buffer

                            # Case 3: Other dictionary formats
                            else:
                                yield f"data: {json.dumps(chunk)}\n\n"

                        # Handle string chunks
                        else:
                            content_buffer += str(chunk)
                            llm_response['content'] = content_buffer
                            yield f"data: {json.dumps({'content': content_buffer})}\n\n"

                    except Exception as e:
                        print(f"DEBUG - Error in streaming loop: {str(e)}")
                        yield f"data: {json.dumps({'error': str(e)})}\n\n"

                # Stream is complete
                llm_response['is_stream_complete'] = True
                llm_response['timestamp'] = generate_timestamp()

                discussion = await self.node_db.get_node_by_id(self.context.discussion_id)
                if not discussion['properties'].get('Title'):
                    await self.utils._ask_for_title(self.context.discussion_id,messages.copy())

                messages.append(llm_response)
                discussion['properties']['Discussion'] = json.dumps(messages)
                await self.node_db.update_node_by_id(self.context.discussion_id,discussion['properties'])

                self.discussion_so_far = messages
                # Update discussion node
                formatted_output_text = f"LLM response:\n {content_buffer}"
                await self.update_discussion_node(
                    self.context.discussion_id,
                    formatted_output_text=formatted_output_text,
                    updated_discussion=[self.serialize_message(msg) for msg in self.discussion_so_far]
                )

                if is_new_discussion:
                    yield f"data: {json.dumps({'discussion_id': self.context.discussion_id})}\n\n"

            except Exception as e:
                error_msg = f"Error in code query discussion: {str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                yield f"data: {json.dumps({'error': error_msg})}\n\n"
                return

    async def update_discussion_node(self, discussion_id, formatted_output_text=None, updated_discussion=None,
                                     discussion_type=None, action=None, modifications=None, status=None,
                                     modifications_history=None):
        # Initialize an empty dictionary for the parameters to update
        update_params = {}

        # Check each parameter and add it to the dictionary if not None
        if formatted_output_text is not None:
            update_params['formatted_output_text'] = formatted_output_text

        if updated_discussion is not None:
            # Convert to JSON for storage as text, as the updated_discussion is in LLM messages format and is a list
            update_params['discussion_so_far'] = json.dumps(updated_discussion)

        if discussion_type is not None:
            update_params['discussion_type'] = discussion_type

        if action is not None:
            update_params['action'] = action
        # print(f'update_node {modifications}')
        if modifications is not None:
            update_params['modifications'] = json.dumps(modifications)

        if modifications_history is not None:
            update_params['modifications_history'] = json.dumps(
                modifications_history)

        if status is not None:
            update_params['status'] = status

        # Proceed with the update only if there are parameters to update
        if update_params:
            update_params['updated_at'] = generate_timestamp()
            await self.node_db.update_node_by_id(discussion_id, update_params, "Discussion")

        return

    def serialize_message(self,message):
        """Convert message objects to JSON serializable format"""
        if isinstance(message, dict):
            return {k: self.serialize_message(v) for k, v in message.items()}
        elif isinstance(message, list):
            return [self.serialize_message(item) for item in list(message)]
        elif hasattr(message, 'model_dump'):  # For Pydantic models
            return message.model_dump()
        elif hasattr(message, '__dict__'):  # For custom objects
            return {k: self.serialize_message(v) for k, v in message.__dict__.items()
                    if not k.startswith('_')}
        elif isinstance(message, (str, int, float, bool, type(None))):
            return message
        else:
            return str(message)  # Fallback for other types