#!/usr/bin/env python3
"""
supabase_bootstrap.py
Class-based wrapper for Supabase database bootstrap operations.
Initializes helper objects in your Supabase Postgres:
• Ensures the pgcrypto extension exists (needed for pgp_sym_encrypt / decrypt)
• Clears & re-creates public.run_sql(query text)

Usage:
    from supabase_bootstrap import SupabaseBootstrap
    
    bootstrap = SupabaseBootstrap(db_url="postgres://user:pass@host:port/db")
    success = bootstrap.setup()
    
    if success:
        print("Project ready for API-based access")
"""

import os
import sys
import textwrap
import psycopg2
from contextlib import contextmanager
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class BootstrapResult:
    """Result object for bootstrap operations"""
    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None


class SupabaseBootstrap:
    """
    Bootstrap class for setting up Supabase database utilities.
    Handles pgcrypto extension and run_sql() function installation.
    """
    
    def __init__(self, db_url: str, verbose: bool = True):
        """
        Initialize bootstrap with database connection.
        
        Args:
            db_url: PostgreSQL connection string (with admin privileges)
            verbose: Whether to print status messages
        """
        self.db_url = db_url
        self.verbose = verbose
        
        # SQL payloads
        self.DROP_RUN_SQL = "DROP FUNCTION IF EXISTS public.run_sql(text);"
        self.CREATE_RUN_SQL = textwrap.dedent(r"""
            CREATE OR REPLACE FUNCTION public.run_sql(query text)
            RETURNS jsonb
            LANGUAGE plpgsql
            SECURITY DEFINER
            SET search_path = public
            AS $$
            DECLARE
                clean text := regexp_replace(query, ';\\s*$', '');    -- strip trailing ';'
                result jsonb;
                produces_rows boolean :=
                     clean ~* '^\s*(select|with)\s'                   -- SELECT / WITH …
                  or clean ~* '\\breturning\\b';                      -- … or DML with RETURNING
            BEGIN
                IF produces_rows THEN
                    EXECUTE format(
                       'WITH _q AS (%s) SELECT coalesce(json_agg(_q), ''[]'') FROM _q',
                       clean
                    ) INTO result;
                    RETURN result;
                ELSE
                    EXECUTE clean;                                    -- DDL or DML w/o rows
                    RETURN 'null'::jsonb;
                END IF;
            END;
            $$;
        """)
    
    @contextmanager
    def _get_cursor(self):
        """Context manager for database connections with error handling."""
        conn = None
        try:
            conn = psycopg2.connect(self.db_url)
            with conn.cursor() as cur:
                yield cur
            conn.commit()
        except psycopg2.Error as e:
            if conn:
                conn.rollback()
            error_msg = f"PostgreSQL error: {e.pgerror or str(e).strip()}"
            if self.verbose:
                print(f"❌ {error_msg}")
            print(error_msg)
        finally:
            if conn:
                conn.close()
    
    def _log(self, message: str, is_error: bool = False):
        """Log message if verbose mode is enabled."""
        if self.verbose:
            prefix = "❌" if is_error else "✅"
            print(f"{prefix} {message}")
    
    def check_pgcrypto(self) -> bool:
        """Check if pgcrypto extension is installed."""
        try:
            with self._get_cursor() as cur:
                cur.execute(
                    "SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto' LIMIT 1;"
                )
                return cur.fetchone() is not None
        except Exception:
            return False
    
    def install_pgcrypto(self) -> BootstrapResult:
        """
        Install pgcrypto extension in public schema.
        Drops existing extension first if present.
        """
        try:
            # Check if already exists
            already_exists = self.check_pgcrypto()
            
            with self._get_cursor() as cur:
                if already_exists:
                    self._log("pgcrypto already present – dropping first")
                    cur.execute("DROP EXTENSION pgcrypto CASCADE;")
                
                self._log("Installing pgcrypto extension")
                cur.execute("CREATE EXTENSION pgcrypto WITH SCHEMA public;")
            
            self._log("pgcrypto installed successfully")
            return BootstrapResult(
                success=True,
                message="pgcrypto extension installed",
                details={"was_existing": already_exists}
            )
            
        except psycopg2.errors.InsufficientPrivilege as e:
            error_msg = (
                "Insufficient privileges to install pgcrypto. "
                "Run with supabase_admin role or equivalent."
            )
            self._log(error_msg, is_error=True)
            return BootstrapResult(
                success=False,
                message=error_msg,
                details={"pgerror": str(e.pgerror).strip()}
            )
        except Exception as e:
            error_msg = f"Failed to install pgcrypto: {str(e)}"
            self._log(error_msg, is_error=True)
            return BootstrapResult(success=False, message=error_msg)
    
    def check_run_sql(self) -> bool:
        """Check if public.run_sql function exists."""
        try:
            with self._get_cursor() as cur:
                cur.execute("""
                    SELECT 1
                    FROM pg_proc p
                    JOIN pg_namespace n ON n.oid = p.pronamespace
                    WHERE p.proname = 'run_sql' AND n.nspname = 'public'
                    LIMIT 1;
                """)
                return cur.fetchone() is not None
        except Exception:
            return False
    
    def drop_run_sql(self) -> BootstrapResult:
        """Drop existing run_sql function if it exists."""
        try:
            with self._get_cursor() as cur:
                cur.execute(self.DROP_RUN_SQL)
            
            self._log("run_sql() function dropped (if it existed)")
            return BootstrapResult(success=True, message="run_sql function dropped")
            
        except Exception as e:
            error_msg = f"Failed to drop run_sql function: {str(e)}"
            self._log(error_msg, is_error=True)
            return BootstrapResult(success=False, message=error_msg)
    
    def install_run_sql(self) -> BootstrapResult:
        """Install and verify run_sql function."""
        try:
            with self._get_cursor() as cur:
                cur.execute(self.CREATE_RUN_SQL)
            
            # Verify installation
            if self._verify_run_sql():
                self._log("run_sql() function installed and verified")
                return BootstrapResult(
                    success=True,
                    message="run_sql function installed and verified"
                )
            else:
                error_msg = "run_sql function installation failed verification"
                self._log(error_msg, is_error=True)
                return BootstrapResult(success=False, message=error_msg)
                
        except Exception as e:
            error_msg = f"Failed to install run_sql function: {str(e)}"
            self._log(error_msg, is_error=True)
            return BootstrapResult(success=False, message=error_msg)
    
    def _verify_run_sql(self) -> bool:
        """Verify run_sql function works with a test query."""
        try:
            if not self.check_run_sql():
                return False
            
            with self._get_cursor() as cur:
                cur.execute("SELECT public.run_sql('SELECT 1 as test');")
                result = cur.fetchone()
                return result is not None
                
        except Exception:
            return False
    
    def setup(self) -> BootstrapResult:
        """
        Complete bootstrap setup: install pgcrypto + run_sql function.
        
        Returns:
            BootstrapResult with overall success status and details
        """
        self._log("Starting Supabase bootstrap setup")
        
        results = []
        
        # Step 1: Install pgcrypto
        pgcrypto_result = self.install_pgcrypto()
        results.append(pgcrypto_result)
        
        if not pgcrypto_result.success:
            return BootstrapResult(
                success=False,
                message="Bootstrap failed at pgcrypto installation",
                details={"results": results}
            )
        
        # Step 2: Drop existing run_sql
        drop_result = self.drop_run_sql()
        results.append(drop_result)
        
        if not drop_result.success:
            return BootstrapResult(
                success=False,
                message="Bootstrap failed at run_sql cleanup",
                details={"results": results}
            )
        
        # Step 3: Install run_sql
        install_result = self.install_run_sql()
        results.append(install_result)
        
        if not install_result.success:
            return BootstrapResult(
                success=False,
                message="Bootstrap failed at run_sql installation",
                details={"results": results}
            )
        
        self._log("Bootstrap setup completed successfully")
        return BootstrapResult(
            success=True,
            message="Bootstrap setup completed successfully",
            details={"results": results}
        )
    
    def status(self) -> Dict[str, bool]:
        """Check current status of bootstrap components."""
        return {
            "pgcrypto_installed": self.check_pgcrypto(),
            "run_sql_installed": self.check_run_sql(),
            "run_sql_functional": self._verify_run_sql()
        }


# Convenience function for quick setup
def bootstrap_supabase_project(db_url: str, verbose: bool = True) -> BootstrapResult:
    """
    Convenience function to bootstrap a Supabase project.
    
    Args:
        db_url: PostgreSQL connection string with admin privileges
        verbose: Whether to print status messages
        
    Returns:
        BootstrapResult indicating success/failure
    """
    bootstrap = SupabaseBootstrap(db_url, verbose)
    return bootstrap.setup()

