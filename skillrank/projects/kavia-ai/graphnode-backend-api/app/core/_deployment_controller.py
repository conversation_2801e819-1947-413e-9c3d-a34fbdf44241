import os
import zipfile
import tempfile
import boto3
import uuid
from datetime import datetime
from pathlib import Path
from botocore.exceptions import ClientError
from app.core.Settings import settings
from app.core.websocket.client import WebSocketClient


def deploy_artifact(build_path, ws_client:WebSocketClient=None, data = {"id": uuid.uuid4()}):
    
    """
    Zip and upload files inside the build path to S3.
    
    Args:
        build_path (str): Path to the directory containing build artifacts
        
    Returns:
        dict: Information about the uploaded artifact including:
            - success (bool): Whether the upload was successful
            - artifact_url (str): S3 URL of the uploaded artifact (if successful)
            - error (str): Error message (if unsuccessful)


    - deployment_id (str, optional): Unique identifier for the deployment
    - ws_client (WebSocketClient, optional): WebSocket client for sending status updates
    - data (dict, optional): Additional deployment data with structure:
        {
            "id": str,                   # Deployment ID
            "deployment_type": str,      # Type of deployment (e.g., "react_app")
            "command": str,              # Build command that was executed
            "root_path": str,            # Root path of the project
            "status": str,               # Status of the build process
            "build_path": str,           # Path to the build artifacts
            "levels": list,              # List of deployment levels ["build", "deploy"]
            "level": str                 # Current deployment level ("build" or "deploy")
        }

    """
    temp_zip_path = None
    try:
        # Validate build path
        build_dir = Path(build_path)
        deployment_id = data["id"]
        
        # Ensure levels and level fields are present in data
        if "levels" not in data:
            data["levels"] = ["build", "deploy"]
        if "level" not in data:
            data["level"] = "deploy"
            
        # Validate level is in levels
        if data["level"] not in data["levels"]:
            error_msg = f"Invalid level '{data['level']}'. Must be one of: {', '.join(data['levels'])}"
            if ws_client:
                data["status"] = "failed"
                data["error"] = error_msg
                ws_client.send_message("deployment_update", data)
            return {
                "success": False,
                "error": error_msg
            }
            
        if not build_dir.exists() or not build_dir.is_dir():
            error_msg = f"Build path '{build_path}' does not exist or is not a directory"
            if ws_client:
                data["status"] = "failed"
                data["error"] = error_msg
                ws_client.send_message("deployment_update", data)
            return {
                "success": False,
                "error": error_msg
            }
            
        # Validate AWS credentials
        try:
            s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION
            )
            # Test credentials with a simple operation
            s3_client.list_buckets()
        except ClientError as e:
            error_msg = f"Invalid AWS credentials: {str(e)}"
            if ws_client:
                data["status"] = "failed"
                data["error"] = error_msg
                ws_client.send_message("deployment_update", data)
            return {
                "success": False,
                "error": error_msg
            }
            
        # Create a temporary file for the zip
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            temp_zip_path = temp_file.name
            
        # Create a zip file containing all files in the build directory
        with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(build_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Store with relative path inside the zip
                    arcname = os.path.relpath(file_path, build_path)
                    zipf.write(file_path, arcname)
        
        # Generate a unique artifact name
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        artifact_name = f"build-{timestamp}-{str(uuid.uuid4())[:8]}.zip"
        
        # Get bucket name from environment or use default
        bucket_name = settings.S3_BUILD_ARTIFACTS_BUCKET
        
        # Upload the zip file to S3
        s3_client.upload_file(
            temp_zip_path, 
            bucket_name, 
            f"artifacts/{artifact_name}"
        )
        
        # Generate the S3 URL for the uploaded artifact
        artifact_url = f"s3://{bucket_name}/artifacts/{artifact_name}"
        
        # Update data with success information
        data["status"] = "success"
        data["artifact_url"] = artifact_url
        data["timestamp"] = timestamp
        
        if ws_client:
            ws_client.send_message("deployment_update", data)
            
        return {
            "success": True,
            "artifact_url": artifact_url,
            "artifact_name": artifact_name,
            "timestamp": timestamp,
            "level": data["level"]
        }
        
    except ClientError as e:
        error_msg = f"S3 client error: {str(e)}"
        if ws_client:
            data["status"] = "failed"
            data["error"] = error_msg
            ws_client.send_message("deployment_update", data)
        return {
            "success": False,
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"Failed to upload artifact: {str(e)}"
        if ws_client:
            data["status"] = "failed"
            data["error"] = error_msg
            ws_client.send_message("deployment_update", data)
        return {
            "success": False,
            "error": error_msg
        }
    finally:
        # Clean up the temporary zip file if it exists
        if temp_zip_path and os.path.exists(temp_zip_path):
            try:
                os.unlink(temp_zip_path)
            except Exception as e:
                print(f"Warning: Failed to delete temporary file {temp_zip_path}: {str(e)}")
