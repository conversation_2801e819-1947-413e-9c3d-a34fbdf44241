{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function with the following structure:

capture_discussion_output({
    "Modified-Node": {
        "Title": "Updated title if changed",
        "Description": "Updated description of the component or design",
        // Other fields as needed
    },
    // Additional fields based on the specific prompt and node type
})

Please ensure your response adheres to this function structure. Now, for your specific task:

Your task is to add more details to the description and update the title for a Task based on your knowledge base and the description provided here, and to provide all information needed for an engineer, a project manager, or an AI agent to implement the task. 
In addition to this, you should also create a comprehensive list of sub-tasks that are necessary to complete this task. The list of sub-tasks should cover all requirements for the task. 
The sub-tasks should be assigned to one of the AI agents or if the task is beyond the capabilities of an AI engineer, it should be assigned to a human engineer. 
If the sub-task requires multiple AI engineers or if it require breaking down further into more sub-tasks assign that sub-task to an AI Project Manager. 
Here are the list of AI agents that will be involved in the project:
{{ ai_agents | tojson }}
{% endblock %}
