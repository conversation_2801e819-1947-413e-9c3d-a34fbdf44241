from fastapi import HTTPException
import logging
import os
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from app.knowledge.redis_kg import getRedisKnowledge
from app.utils.kg_build.import_codebase import get_latest_commit_hash

class KnowlegeUpdate:
    
    def __init__(self, project_id, user_id=None):
        self.project_id = project_id
        self.mongo_handler  = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id=user_id
        )
        
    async def validate_github_access(self, git_url, branch_name, github_token=None):
        """
        Validates GitHub access before attempting to get the latest commit hash.
        Checks for rate limits and other common API restrictions.
        
        Args:
            git_url: GitHub repository URL
            branch_name: Name of the branch to check
            github_token: Optional GitHub authentication token
        
        Returns:
            tuple: (is_valid, error_message)
                is_valid: Boolean indicating if GitHub can be accessed
                error_message: String with error details if is_valid is False, None otherwise
        """
        try:
            # Extract owner and repo from the git URL
            if not git_url.startswith("https://github.com/"):
                return True, None  # Skip validation for non-GitHub URLs
                
            repo_path = git_url.replace("https://github.com/", "").replace(".git", "")
            parts = repo_path.split('/')
            if len(parts) < 2:
                return False, "Invalid GitHub URL format"
                
            owner, repo = parts[0], parts[1]
            
            # Check rate limits first using requests
            import requests
            
            headers = {}
            if github_token:
                headers["Authorization"] = f"token {github_token}"
                
            # Check rate limit endpoint
            rate_limit_url = "https://api.github.com/rate_limit"
            response = requests.get(rate_limit_url, headers=headers, timeout=5)
            
            # If already rate limited, detect it right away
            if response.status_code == 403 and "rate limit exceeded" in response.text.lower():
                return False, "GitHub API rate limit exceeded"
            
            # Check remaining rate limit if response is OK
            if response.status_code == 200:
                data = response.json()
                core_remaining = data.get("resources", {}).get("core", {}).get("remaining", 0)
                
                if core_remaining < 5:  # Keep some buffer
                    return False, "GitHub API rate limit nearly exceeded"
            
            # Make a lightweight check to the repo
            repo_url = f"https://api.github.com/repos/{owner}/{repo}"
            response = requests.head(repo_url, headers=headers, timeout=5)
            
            if response.status_code == 404:
                return False, "Repository not found"
            elif response.status_code == 403:
                if "rate limit exceeded" in response.headers.get("X-RateLimit-Resource", "").lower():
                    return False, "GitHub API rate limit exceeded"
                return False, "Access forbidden"
            elif response.status_code == 401:
                return False, "Authentication failed"
            elif response.status_code >= 400:
                return False, f"GitHub API error: HTTP {response.status_code}"
                
            return True, None
            
        except requests.Timeout:
            return False, "GitHub API request timed out"
        except requests.ConnectionError:
            return False, "Network connection error"
        except Exception as e:
            return False, f"GitHub validation error: {str(e)}"
    
    async def update_sync(self, build_id, rebuild_msg='', strict=False):
        
        self.build_id = build_id

        filter = {
            "project_id": self.project_id,
            "repositories.branches.builds.build_id": self.build_id
        }

        if strict:
            update = {
                "repositories.$[repo].branches.$[branch].upstream": True,
                "repositories.$[repo].branches.$[branch].builds.kg_creation_status": 3,
                "repositories.$[repo].branches.$[branch].rebuild_msg": rebuild_msg
            }
        else:
            update = {
                "repositories.$[repo].branches.$[branch].upstream": True,
            }

        array_filters = [
            {"repo.branches.builds.build_id": self.build_id},
            {"branch.builds.build_id": self.build_id}
        ]

        result = await self.mongo_handler.update_with_nested_object_and_filters(
            filter=filter,
            update=update,
            array_filters=array_filters
        )

        print(f"Modified {result.modified_count} documents")
            
    async def detect_sync(self, user_id):
        
        print("PRoject ID: ", self.project_id)
        
        cwd = os.getcwd() #used to get current cwd to revert it back to this dir later

        _result = await self.mongo_handler.get_one(
            filter={
                'project_id': int(self.project_id),
            },
            db=self.mongo_handler.db
        )
            
        if not _result:
            raise HTTPException(status_code=404, detail="No repository is found")
        
        repositories = _result.get('repositories', [])
        repositories = [repo for repo in repositories if repo.get('associated') is True]
        
        for repo_index, repo in enumerate(repositories):
            repo_name = repo.get('repository_name', '')
            git_url = repo.get('git_url')
            repo_type = repo.get('repo_type')

            for branch_index, branch in enumerate(repo.get('branches', [])):
                print("Checking...")
                branch_name = branch.get('name')
                
                # Get the build_id for this branch
                build_id = repositories[repo_index]['branches'][branch_index]['builds'].get('build_id')
                
                # Skip if kg_creation_status is -1
                if repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] in [1, -1, 3]:
                    print(f"Skipped sync detection for Repo {repo_name} because the status is {repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status']}")
                    continue
                
                # Check if repo_path exists
                repo_path = branch.get('builds', {}).get('path')
                
                print(repo_path)
                rebuild_needed = False
                
                if repo_path:
                    # knowledge check 
                    knowledge_path = os.path.join(repo_path, '.knowledge')
                                        
                    redis_kg = getRedisKnowledge(id=[build_id])
                                    
                    if redis_kg is False:
                        rebuild_needed = True
                        rebuild_msg = "Redis Knowledge Not found"
                    
                    elif os.path.exists(knowledge_path):
                        # vector knowledge check 
                        vector_db_path = os.path.join(knowledge_path, '.vector_db')
                        if not os.path.exists(vector_db_path):
                            rebuild_needed = True
                            rebuild_msg = ".vector_db folder not found"
                    else:
                        rebuild_needed = True
                        rebuild_msg = ".knowledge folder not found"
                
                # Add rebuild flag if needed
                if rebuild_needed and repositories[repo_index]['branches'][branch_index]['builds']['kg_creation_status'] != 1:
                    print(rebuild_msg)
                    await self.update_sync(build_id, rebuild_msg, strict=True)
                    
                    
                user_github = get_mongo_db(
                    db_name=settings.MONGO_DB_NAME,
                    collection_name='users_github',
                    user_id = user_id
                )
                github_token = None
                try:
                    user_data = await user_github.git_get_by_user_id(branch.get('builds', {}).get('user_id'))
                    if user_data:
                        github_token = user_data["access_token"]
                except Exception as e:
                    logging.info("Failed to fetch the github token")
                        
                try:         
                    os.chdir(repo_path)        
                    # Get list of commits using git log
                    command = f"git log {branch_name} --format=%H"
                    git_result = os.popen(command).read().strip()
                    
                    # Split into list of commit hashes
                    local_commits = git_result.split('\n') if git_result else []

                    # Validate GitHub access before attempting to get commit hash
                    is_valid, error_message = await self.validate_github_access(
                        git_url=git_url,
                        branch_name=branch_name,
                        github_token=github_token if repo_type == 'private' else None
                    )
                    
                    if not is_valid:
                        logging.warning(f"Skipping GitHub check for {git_url} branch {branch_name}: {error_message}")
                    
                    try:
                        remote_latest_commit = get_latest_commit_hash(
                                    git_url=git_url,
                                    branch_name=branch['name'],
                                    github_token=github_token if repo_type == 'private' else settings.GITHUB_ACCESS_TOKEN #fallback to KAVIA github token in case of public repo to avoid rate limit
                                )
                        
                        if remote_latest_commit and local_commits:
                            # Check if remote commit exists in local commits
                            is_up_to_date = remote_latest_commit in local_commits
                            if not is_up_to_date and build_id:
                                print("Sync Detected")
                                await self.update_sync(build_id)
                            
                    except Exception as e:
                        logging.error(f"Error checking upstream for {git_url} branch {branch['name']}: {str(e)}")
                      
                            
                except Exception as e:
                    logging.error(f"Error checking commit hash for {git_url} branch {branch['name']}: {str(e)}")
                
                finally:
                    os.chdir(cwd)
                    