from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database
from typing import Dict, Optional
from app.models.user_model import ModuleConfig
import json

class ConfigRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('config')
        self.collection: str = "config"

    async def get_module_configuration(self, module_name: str, user_id: str, db: Database) -> Dict:
        """Get the module configuration for a user."""
        try:
            query = {
                "user_id": user_id,
                f"user_config.modules.{module_name}": {"$exists": True}
            }
            projection = {
                "_id": 0,
                f"user_config.modules.{module_name}": 1
            }
            
            collection = await self.get_collection(db)
            module_config = await collection.find_one(query, projection)
            
            if not module_config:
                return json.loads(ModuleConfig().model_dump_json())
                
            return module_config["user_config"]["modules"][module_name]
            
        except Exception as e:
            print("Error getting module configuration: ", e)
            return {}

class LLMCostsRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('llm_costs')
        self.collection: str = "llm_costs"

    async def get_llm_costs(self, user_id: Optional[str], db: Database):
        """Retrieve LLM costs for a given user_id."""
        try:
            collection = await self.get_collection(db)
            if user_id:
                document = await collection.find_one({"user_id": user_id})
                if document:
                    document["_id"] = str(document["_id"])
                    return document
                return None
            else:
                cursor = collection.find({})
                result = await cursor.to_list(length=None)
                if result:
                    for doc in result:
                        doc["_id"] = str(doc["_id"])
                    return result
                return None
        except Exception as e:
            print(f"Error in get_llm_costs: {str(e)}")
            return None

class CodeGenTasksRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('code_gen_tasks')
        self.collection: str = "code_gen_tasks"

    async def find_code_gen_tasks_credits(self, project_id: int, user_id: str, db: Database) -> float:
        """Find code generation tasks and calculate total credits."""
        try:
            query = {
                "project_id": project_id,
                "user_id": user_id
            }
            
            collection = await self.get_collection(db)
            tasks = await collection.find(query).to_list(length=None)
            
            if not tasks:
                return 0
            
            total_credits = 0
            for task in tasks:
                if 'total_cost' in task:
                    try:
                        total_credits += float(task['total_cost']) * 10
                    except (ValueError, TypeError):
                        print(f"Invalid total_cost format in task: {task['total_cost']}")
            
            return total_credits
            
        except Exception as e:
            print(f"Error in find_code_gen_tasks: {str(e)}")
            return 0