from fastapi.responses import StreamingResponse , JSONResponse

from app.connection.establish_db_connection import get_node_db, get_vector_db , get_mongo_db
from app.connection.llm_init import get_llm_interface

from fastapi import APIRouter, Depends, Body , Query, Request, responses, HTTPException, status
from app.models.node_model import ConfigureNodeRequest

import asyncio
import json
import time
import uuid
from datetime import datetime

import traceback
import logging
import pkgutil
import importlib
import inspect
from app.utils.node_utils import get_node_type
from app.discussions.discussion_factory import DiscussionFactory
from app.models.configure_model import get_config_options
# Discover all classes in the 'app.discussions.types' package
# Import the package correctly. Assuming 'discussions.types' is within a parent package 'discussions'.
package = importlib.import_module("app.discussions.types")

for loader, module_name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + '.'):
    # Ensure the imported modules are correctly referenced with their full package names
    importlib.import_module(module_name)
# Discover and register all classes in the 'app.discussions.types' package


tasks = {}

_SHOW_NAME = "configure"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

@router.get("/options/{node_type}")
async def get_available_options(
    node_type: str,
):
    try:
        config_options = get_config_options(node_type)
        return config_options
    except ValueError as e:  # Catch invalid NodeType values
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:  # Catch any unexpected errors
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )  


@router.post("/cancel_task/{task_id}")
async def cancel_task(task_id: str, mongo_db = Depends(get_mongo_db)):
    print('Cancelling task:', task_id)
    result = await mongo_db.update_by_task_id(task_id, {'cancel': True}, mongo_db.db)
    result = str(result)
    if result:
        return JSONResponse({'message': f'Task cancellation initiated for {task_id}'}, status_code=200)
    else:
        if task_id in tasks:
            tasks[task_id]['cancel'] = True  # Signal cancellation
            return JSONResponse({'message': f'Task cancellation initiated for {task_id}'}, status_code=200)
    raise HTTPException(status_code=404, detail='Task not found')


@router.get("/current_active_task/{node_id}")
async def current_active_task(node_id: int, mongo_db = Depends(get_mongo_db)):
    active_task = await mongo_db.get_active_task(node_id)
    if active_task:
        active_task = dict(active_task)
    return active_task


@router.get("/options/{node_type}")
async def get_configuration_options(node_type: str, node_db = Depends(get_node_db)):
    try:
        options = await node_db.get_configuration_options(node_type)
        return options
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))