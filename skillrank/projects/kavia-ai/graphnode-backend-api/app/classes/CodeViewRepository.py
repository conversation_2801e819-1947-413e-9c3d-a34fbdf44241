
from app.classes.Ec2Repository import Ec2Repository
import asyncio,json

class CodeViewRepository():
    def __init__(self, helper_repo:Ec2Repository):
        self.helper_repo = helper_repo

    def get_instance_status(self,user_name,project_name):
        instance_name = f"{user_name}-{project_name}"
        instances = self.helper_repo.describe_instances_by_tag(None,'tag:Name',[instance_name])
        return {
            "status": "notfound" if not instances['Reservations'] else instances['Reservations'][0]["Instances"][0]["State"]["Name"],
            "url":  "notfound" if not instances['Reservations'] else f'https://{instances["Reservations"][0]["Instances"][0]["PublicIpAddress"]}.nip.io/' if instances['Reservations'][0]["Instances"][0]["State"]["Name"] == "running" else None,
        }

    def get_instance_ip(self,project_obj,project_name):
        instance_name = f"{project_obj['user_name']}-{project_name}"
        instances = self.helper_repo.describe_instances_by_tag(None,'tag:Name',[instance_name])
        if not instances['Reservations']:
            instance = self.helper_repo.create_instances(instance_name,project_obj)["Instances"][0]
            instance = self.helper_repo.wait_until_instance_created(instance["InstanceId"])
            print(instance["InstanceId"])
            cloudwatch_response = self.helper_repo.create_cpu_alarm(instance["InstanceId"])
            print("cloudwatch response: ",cloudwatch_response)
            return {
                "status": instance["State"]["Name"],
                "ip": instance["PublicIpAddress"],
                "url": f'https://{instance["PublicIpAddress"]}.nip.io/?folder=/home/<USER>/'+project_obj['repo_name']
            }
        elif instances['Reservations'][0]["Instances"][0]["State"]["Name"] == "running":
            instance = instances["Reservations"][0]["Instances"][0]
            return {
                "status": instance["State"]["Name"],
                "ip": instance["PublicIpAddress"],
                "url": f'https://{instance["PublicIpAddress"]}.nip.io/?folder=/home/<USER>/'+project_obj['repo_name']
            }
        else:
            instance_id = instances["Reservations"][0]["Instances"][0]["InstanceId"]
            self.helper_repo.start_instances(instance_id)
            instance = self.helper_repo.wait_until_instance_created(instance_id)
            return {
                "status": instance["State"]["Name"],
                "ip": instance["PublicIpAddress"],
                "url": f'https://{instance["PublicIpAddress"]}.nip.io/?folder=/home/<USER>/'+project_obj['repo_name']
            }
        

    async def get_instance_ip_stream(self,project_obj,project_name):
        yield {"message":"Finding Workspace!","error":False,"end":False}
        instance_name = f"{project_obj['user_name']}-{project_name}"
        instances = self.helper_repo.describe_instances_by_tag(None,'tag:Name',[instance_name])
        if not instances['Reservations']:
            yield {"message":"Workspace not found!","error":False,"end":False}
            await asyncio.sleep(2)
            instance = self.helper_repo.create_instances(instance_name,project_obj)["Instances"][0]
            yield {"message":"Creating workspace! Lets wait for 40 seconds","error":False,"end":True,"wait":40}
            # async_generator = self.helper_repo.wait_until_instance_created_stream(instance["InstanceId"])
            # async for message in async_generator:
            #     if not message['end']:
            #         yield message
            #         # await asyncio.sleep(2)
            #     else:
            #         instance = message['response']
            # yield {"message":"Your lab is on the way!","error":False,"end":False}
            # self.helper_repo.create_cpu_alarm(instance["InstanceId"])
            # # print("cloudwatch response: ",cloudwatch_response)
            # yield {
            #     "status": instance["State"]["Name"],
            #     "ip": instance["PublicIpAddress"],
            #     "url": f'https://{instance["PublicIpAddress"]}.nip.io/?folder=/home/<USER>/'+project_obj['repo_name'],
            #     "message":"Your new Workspace!",
            #     "error":False,
            #     "end":True
            # }
        elif instances['Reservations'][0]["Instances"][0]["State"]["Name"] == "running":
            instance = instances["Reservations"][0]["Instances"][0]
            if not self.helper_repo.check_cloudwatch_alarm_for_instance(instance["InstanceId"]):
                yield {"message":"Initializing Cloud Watch","error":False,"end":False}
                self.helper_repo.create_cpu_alarm(instance["InstanceId"])
                await asyncio.sleep(2)
                yield {"message":"Done","error":False,"end":False}
            yield {
                "status": instance["State"]["Name"],
                "ip": instance["PublicIpAddress"],
                "url": f'https://{instance["PublicIpAddress"]}.nip.io/?folder=/home/<USER>/'+project_obj['repo_name'],
                "message":"Your Workspace here!",
                "error":False,
                "end":True
            }
        elif instances['Reservations'][0]["Instances"][0]["State"]["Name"] == "stopped":
            yield {"message":"Starting the instance...","error":False,"end":False}
            instance = instances["Reservations"][0]["Instances"][0]
            instance_id = instances["Reservations"][0]["Instances"][0]["InstanceId"]
            if not self.helper_repo.check_cloudwatch_alarm_for_instance(instance["InstanceId"]):
                yield {"message":"Initializing Cloud Watch","error":False,"end":False}
                self.helper_repo.create_cpu_alarm(instance["InstanceId"])
                await asyncio.sleep(2)
                yield {"message":"Done","error":False,"end":False}
            self.helper_repo.start_instances(instance_id)
            yield {"message":"Workspace is starting, please wait for 30 seconds...","error":False,"end":True,"wait":30}
            # instance = self.helper_repo.wait_until_instance_created(instance_id,old_instance=True)
            # yield {
            #     "status": instance["State"]["Name"],
            #     "ip": instance["PublicIpAddress"],
            #     "url": f'https://{instance["PublicIpAddress"]}.nip.io/?folder=/home/<USER>/'+project_obj['repo_name'],
            #     "message":"Your Workspace waked up!",
            #     "error":False,
            #     "end":True
            # }
        else:
            yield {
                "message":"Please wait for 1 minute until the workspace has completely stopped before starting!.",
                "error":True,
                "end":True
            }

    def stop_instance(self,user_name,project_name):
        instance_name = f"{user_name}-{project_name}"
        instances = self.helper_repo.describe_instances_by_tag(None,'tag:Name',[instance_name])
        instance_id = instances["Reservations"][0]["Instances"][0]["InstanceId"]
        return self.helper_repo.stop_instances(instance_id)

    def terminate_instance(self,user_name,project_name):
        instance_name = f"{user_name}-{project_name}"
        instances = self.helper_repo.describe_instances_by_tag(None,'tag:Name',[instance_name])
        instance_id = instances["Reservations"][0]["Instances"][0]["InstanceId"]
        return self.helper_repo.terminate_instance(instance_id)