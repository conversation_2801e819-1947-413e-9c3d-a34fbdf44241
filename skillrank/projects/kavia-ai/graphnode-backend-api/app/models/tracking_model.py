# app/models/tracking_models.py
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
import uuid

class APITrackingLog(BaseModel):
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    date_partition: str = Field(default_factory=lambda: datetime.now(timezone.utc).strftime("%Y-%m-%d"))
    hour_partition: int = Field(default_factory=lambda: datetime.now(timezone.utc).hour)
    
    # Tenant & User information
    tenant_id: str
    tenant_name: Optional[str] = None
    user_id: str
    user_email: str
    username: str
    user_role: Optional[str] = "user"
    
    # Request details
    method: str
    endpoint: str
    full_url: str
    status_code: int
    duration_ms: float
    
    # Response information
    response_size_bytes: int = 0
    response_type: str = "success"  # success, error, timeout
    
    # Client information
    client_ip: str
    user_agent: str = ""
    device_type: str = "desktop"  # mobile, desktop, tablet
    browser: str = "unknown"
    
    # Location information (NEW)
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    timezone_name: Optional[str] = None
    isp: Optional[str] = None
    
    # Authentication
    auth_method: str = "bearer"  # bearer, none, api_key
    has_valid_token: bool = True
    
    # Business context
    project_id: Optional[str] = None
    resource_type: Optional[str] = None
    action_type: str = "read"  # create, read, update, delete, list
    
    # Additional data
    custom_headers: Dict[str, str] = Field(default_factory=dict)
    query_params: Dict[str, str] = Field(default_factory=dict)
    
    # Tags for categorization
    tags: List[str] = Field(default_factory=list)
    
    def dict(self, **kwargs):
        """Convert to dict for MongoDB storage"""
        data = super().dict(**kwargs)
        # Ensure datetime is properly serialized
        if isinstance(data.get('created_at'), datetime):
            data['created_at'] = data['created_at']
        return data

class DailyTenantStats(BaseModel):
    tenant_id: str
    date: str
    total_requests: int = 0
    unique_users: int = 0
    avg_response_time_ms: float = 0
    total_errors: int = 0
    error_rate: float = 0
    total_data_transferred_mb: float = 0
    
    # Geographic stats (NEW)
    top_countries: List[Dict[str, Any]] = Field(default_factory=list)
    top_regions: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Hourly breakdown
    hourly_breakdown: Dict[str, int] = Field(default_factory=dict)
    
    # Status codes
    status_codes: Dict[str, int] = Field(default_factory=dict)
    
    # Top endpoints
    top_endpoints: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Active users list
    active_users: List[str] = Field(default_factory=list)
    
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))