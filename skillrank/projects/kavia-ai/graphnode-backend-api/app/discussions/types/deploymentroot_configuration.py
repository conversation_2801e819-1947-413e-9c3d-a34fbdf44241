from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.connection.establish_db_connection import get_mongo_db
from app.core.function_schema_generator import get_function_schema
from datetime import datetime
import os 
# from app.discussions.types.component_form_generator import ComponentFormGenerator
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.connection.establish_db_connection import get_mongo_db
from app.utils.logs_utils import get_path

class DeploymentRootConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Project")
        self.template_name = "deploymentroot_configuration.prompt"
        self.function_schema_type = "DeploymentRootConfiguration"
        self.deployment_root_node = None
        self.node_id = node_id

    def determine_component_type(self, work_item):
        """Analyze component to determine its type."""
        description = work_item.get("description", "").lower()
        
        if any(key in description for key in ["ui", "frontend", "interface", "react", "vue", "angular"]):
            return "frontend"
        
        if any(key in description for key in ["database", "storage", "mongodb", "postgresql", "mysql"]):
            return "database"
        
        return "backend"

    async def generate_form_schema(self, context):
        """Generate form schema using LLM."""
        self.llm = LLMInterface(get_path(), 'discussion', self.current_user, self.node_id, self.discussion_type, mongo_handler = get_mongo_db())
        try:
            base_path = os.path.dirname(os.path.abspath(__file__))
            template_path = os.path.join(base_path, 'discussions', 'prompt_templates')
            env = Environment(loader=FileSystemLoader(template_path))
            template = env.get_template("generate_form_schema.prompt")
            
            # Load work item details
            with open('app/discussions/prompt_templates/work_item_sample.json', 'r') as file:
                work_item = json.load(file)
                self.update_logger.info("Successfully loaded work_item_sample.json")

            # Determine component type
            component_type = self.determine_component_type(work_item)
            
            # Prepare context for form schema generation
            context = {
                "details_for_discussion": {
                    "component_name": work_item.get("component_name", ""),
                    "description": work_item.get("description", ""),
                    "component_type": component_type,
                    "current_node": self.retrieved_info["current_node"]["labels"],
                },
                "prompt_type": "user",
                "discussion_type": self.discussion_type,
                "node_type": self.node_type
            }

            user_prompt = template.render(context)
            
            response = await self.llm.llm_interaction_wrapper(
                messages=[],
                user_prompt=user_prompt,
                system_prompt="You are an expert deployment engineer. Generate a detailed form schema for component deployment configuration.",
                response_format={'type': 'json_object'},
                model=self.model_name,
                stream=False
            )
            
            return response

        except Exception as e:
            self.update_logger.error(f"Error generating schema from LLM: {e}")
            raise

    async def retrieve_info(self):
        """Collect and store deployment configuration information."""
        await super().retrieve_info()

        try:
        
            # if self.invocation_type == "interactive_config":
            #     form_schema = await self.generate_form_schema()
            # else:
            #     # Generate form schema
            #     form_schema = await self.generate_form_schema()
            # # Store in retrieved_info
            # self.retrieved_info.update({
            #     'deployment_form': form_schema
            # })

                # Create a new deployment attempt node
            deployment_attempt_properties = {
                "Title": f"Deployment Attempt {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "Description": "Infrastructure deployment configuration",
                "Status": "pending",
                "CreatedAt": datetime.now().isoformat(),
                "Type": "DeploymentAttempt"
            }

            # Create the deployment attempt node
            self.deployment_attempt_node = await self.db.create_node(
                ["DeploymentAttempt"],
                deployment_attempt_properties,
                self.node_id  # Use the deployment root node ID as parent
            )

            # Update node_id to the new deployment attempt node
            self.node_id = self.deployment_attempt_node['id']

        except Exception as e:
            self.update_logger.error(f"Error in retrieve_info: {e}")
            raise

        return self.retrieved_info
    

    def get_modifications_from_llm_output(self):
        self.modifications['modified_node']['Type'] = 'DeploymentRoot'

        
        return self.modifications

   

# Register the subclass in the factory
DiscussionFactory.register('configuration', DeploymentRootConfiguration, "DeploymentRoot")