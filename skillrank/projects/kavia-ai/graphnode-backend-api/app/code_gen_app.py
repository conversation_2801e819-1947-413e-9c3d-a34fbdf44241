from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from app.routes import code_gen
from app.routes.code_gen import start_session_monitor
from app.connection.establish_db_connection import connect_node_db, connect_vector_db, get_mongo_db
from app.connection.tenant_middleware import TenantMiddleware, tenant_context
from app.repository.mongodb.client import connect_db
import logging
from contextlib import asynccontextmanager
import threading
import os
import subprocess
import shutil

def configure_git():
    """Configure git user details for the application."""
    os.environ["GIT_AUTHOR_NAME"] = "Kavia AI"
    os.environ["GIT_AUTHOR_EMAIL"] = "<EMAIL>"
    subprocess.run(["git", "config", "--global", "user.name", os.environ["GIT_AUTHOR_NAME"]])
    subprocess.run(["git", "config", "--global", "user.email", os.environ["GIT_AUTHOR_EMAIL"]])

def replace_docker_executor():
    """Replace the internal Docker executor with our enhanced version."""
    try:
        # Paths
        internal_path = "/usr/local/lib/python3.11/site-packages/code_generation_core_agent/agents/tools/executor/docker_executor.py"
        custom_path = "/app/app/core/custom_docker_executor.py"
        backup_path = f"{internal_path}.backup"
        
        # Check if custom file exists
        if not os.path.exists(custom_path):
            logging.warning(f"Custom docker executor not found at: {custom_path}")
            return False
        
        # Check if internal file exists
        if not os.path.exists(internal_path):
            logging.warning(f"Internal docker executor not found at: {internal_path}")
            return False
        
        # Create backup if it doesn't exist
        if not os.path.exists(backup_path):
            shutil.copy2(internal_path, backup_path)
            logging.info("Created backup of original docker_executor.py")
        else:
            logging.info("Backup already exists, skipping backup creation")
        
        # Replace the file
        shutil.copy2(custom_path, internal_path)
        logging.info("Successfully replaced internal docker_executor.py with enhanced version")
        
        # Verify replacement
        if os.path.exists(internal_path):
            file_size = os.path.getsize(internal_path)
            logging.info(f"Replacement verified - new file size: {file_size} bytes")
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to replace docker executor: {str(e)}")
        return False

def create_code_query_app():
    _app = FastAPI(title="Code Query API", lifespan=lifespan)
    
    origins = ['*']
    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    _app.add_middleware(TenantMiddleware)
    
    @_app.get("/", status_code=200)
    def health(__: Request):
        return {"message": "Hey!, Welcome To Kavia AI Code Gen Session..."}
    
    # Code query routes
    _app.include_router(
        code_gen.app,
        prefix="/api"
    )
    
    return _app

def get_complete_application():
    """In production need to start and close db connection and some middleware."""
    _app = create_code_query_app()
    _app.add_event_handler("startup", connect_db)
    _app.add_event_handler("startup", connect_node_db)
    _app.add_event_handler("startup", connect_vector_db)
    _app.add_event_handler("startup", lambda: get_mongo_db(collection_name='tasks'))
    return _app

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup event
    logging.info("Application starting up...")
    
    # Replace Docker executor FIRST (before any imports that might use it)
    logging.info("Replacing Docker executor with enhanced version...")
    replace_docker_executor()
    
    # Configure git
    configure_git()
    
    # Start the session monitor for auto-termination
    start_session_monitor(check_interval_minutes=0.1)  # Run every 6 seconds

    yield
    
    # Shutdown
    logging.info("Application shutting down...")

app = get_complete_application()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
    logging.info("Uvicorn server started")