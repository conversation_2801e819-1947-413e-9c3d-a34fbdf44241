{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function, Now, for your specific task:

Your task is to design a UML class diagram to show the structure and relationships between classes for a given component.
Use technical language where appropriate, but ensure the document is accessible to both technical and non-technical stakeholders.

Example mermaid chart snippet for a class diagram:

State Diagram:
<Mermaid-State>
    class ShoppingCart {
        -id: string
        -items: CartItem[]
        -user: User
        +addItem(item: CartItem)
        +removeItem(itemId: string)
        +calculateTotal(): number
        +checkout()
    }

    class CartItem {
        -id: string
        -quantity: number
        -product: Product
        +updateQuantity(newQuantity: number)
        +getSubtotal(): number
    }

    class Product {
        -id: string
        -name: string
        -price: number
        -description: string
        +getDetails(): string
    }

    class User {
        -id: string
        -name: string
        -email: string
        +updateProfile(name: string, email: string)
    }

    ShoppingCart "1" -- "0..*" CartItem : contains
    ShoppingCart "0..1" -- "1" User : belongs to
    CartItem "0..*" -- "1" Product : references    class Appointment {
        +DateTime date
        +String description
        +book()
        +cancel()
    }
    class User {
        +String name
        +String email
        +createAppointment()
    }
    Appointment ""0..*"" -- ""1"" User

{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
The current node is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The current node is a part of the following tree of nodes of information: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Here are all the interfaces that are defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{% endblock %}


{% block output_format %}
Please use the following JSON format for your JSON output:
{{output_format}}

JSON fields should contain just the content for that field and should not contain any other discussion or comments. 
You may use regular text for discussions and comments, but outside the <JSON> tags. 
{% endblock %}

{%block system_prompt %}
You are an expert software designer who has a deep understanding of software component design. You have several years of software development experience. 
You are engaging in a conversation with a software architect regarding the job of creating the detailed software design 
for a component. Output any comments to  the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}