from app.utils.aws.cognito_main import TenantService
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.utils.aws.cognito_group_manager import CognitoGroupManager
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import List, Dict, Optional
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from app.models.organization_models import Organization
import json
from botocore.exceptions import ClientError
from fastapi import Request
import urllib.parse
from app.utils.hash import encrypt_tenant_id

# Router setup
_SHOW_NAME = "Tenant Management"
router = APIRouter(
    prefix=f"/tenant_manager",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

class UserCreate(BaseModel):
    email: EmailStr
    temp_password: str = Field(
        default="Welcome1@2024",
        description="Temporary password for the user"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "temp_password": "Welcome1@2024"  # Optional, will use default if not provided
            }
        }

class GroupCreate(BaseModel):
    name: str
    permissions: Dict[str, Dict[str, bool]]

    class Config:
        schema_extra = {
            "example": {
                "name": "admins",
                "permissions": {
                    "users": {
                        "create": True,
                        "read": True,
                        "update": True,
                        "delete": True
                    },
                    "reports": {
                        "create": True,
                        "view": True,
                        "export": True
                    },
                    "settings": {
                        "modify": True
                    }
                }
            }
        }

class UpdateGroupPermissions(BaseModel):
    permissions: Dict[str, Dict[str, bool]]

    class Config:
        schema_extra = {
            "example": {
                "permissions": {
                    "users": {
                        "create": True,
                        "read": True,
                        "update": True,
                        "delete": True
                    },
                    "reports": {
                        "create": True,
                        "view": True,
                        "export": True
                    },
                    "settings": {
                        "modify": True
                    }
                }
            }
        }

# Initialize TenantService
tenant_service = TenantService()

@router.post("/tenant/{tenant_id}/create", status_code=201)
async def create_tenant(tenant_id: str):
    """
    Explicitly create a new tenant with its own Cognito user pool and client
    
    Args:
        tenant_id: The tenant identifier to create
        
    Returns:
        Dict containing the created user_pool_id and client_id
        
    Raises:
        HTTPException: If tenant creation fails or tenant already exists
    """
    try:
        # First check if tenant already exists
        try:
            await tenant_service.get_tenant_cred(tenant_id)
            raise HTTPException(
                status_code=409,
                detail=f"Tenant '{tenant_id}' already exists"
            )
        except Exception:
            # If get_tenant_cred fails, proceed with creation
            pass
        
        # Create new tenant credentials
        creds = tenant_service.create_tenant_cred(tenant_id)
        
        return JSONResponse(
            status_code=201,
            content={
                "message": f"Tenant '{tenant_id}' created successfully",
                "credentials": creds
            }
        )
    
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create tenant: {str(e)}"
        )
    
@router.post("/tenant/{tenant_id}/credentials")
async def get_tenant_credentials(tenant_id: str):
    """Get or create tenant credentials"""
    try:
        creds = await tenant_service.get_tenant_cred(tenant_id)
        return creds
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/tenant/{tenant_id}/group")
async def create_group(tenant_id: str, group_data: GroupCreate):
    """Create a new group with permissions"""
    try:
        group = tenant_service.create_group(
            tenant_id=tenant_id,
            group_name=group_data.name,
            permissions=group_data.permissions
        )
        return group
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenant/{tenant_id}/groups")
async def list_groups(tenant_id: str):
    """List all groups in the tenant"""
    try:
        creds = await tenant_service.get_tenant_cred(tenant_id)
        group_manager = CognitoGroupManager(creds['user_pool_id'])
        return group_manager.list_groups()
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenant/{tenant_id}/group/{group_name}/permissions")
async def get_group_permissions(tenant_id: str, group_name: str):
    """Get permissions for a specific group"""
    try:
        permissions = tenant_service.list_permissions(tenant_id, group_name)
        return permissions
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/tenant/{tenant_id}/group/{group_name}/permissions")
async def update_group_permissions(
    tenant_id: str, 
    group_name: str, 
    permissions: UpdateGroupPermissions
):
    """Update permissions for a group"""
    try:
        success = tenant_service.update_group_permissions(
            tenant_id=tenant_id,
            group_name=group_name,
            permissions=permissions.permissions
        )
        return {"success": success}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenant/{tenant_id}/permissions/batch")
async def get_batch_permissions(
    tenant_id: str,
    group_names: List[str] = Query(None)
):
    """Get combined permissions for multiple groups"""
    try:
        permissions = tenant_service.list_permissions_batch(tenant_id, group_names)
        return permissions
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/tenant/{tenant_id}/user")
async def create_user(tenant_id: str, user_data: UserCreate, request: Request):
    """
    Create a new user with email and optional temporary password
    
    Args:
        tenant_id: The tenant identifier
        user_data: User creation data containing email and optional temp_password
        request: FastAPI Request object to get the base URL
    """
    try:
        # Get tenant credentials
        creds = await tenant_service.get_tenant_cred(tenant_id)

        # Initialize CognitoManager with tenant credentials
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        # Get the base URL from the request - Ui Url
        tenant_data = await Organization.get(org_id=tenant_id)
        ui_url = request.headers.get('origin', str(request.base_url))
        set_password_url = f"{ui_url}/users/set_password?tenant_id={tenant_id}&email={urllib.parse.quote(user_data.email)}"
        login_url = f"{ui_url}/users/login?tenant_id={tenant_id}&email={urllib.parse.quote(user_data.email)}"

        # Create user with provided or default temporary password
        user_manager.create_user(
            email=user_data.email,
            temporary_password=user_data.temp_password,
            organization_name=tenant_data['name'],
            set_password_url=set_password_url,
            login_url=login_url
        )
        
        return {
            "message": "User created successfully",
            "email": user_data.email,
            "temporary_password": user_data.temp_password,
            "set_password_url": set_password_url,
            "login_url": login_url
        }
        
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', '')
        if error_code == 'UsernameExistsException':
            raise HTTPException(
                status_code=400,
                detail=f"User with email {user_data.email} already exists"
            )
        elif error_code == 'InvalidPasswordException':
            raise HTTPException(
                status_code=400,
                detail="Invalid password. Password must have at least 8 characters, "
                       "including uppercase and lowercase letters, numbers, and symbols."
            )
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/tenant/{tenant_id}/users")
async def list_users(tenant_id: str):
    """List all users in the tenant"""
    try:
        users = tenant_service.list_users(tenant_id)
        return users
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenant/{tenant_id}/group/{group_name}/users")
async def get_group_users(tenant_id: str, group_name: str):
    """Get all users in a group"""
    try:
        users = tenant_service.get_group_users(tenant_id, group_name)
        return users
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/tenant/{tenant_id}/user/{email}/groups")
async def get_user_groups(tenant_id: str, email: str):
    """Get all groups for a user"""
    try:
        groups = tenant_service.get_user_groups(tenant_id, email)
        return groups
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/tenant/{tenant_id}/group/{group_name}/user/{email}")
async def add_user_to_group(tenant_id: str, group_name: str, email: str):
    """Add a user to a group"""
    try:
        tenant_service.add_user_to_group(tenant_id, email, group_name)
        return {"message": "User added to group successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/tenant/{tenant_id}/group/{group_name}/user/{email}")
async def remove_user_from_group(tenant_id: str, group_name: str, email: str):
    """Remove a user from a group"""
    try:
        tenant_service.remove_user_from_group(tenant_id, email, group_name)
        return {"message": "User removed from group successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/tenant/{tenant_id}/user/{email}")
async def delete_user(tenant_id: str, email: str):
    """Delete a user"""
    try:
        creds = await tenant_service.get_tenant_cred(tenant_id)
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        user_manager.delete_user(email)
        return {"message": "User deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/tenant/{tenant_id}/group/{group_name}")
async def delete_group(tenant_id: str, group_name: str):
    """Delete a group"""
    try:
        tenant_service.delete_group(tenant_id, group_name)
        return {"message": "Group deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

