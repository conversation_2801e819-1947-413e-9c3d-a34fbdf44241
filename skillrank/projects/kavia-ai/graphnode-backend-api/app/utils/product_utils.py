from app.models.product_model import ProductListResponse

free_plan = ProductListResponse(
    product_id="free_plan",
    product_name="Free",
    product_description="Free Plan",
    price_id="free_plan",
    currency="USD",
    credits=50000,
    price="0",
    is_recurring=False,
    recurring_interval="month",
    recurring_interval_count=1
)


plan_to_filter = {
    "names":["Premium", "Teams"]
}

plan_to_exclude = {
    "names":["Kavia AI Workflow Manager"]
}

def format_price(unit_amount_decimal: str) -> str:
    return str(round(float(unit_amount_decimal)/100, 2))