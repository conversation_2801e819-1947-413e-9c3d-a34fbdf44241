import subprocess
import logging
from typing import Optional, Dict

class ScreenSessionManager:
    @staticmethod
    def is_running(session_name: str) -> bool:
        try:
            # Run screen -ls and capture output
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # More robust session detection using proper parsing
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            for line in session_lines:
                if f".{session_name}\t" in line and not line.endswith('(Dead)'):
                    logging.info(f"Screen session '{session_name}' is currently running.")
                    return True
            logging.info(f"No running session found for name: '{session_name}'")
            return False
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return False
            logging.error(f"Error checking screen sessions: {e.output}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error checking screen sessions: {str(e)}")
            return False

    @staticmethod
    def get_session_details(session_name: str) -> Dict[str, str]:
        """Get detailed information about a screen session"""
        try:
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # More robust session parsing
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            for line in session_lines:
                if f".{session_name}\t" in line:
                    parts = line.strip().split('\t')
                    session_id = parts[0].strip()
                    status = 'Detached'
                    if len(parts) > 2:
                        status_part = parts[-1].strip('()')
                        status = status_part if status_part != 'Detached' else 'Detached'
                    
                    return {
                        "session_id": session_id,
                        "name": session_name,
                        "status": status
                    }
            return {}
            
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return {}
            logging.error(f"Error getting session details: {e.output}")
            return {}
        except Exception as e:
            logging.error(f"Unexpected error getting session details: {str(e)}")
            return {}

    @staticmethod
    def stop_session(session_name: str) -> bool:
        """Stop a screen session safely"""
        try:
            if not ScreenSessionManager.is_running(session_name):
                return True
                
            # Try graceful quit first
            subprocess.run(
                ['screen', '-S', session_name, '-X', 'quit'],
                check=True,
                capture_output=True,
                text=True
            )
            
            # Verify session was stopped
            if ScreenSessionManager.is_running(session_name):
                # If still running, try force kill
                logging.warning(f"Session {session_name} still running after quit, attempting force kill")
                session_details = ScreenSessionManager.get_session_details(session_name)
                if session_details and "session_id" in session_details:
                    subprocess.run(
                        ['screen', '-S', session_details["session_id"], '-X', 'kill'],
                        check=True,
                        capture_output=True,
                        text=True
                    )
            
            return not ScreenSessionManager.is_running(session_name)
            
        except Exception as e:
            logging.error(f"Error stopping session {session_name}: {str(e)}")
            return False

