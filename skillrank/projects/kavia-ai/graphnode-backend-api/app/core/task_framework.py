import uuid
import json
from enum import Enum
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from app.core.constants import TaskStatus
from app.connection.tenant_middleware import tenant_context
from app.utils.async_utils import async_to_sync
from app.celery_app import user_context

tasks_collection_name = 'tf_tasks'

active_statuses_for_auto_configure = [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.PAUSED]


class Task:
    def __init__(self, type="auto-config"):
        self.task_id = f'task_{uuid.uuid4()}'
        self.status = TaskStatus.PENDING
        self.progress = 0
        self.root_task_id = self.task_id
        self.parent_task_id = None
        self.type = type
        self.approved_or_rejected = False

    @staticmethod
    def from_db_record(db_record):
        task = Task()
        task.task_id = db_record.get('_id')
        task.status = db_record.get('status')
        task.progress = db_record.get('progress')
        task.type = db_record.get('type', 'auto-config')
        task.parent_task_id = db_record.get('parent_task_id')
        task.root_task_id = db_record.get('root_task_id')
        if(db_record.get('type') == 're-config'):
            task.approved_or_rejected = db_record.get('approved_or_rejected', False)
        return task

    @staticmethod
    def get_by_id(task_id):
        db = get_mongo_db().db
        db_record = db[tasks_collection_name].find_one({ '_id': task_id })
        if not db_record:
            return None
        return Task.from_db_record(db_record)
    
    @staticmethod
    def get_pending_by_node_id(node_id):
        db = get_mongo_db().db
        db_record = db[tasks_collection_name].find_one({
        'node_id': node_id,
        'status': {'$in': active_statuses_for_auto_configure},

    })
        if not db_record:
            return None
        return Task.from_db_record(db_record)

    @staticmethod
    def get_pending_by_project_id(project_id):
        db = get_mongo_db().db
        db_record = db[tasks_collection_name].find_one({
        'project_id': project_id,
        'status': {'$in': active_statuses_for_auto_configure},

         })
        if not db_record:
            return None
        return Task.from_db_record(db_record)
    
    def cancel(self):
        from app.celery_app import celery_app as app

        self.report_status(TaskStatus.CANCELLED)
        app.control.revoke(self.task_id, terminate=True)

        if self.parent_task_id:
            parent_task = Task.get_by_id(self.parent_task_id)
            parent_task.cancel()
    
    def delete(self):
        db = get_mongo_db().db
        db[tasks_collection_name].delete_one({ '_id': self.task_id })

    def to_dict(self):
        return {
            'task_id': self.task_id,
            'status': self.status,
            'progress': self.progress,
            'parent_task_id': self.parent_task_id,
            'root_task_id': self.root_task_id,
            'type': self.type,
            'approved_or_rejected': self.approved_or_rejected
        }

    def to_json(self):
        return json.dumps(self.to_dict())

    @staticmethod
    def schedule_task(handler, *args, **kwargs):
        task = Task(kwargs.get('type'))
        task.dump_to_db()
        tenant_id = tenant_context.get()
        user_id = user_context.get()
        
        
        # Create new kwargs if none provided
        task_kwargs = kwargs if kwargs else {}
        # Explicitly add tenant_id to kwargs
        task_kwargs['tenant_id'] = tenant_id
        if user_id == 'admin' or None:
            user_id = task_kwargs.get('current_user') or "admin"
        print("TID", tenant_id)
        print("USER_ID", user_id)
        print("kwargs current_user", task_kwargs.get('current_user'))
        
        from app.celery_app import celery_app
        # Apply task with tenant_id in kwargs
        handler.apply_async(
            args=args, 
            task_id=task.task_id, 
            kwargs=task_kwargs
        )
        return task
    
    @staticmethod
    def update_task(task_id, update_dict):
        db = get_mongo_db().db
        db[tasks_collection_name].update_one({ '_id': task_id }, { '$set': update_dict })

        
    @staticmethod
    def get_detailed_report(task_id):
        db = get_mongo_db().db
        db_record = db[tasks_collection_name].find_one({ '_id': task_id},{ '_id': 0, 'terminal_output': 0 })
        if not db_record:
            return {}
        try:
            context = json.loads(db_record.get('context', "{}"))
        except:
            context = {}
        insigths = Task.process_context(context, task_id)
        db_record['context'] = context
        db_record['insights'] = insigths
        return db_record
    
    @staticmethod
    def process_context(context, task_id):
        try:
            task_count_map = {
                "total": 0,
                "completed": 0,
                "failed": 0,
                "in-progress": 0,
                "to-do": 0
                
            }
            task_list = context.get('task_list')   
            if not task_list:
                return task_count_map
                
            total_tasks = len(task_list)
            task_count_map["total"] = total_tasks
            if total_tasks == 0:
                return task_count_map
            
            for task in task_list:
                if task_count_map.get(task.get('status')) is None:
                    task_count_map[task.get('status')] = 1
                task_count_map[task.get('status')] += 1 
            if task_count_map["completed"] == 0:
                percentage = 0
            else:     
                percentage = (task_count_map["completed"] / total_tasks) * 100
            with TaskWrapper(task_id) as task:
                task.report_progress(percentage)
                if task_count_map["completed"] == total_tasks:
                    task.report_status("complete")
                else:
                    task.report_status("in_progress")
            return task_count_map
        except Exception as e:
            print(e)
            return None

    def report_status(self, status):
        self.status = status
        db = get_mongo_db().db
        db[tasks_collection_name].update_one({ '_id': self.task_id }, { '$set': { 'status' : self.status } })
        if (status == TaskStatus.COMPLETE):
            self.propagate_complete_to_parent()
        elif (status == TaskStatus.FAILED):
            self.propagate_failure_to_parent()

    def is_over(self):
        if self.refresh().status in [TaskStatus.COMPLETE, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            return True
        return False

    def propagate_complete_to_parent(self):
        if self.parent_task_id is not None:
            parent_task = Task.get_by_id(self.parent_task_id)
            if parent_task.is_over():
                return

            siblings = parent_task.get_subtasks()
            if len(list(filter(lambda s: s.status != TaskStatus.COMPLETE, siblings))) == 0:
                parent_task.report_status(TaskStatus.COMPLETE)
                parent_task.report_progress(100)

    def propagate_failure_to_parent(self):
        if self.parent_task_id is not None:
            parent_task = Task.get_by_id(self.parent_task_id)
            parent_task.report_status(TaskStatus.FAILED)

    def report_progress(self, progress):
        self.progress = progress
        db = get_mongo_db().db
        db[tasks_collection_name].update_one({ '_id': self.task_id }, { '$set': { 'progress' : self.progress } })

    def schedule_subtask(self, handler, *args):
        if self.is_over():
            return None

        task = Task()
        task.parent_task_id = self.task_id
        task.root_task_id = self.root_task_id
        task.dump_to_db()
        handler.apply_async(args=args, task_id=task.task_id)
        return task

    def dump_to_db(self):
        current_user = user_context.get()
        current_tenant = tenant_context.get()
        print(f"Storing task with tenant: {current_tenant}")  # Debug print
        
        db_record = {
            '_id': self.task_id,
            'task_id': self.task_id,
            'status': self.status,
            'progress': self.progress,
            'queue_messages': [],
            'configuration_status': {},
            'parent_task_id': self.parent_task_id,
            'root_task_id': self.root_task_id,
            'tenant_id': current_tenant,
            'user_id': current_user,
            'type': self.type
        }
        
        db_instance = get_mongo_db()
        db = db_instance.db
        print("DB_NAME", db_instance.db_name)
        db[tasks_collection_name].insert_one(db_record)

    def refresh(self):
        db = get_mongo_db().db
        db_record = db[tasks_collection_name].find_one({ '_id': self.task_id })
        self.task_id = db_record.get('_id')
        self.status = db_record.get('status')
        self.progress = db_record.get('progress')
        self.parent_task_id = db_record.get('parent_task_id')
        self.root_task_id = db_record.get('root_task_id')
        return self

    def get_subtasks(self):
        db = get_mongo_db().db
        db_records = db[tasks_collection_name].find({ 'parent_task_id': self.task_id })
        return [Task.from_db_record(t) for t in db_records]


class TaskWrapper:
    def __init__(self, task_id):
        self.task = Task.get_by_id(task_id)
        # Get task record from MongoDB
        db = get_mongo_db().db
        self.task_record = db[tasks_collection_name].find_one({'_id': task_id})
        self.tenant_id = self.task_record.get('tenant_id', settings.KAVIA_ROOT_TENANT_ID)
        self.user_id = self.task_record.get('user_id', 'admin')
        print(f"TaskWrapper initialized with tenant: {self.tenant_id}, {self.user_id}")  # Debug print

    def __enter__(self):
        print(f"Setting task context to tenant: {self.tenant_id}")
        # Debug print
        print(f"Setting user context to user: {self.user_id}")
        self.user_token = user_context.set(self.user_id)
        self.token = tenant_context.set(self.tenant_id)
        self.task.report_status(TaskStatus.IN_PROGRESS)
        self.task.report_progress(0)
        return self.task

    def __exit__(self, exception_type, exception_value, exception_traceback):
        try:
            if exception_type:
                if exception_type == SystemExit:
                    self.task.report_status(TaskStatus.CANCELLED)
                else:
                    self.task.report_status(TaskStatus.FAILED)
            else:
                subtasks = self.task.get_subtasks()
                pending_subtasks = list(filter(lambda s: s.status != TaskStatus.COMPLETE, subtasks))
                failed_subtasks = list(filter(lambda s: s.status == TaskStatus.FAILED, subtasks))
                if (len(failed_subtasks) > 0):
                    self.task.report_status(TaskStatus.FAILED)
                elif (len(pending_subtasks) > 0):
                    self.task.report_status(TaskStatus.PENDING_SUBTASKS)
                else:
                    self.task.report_progress(0)
                    self.task.report_status(TaskStatus.IN_PROGRESS)
        finally:
            print(f"Resetting task context from tenant: {self.tenant_id}")  # Debug print
            tenant_context.reset(self.token)