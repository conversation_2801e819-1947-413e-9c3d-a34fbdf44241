{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
Review the purpose and functionalities of the component, its interface details and provide:

1. Algorithmic Details for the major functionalities of the component - 
   - Functionality implementation using pseudo-code - create as child nodes of type Algorithm. child nodes needs to be in json format

{% else %}
You are an expert system architect reviewing the design behaviour for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - System context: 
     - Algorithms: {{ bg_info.get('design_context', {}).get('algorithms') | tojson(indent=2) }}
     - parent_component_node: {{ bg_info.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated system context:
     - Algorithms: {{ new_bg.get('design_context', {}).get('algorithms') | tojson(indent=2) }}
     - parent_component_node: {{ new_bg.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
  

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Component Configuration:
   Existing Containers: {{ details_for_discussion.get('parent_component') | tojson(indent=2) }}
   Algorithms: {{ details_for_discussion.get('algorithms') | tojson(indent=2) }}

Analysis Instructions:


1. Component Architecture Analysis:
   - Review the parent component against new requirements
   - Evaluate component responsibilities and boundaries
   - Assess need for new algorithm components or modifications

2. Required Changes:
   Modify Existing algorithms, or add any required algorithms following the below guidelines 

{% endif %}


Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}
Create the required Algorithm Logic for the given component. 
{% endblock %}

{% block auto_reconfig %}
Create or update the funtional and / or non-functional requirements following the above guidelines.
{% endblock %}


{% block node_details_interactive_reconfig %}
design behaviors - Algorithmic Details 
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Focus on critical functionalities and key algorithmic aspects of the component, addressing any gaps or new requirements. Ensure that both Algorithmic Details and State Management Logic are thoroughly updated.
{% endblock %}

{% block information_about_task %}
Here is the architectural node for the component for which the detail design node is being configured:
{{details_for_discussion.get('architecture_node') | tojson(indent=2)}}
{{details_for_discussion.get('existing_algorithms') | tojson(indent=2)}}
Component interfaces: {{ details_for_discussion.get('interface_details') | tojson(indent=2) }}
{% endblock %}
{% block background_information %}
 Here is the list of child nodes:
   {{ details_for_discussion.get('child_nodes') | tojson(indent=2) }}
{% endblock %}