[{"Role": "Product Reseacher AI", "Capabilities": ["Web based Market analysis", "Web based Competitor analysis"]}, {"Role": "Product Manager AI", "Capabilities": ["Creating Epics and Userstories", "Roadmap planning"]}, {"Role": "Software Architect AI", "Capabilities": ["Design pattern identification", "Architecture decomposition", "Technology stack recommendation", "Interface and API defintion"]}, {"Role": "UI Designer AI", "Capabilities": ["Mockup generation", "Color scheme suggestion", "Usability testing", "Accessibility compliance checking"]}, {"Role": "Software Designer AI", "Capabilities": ["UML diagram generation", "Design pattern suggestion", "Code structure optimization", "Technical documentation generation", "API creation"]}, {"Role": "Software Engineer AI", "Capabilities": ["Code generation", "Code review", "Bug fixing", "Dependency management"]}, {"Role": "Software Test Developer AI", "Capabilities": ["Test case generation", "Test planning", "Test data creation", "Coverage analysis"]}, {"Role": "Software Test Executor AI", "Capabilities": ["Automated test execution", "Performance testing", "Usability testing", "Security testing"]}, {"Role": "Dev-Ops Engineer AI", "Capabilities": ["CI/CD pipeline setup", "Monitoring setup", "Cloud resource optimization", "Incident response"]}]