# app/repository/mongodb/client.py
from typing import Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import certifi
from app.core.Settings import settings

Database = AsyncIOMotorDatabase
db_client: Any = None
ca = certifi.where()

async def connect_db():
    """Create database connection."""
    print(" ++ initialising new connection ++")
    global db_client
    db_client = AsyncIOMotorClient(settings.MONGO_CONNECTION_URI)

async def get_db_client():
    """Return database client instance."""
    return db_client

def close_db():
    """Close database connection."""
    if db_client:
        db_client.close()

async def get_db(db_name: str = settings.MONGO_DB_NAME) -> Database:
    """Return database instance."""
    if db_client is None:
        await connect_db()
    return db_client[db_name]