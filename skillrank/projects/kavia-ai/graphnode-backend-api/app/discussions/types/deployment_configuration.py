from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
import os

class DeploymentConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Architecture")
        self.template_name = "deployment_configuration.prompt"
        self.function_schema_type = "DeploymentConfiguration"

    async def determine_container_type(self, component):
        """
        Analyze component to determine its type based on properties and description.
        Returns: str - Component type ('frontend', 'backend', 'database', or 'unknown')
        """
        # Extract properties and convert to lowercase for case-insensitive matching
        title = component['properties'].get('Title', '').lower()
        description = component['properties'].get('Description', '').lower()
        technology = component['properties'].get('Technology', '').lower()
        
        # Combine all fields for comprehensive searching
        combined_text = f"{title} {description} {technology}"
        
        # Enhanced keyword sets with more comprehensive terms
        database_keywords = {
            'database', 'db', 'storage', 'mongo', 'postgresql', 'mysql', 'redis',
            'oracle', 'sqlserver', 'cassandra', 'elasticsearch', 'dynamodb',
            'mariadb', 'sql', 'nosql', 'data store'
        }
        
        frontend_keywords = {
            'ui', 'frontend', 'react', 'vue', 'angular', 'web', 'client',
            'browser', 'spa', 'tsx', 'jsx','javascript','html','css'
        }
        
        backend_keywords = {
            'api', 'backend', 'server', 'service', 'nodejs', 'java', 'python',
            'microservice', 'rest', 'graphql', 'grpc', 'flask', 'django', 'express','Node.js','Chart.js',
        }

        # Count matches for each type to handle components that might match multiple types
        matches = {
            'database': sum(1 for keyword in database_keywords if keyword in combined_text),
            'frontend': sum(1 for keyword in frontend_keywords if keyword in combined_text),
            'backend': sum(1 for keyword in backend_keywords if keyword in combined_text)
        }
        
        # Debug logging to help track matches
        print(f"Component: {title}")
        print(f"Matches found: {matches}")
        print("--------------------}")
        
        # If we have matches, return the type with the most matches
        if any(matches.values()):
            return max(matches.items(), key=lambda x: x[1])[0]
        
        return "unknown"

    def _set_template_name(self):
        """Set the correct template name based on container type"""
        if self.container_type == "frontend":
            self.template_name = "deployment_frontend_configuration.prompt"
        elif self.container_type == "backend":
            self.template_name = "deployment_backend_configuration.prompt"
        else:
            # Default template for unknown types
            self.template_name = "deployment_configuration.prompt"
        
        self.update_logger.info(f"Selected template: {self.template_name} for container type: {self.container_type}")

    
    async def async_initialize(self):
        # Initialization specific to this configuration
        await super().async_initialize()
        

        deployment_roots = await self.db.get_child_nodes(self.node_id, "Deployment")
        if deployment_roots:
            deployment_root = deployment_roots[0]
        else:
            self.node = await self.db.get_node_by_id(self.node_id)
            properties = {
                'Title': f"Deployment Configuration for {self.node['properties']['Title']}",
                'Description': "Root node for deployment configurations"
            }
            deployment_root = await self.db.create_node(["Deployment"], properties, self.node_id)
        self.node_id = deployment_root['id']
    
    
    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Retrieve container information
        container = await self.db.get_parent_node(self.node_id)
        self.container_type = await self.determine_container_type(container)
        self._set_template_name()
        if container:
            self.retrieved_info['container'] = {
                'id': container['id'],
                'properties': container['properties'],
                'Technology': container['properties'].get('Technology'),
                'Title': container['properties'].get('Title'),
                'Description': container['properties'].get('Description'),
                'Repository_Name': container['properties'].get('Repository_Name')

            }
        
        # Get deployment node to access configuration
        deployment_nodes = await self.db.get_child_nodes(container['id'], "Deployment")
        if deployment_nodes:
            deployment_node = deployment_nodes[0]
            deployment_config = deployment_node['properties'].get('deployment_config', {})
            if isinstance(deployment_config, str):
                deployment_config = json.loads(deployment_config)
            elif not isinstance(deployment_config, dict):
                deployment_config = {}
                
            # Set variables using deployment configuration
            self.retrieved_info['variables'] = {
                'access_token': os.environ["GITHUB_ACCESS_TOKEN"],
                'repo_url': deployment_config.get('repo_url', ''),
                'app_name': deployment_config.get('app_name', ''),
                'branch': deployment_config.get('branch', ''),
                'domain_name': deployment_config.get('domain_name', '')
            }
        else:
            # Fallback to default values if no deployment configuration exists
            self.retrieved_info['variables'] = {
                'access_token': os.environ["GITHUB_ACCESS_TOKEN"],
                'repo_url': "https://github.com/cjay91/cosmetics-recommender-cosmetics-recommender-44195-44388_deploy",
                'app_name': "demo-app-2020-12-06",
                'branch': "conflict-resolution-8d71f9cd",
                'domain_name': "demo-app-2020-12-06.com"
            }
        
        return self.retrieved_info

    
    def get_modifications_from_llm_output(self):

        #super().get_modifications_from_llm_output(node_type, root_node_type, discussion_type, llm_output)
        self.modifications['modified_node']['Type'] = 'Deployment'


        return
    
    async def merge_captured_items(self):
        await super().merge_captured_items()
        
        #if self.modifications.get('next_steps') == "Start Detailed Design":
        #    await self.db.add_label_to_node(self.node_id, "ComponentRoot")
        return
            


# Registering the subclass in the factory
DiscussionFactory.register( 'Configuration' , DeploymentConfiguration,'Deployment')