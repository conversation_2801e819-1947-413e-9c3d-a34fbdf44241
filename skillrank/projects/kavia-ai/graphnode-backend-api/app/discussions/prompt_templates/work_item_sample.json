{"component_name": "User Management Module", "description": "Handles user account creation, authentication, and profile management.", "interfaces": [{"architecture_id": 59647, "relationship_properties": {"Description": "Allows user management to provide user data to the screen time tracking module.", "design_details_state": "configured", "Type": "interfacesWith", "interface_node_id": 59730, "Title": "User Management to Screen Time Tracking"}, "interface_node": {"id": 59730, "properties": {"Description": "The interface titled 'Details for User Management to Screen Time Tracking' facilitates the integration between the User Management Module and the Screen Time Tracking Module. It allows for the secure transfer of user data, including account information and screen time statistics, ensuring that users can effectively manage their digital wellbeing. The interface is designed to support RESTful API communication, enabling real-time data synchronization and robust error handling. Key functionalities include user authentication, data retrieval for screen time tracking, and notifications regarding user activities.", "design_details_state": "configured", "definition_state": "configured", "source_node_id": 59646, "Interface_definition_state": "configured", "target_node_id": 59647, "Title": "Details for User Management to Screen Time Tracking"}}}, {"architecture_id": 59648, "relationship_properties": {"Description": "User management provides user data for analytics.", "design_details_state": "configured", "Type": "interfacesWith", "interface_node_id": 59715, "Title": "User Management to Analytics Dashboard"}, "interface_node": {"id": 59715, "properties": {"design_detail_state": "configured", "Description": "The interface between the User Management Module and the Analytics Dashboard Module is designed to facilitate the secure and efficient transfer of user data for analytics purposes. It adheres to RESTful principles, ensuring that it is stateless and can be easily consumed by the Analytics Dashboard. The interface supports real-time data synchronization and provides robust error handling to enhance the user experience.", "design_details_state": "configured", "definition_state": "configured", "FunctionalRequirements": "1. User Data Provision: The User Management Module must provide user data (e.g., user profiles, screen time statistics) to the Analytics Dashboard for visualization.\n2. Data Synchronization: The interface should ensure that user data is synchronized in real-time or near real-time to provide up-to-date analytics.\n3. User Authentication: The interface must support secure authentication mechanisms to ensure that only authorized requests are processed.\n4. Error Handling: The interface should handle errors gracefully, providing meaningful error messages and status codes to the calling component.\n5. Data Filtering: The Analytics Dashboard should be able to request specific subsets of user data based on parameters (e.g., date range, user preferences).", "source_node_id": 59646, "TechnicalRequirements": "1. Protocol: The interface will utilize RESTful APIs for communication between the User Management Module and the Analytics Dashboard.\n2. Data Format: JSON will be used as the data format for requests and responses.\n3. Authentication: JWT (JSON Web Tokens) will be used for secure authentication between the components.\n4. Rate Limiting: Implement rate limiting to prevent abuse of the API and ensure fair usage.\n5. Versioning: The API should support versioning to allow for future enhancements without breaking existing functionality.", "Interface_definition_state": "configured", "DesignDetails": "1. Endpoint Definitions:\n   - **GET /api/v1/users/{userId}/analytics**: Fetch analytics data for a specific user.\n     - **Request Parameters**: \n       - `userId`: The ID of the user whose analytics data is being requested.\n       - `startDate`: Optional start date for filtering data.\n       - `endDate`: Optional end date for filtering data.\n     - **Response**: \n       - 200 OK: Returns user analytics data in JSON format.\n       - 401 Unauthorized: If the JWT is invalid or missing.\n       - 404 Not Found: If the user does not exist.\n       - 500 Internal Server Error: For any server-side issues.\n\n2. Data Structures:\n   - **User Analytics Response**:\n     ```json\n     {\n       \"userId\": \"string\",\n       \"screenTime\": {\n         \"total\": \"number\",\n         \"dailyAverage\": \"number\",\n         \"trends\": [\n           {\n             \"date\": \"string\",\n             \"usage\": \"number\"\n           }\n         ]\n       },\n       \"goals\": [\n         {\n           \"goalId\": \"string\",\n           \"description\": \"string\",\n           \"achieved\": \"boolean\"\n         }\n       ]\n     }\n     ```\n\n3. Error Handling:\n   - Standardize error responses to include an error code and message.\n   - Example error response:\n     ```json\n     {\n       \"error\": {\n         \"code\": \"string\",\n         \"message\": \"string\"\n       }\n     }\n     ```\n\n4. Security Measures:\n   - Ensure that all API requests are made over HTTPS.\n   - Validate JWT tokens on each request to ensure the authenticity of the user.", "target_node_id": 59648, "Title": "User Management to Analytics Dashboard", "InterfaceDescription": "The interface between the User Management Module and the Analytics Dashboard Module is designed to facilitate the secure and efficient transfer of user data for analytics purposes. It adheres to RESTful principles, ensuring that it is stateless and can be easily consumed by the Analytics Dashboard. The interface supports real-time data synchronization and provides robust error handling to enhance the user experience."}}}, {"architecture_id": 59662, "relationship_properties": {"Description": "This interface allows the Community Forum to send notifications to the User Management Module regarding user interactions.", "design_details_state": "configured", "Type": "interfacesWith", "interface_node_id": 59744, "Title": "Notification Interface"}, "interface_node": {"id": 59744, "properties": {"Description": "The Notification Interface facilitates communication between the Notifications and Alerts sub-component and the User Management Module. It is designed to enhance user engagement by providing timely updates and reminders related to screen time and goal achievements. The interface utilizes RESTful APIs for secure and efficient data transfer, ensuring that notifications are sent reliably. Key functionalities include sending notifications, managing user preferences for notification types, and providing real-time updates. The interface also incorporates error handling mechanisms, such as retries and logging, to ensure reliability and maintainability.", "design_details_state": "configured", "definition_state": "configured", "source_node_id": 59662, "Interface_definition_state": "configured", "target_node_id": 59646, "Title": "Details for Notification Interface"}}}, {"architecture_id": 59681, "relationship_properties": {"Description": "This interface allows the Community Forum to send notifications to the User Management Module regarding user interactions.", "design_details_state": "configured", "Type": "interfacesWith", "interface_node_id": 59746, "Title": "Notification Interface"}, "interface_node": {"id": 59746, "properties": {"Description": "The **Notification Interface** facilitates communication between the Notifications and Alerts sub-component and the User Management Module. It is designed to enhance user engagement by providing timely updates and reminders related to screen time and goal achievements. The interface utilizes RESTful APIs for secure and efficient data transfer, ensuring that notifications are sent reliably. Key functionalities include sending notifications, managing user preferences for notification types, and providing real-time updates. The interface also incorporates error handling mechanisms, such as retries and logging, to ensure reliability and maintainability.", "design_details_state": "configured", "definition_state": "configured", "source_node_id": 59681, "Interface_definition_state": "configured", "target_node_id": 59646, "Title": "Details for Notification Interface"}}}], "algorithms": "", "pseudocode": "", "repository_name": null, "root_folder": null, "figma_components": [{"frame_id": "366:2080", "file_key": "atvZngn3w0bFH9gXtigpGt", "json_data": {"id": "366:2080", "name": "sign up", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2081", "name": "home button", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "207:80", "overrides": [{"id": "366:2081", "overriddenFields": ["name"]}], "children": [{"id": "I366:2081;207:81", "name": "Home Indicator", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "styles": {"fill": "207:43"}, "cornerRadius": 100.0, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 830.0, "y": -210.0, "width": 134.0, "height": 5.0}, "absoluteRenderBounds": {"x": 830.0, "y": -210.0, "width": 134.0, "height": 5.0}, "constraints": {"vertical": "BOTTOM", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 709.0, "y": -231.0, "width": 375.0, "height": 34.0}, "absoluteRenderBounds": {"x": 709.0, "y": -231.0, "width": 375.0, "height": 34.0}, "constraints": {"vertical": "BOTTOM", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2082", "name": "main content", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2083", "name": "Sign up", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "styles": {"fill": "206:151", "text": "207:523"}, "absoluteBoundingBox": {"x": 733.0, "y": -729.0, "width": 327.0, "height": 37.0}, "absoluteRenderBounds": {"x": 839.3187866210938, "y": -723.1259765625, "width": 114.80804443359375, "height": 32.42596435546875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Sign up", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Volkhov-Bold", "fontWeight": 700, "textAutoResize": "HEIGHT", "fontSize": 31.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 37.20000076293945, "lineHeightPercent": 93.**************, "lineHeightPercentFontSize": 120.**************, "lineHeightUnit": "FONT_SIZE_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "366:2084", "name": "sign in with buttons", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2085", "name": "google", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2086", "name": "button bg", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"opacity": 0.4000000059604645, "blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.6000000238418579, "g": 0.6000000238418579, "b": 0.6000000238418579, "a": 1.0}}, {"opacity": 0.20000000298023224, "blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.6000000238418579, "g": 0.6000000238418579, "b": 0.6000000238418579, "a": 1.0}}, {"opacity": 0.10000000149011612, "blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.6000000238418579, "g": 0.6000000238418579, "b": 0.6000000238418579, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "cornerRadius": 2.5878007411956787, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 736.056396484375, "y": -660.0, "width": 320.88726806640625, "height": 51.756011962890625}, "absoluteRenderBounds": {"x": 736.056396484375, "y": -660.0, "width": 320.88726806640625, "height": 51.756011962890625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": false, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.23999999463558197}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 10.351202964782715}, "radius": 10.351202964782715, "showShadowBehindNode": true}, {"type": "DROP_SHADOW", "visible": false, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.11999999731779099}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 0.0}, "radius": 10.351202964782715, "showShadowBehindNode": true}, {"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.23999999463558197}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 2.5878007411956787}, "radius": 2.5878007411956787, "showShadowBehindNode": true}, {"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.11999999731779099}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 0.0}, "radius": 2.5878007411956787, "showShadowBehindNode": true}], "interactions": []}, {"id": "366:2087", "name": "Sign in with Google", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.5400000214576721, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 800.7514038085938, "y": -644.4732055664062, "width": 192.79115295410156, "height": 20.702402114868164}, "absoluteRenderBounds": {"x": 801.4059448242188, "y": -642.0591430664062, "width": 165.39300537109375, "height": 17.35394287109375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Sign up with Google", "style": {"fontFamily": "Roboto", "fontPostScriptName": "Roboto-Medium", "fontWeight": 500, "fontSize": 18.114601135253906, "textAlignHorizontal": "LEFT", "textAlignVertical": "BOTTOM", "letterSpacing": 0.2830406427383423, "lineHeightPx": 21.22804832458496, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "366:2088", "name": "super g", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2089", "name": "<PERSON><PERSON><PERSON>", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9176470637321472, "g": 0.26274511218070984, "b": 0.2078431397676468, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 751.5315551757812, "y": -645.76708984375, "width": 18.192237854003906, "height": 9.341958999633789}, "absoluteRenderBounds": {"x": 751.5315551757812, "y": -645.76708984375, "width": 18.1922607421875, "height": 9.34197998046875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2090", "name": "<PERSON><PERSON><PERSON>", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.25882354378700256, "g": 0.5215686559677124, "b": 0.95686274766922, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 761.9344482421875, "y": -636.2439575195312, "width": 11.179299354553223, "height": 10.946395874023438}, "absoluteRenderBounds": {"x": 761.9344482421875, "y": -636.2439575195312, "width": 11.1793212890625, "height": 10.9464111328125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2091", "name": "<PERSON><PERSON><PERSON>", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9843137264251709, "g": 0.7372549176216125, "b": 0.019607843831181526, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 750.2892456054688, "y": -639.349365234375, "width": 5.020333290100098, "height": 10.45471477508545}, "absoluteRenderBounds": {"x": 750.2892456054688, "y": -639.349365234375, "width": 5.02032470703125, "height": 10.4547119140625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2092", "name": "<PERSON><PERSON><PERSON>", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.20392157137393951, "g": 0.658823549747467, "b": 0.32549020648002625, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 751.54443359375, "y": -631.81884765625, "width": 18.10166358947754, "height": 9.341958999633789}, "absoluteRenderBounds": {"x": 751.54443359375, "y": -631.81884765625, "width": 18.1016845703125, "height": 9.34197998046875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2093", "name": "<PERSON><PERSON><PERSON>", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 750.2892456054688, "y": -645.76708984375, "width": 23.290203094482422, "height": 23.290203094482422}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.2939003705978394, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 750.2892456054688, "y": -645.76708984375, "width": 23.290203094482422, "height": 23.290203094482422}, "absoluteRenderBounds": {"x": 750.2892456054688, "y": -645.76708984375, "width": 23.29022216796875, "height": 23.29022216796875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": false, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.5}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 2.5878007411956787}, "radius": 5.175601482391357, "showShadowBehindNode": true}], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.2939003705978394, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 736.056396484375, "y": -660.0, "width": 320.88726806640625, "height": 51.756011962890625}, "absoluteRenderBounds": {"x": 736.056396484375, "y": -660.0, "width": 320.88726806640625, "height": 51.756011962890625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "372:718", "transitionDuration": 150.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "EASE_OUT"}, "duration": 0.15000000596046448}, "preserveScrollPosition": false}]}]}, {"id": "366:2094", "name": "facebook", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2095", "name": "button bg copy", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.14509804546833038, "g": 0.32549020648002625, "b": 0.7058823704719543, "a": 1.0}}, {"opacity": 0.4000000059604645, "blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.6000000238418579, "g": 0.6000000238418579, "b": 0.6000000238418579, "a": 1.0}}, {"opacity": 0.20000000298023224, "blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.6000000238418579, "g": 0.6000000238418579, "b": 0.6000000238418579, "a": 1.0}}, {"opacity": 0.10000000149011612, "blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 0.6000000238418579, "g": 0.6000000238418579, "b": 0.6000000238418579, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "cornerRadius": 2.575021266937256, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 736.056396484375, "y": -592.2440185546875, "width": 319.3026123046875, "height": 51.500423431396484}, "absoluteRenderBounds": {"x": 736.056396484375, "y": -592.2440185546875, "width": 320.88726806640625, "height": 51.500423431396484}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": false, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.23999999463558197}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 10.300085067749023}, "radius": 10.300085067749023, "showShadowBehindNode": true}, {"type": "DROP_SHADOW", "visible": false, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.11999999731779099}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 0.0}, "radius": 10.300085067749023, "showShadowBehindNode": true}, {"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.23999999463558197}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 2.575021266937256}, "radius": 2.575021266937256, "showShadowBehindNode": true}, {"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.11999999731779099}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 0.0}, "radius": 2.575021266937256, "showShadowBehindNode": true}], "interactions": []}, {"id": "366:2096", "name": "Sign in with <PERSON><PERSON>", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 804.29443359375, "y": -576.7938842773438, "width": 242.05197143554688, "height": 20.600168228149414}, "absoluteRenderBounds": {"x": 804.9457397460938, "y": -574.312744140625, "width": 187.04608154296875, "height": 17.26824951171875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "characters": "Sign up with Facebook", "style": {"fontFamily": "Roboto", "fontPostScriptName": "Roboto-Medium", "fontWeight": 500, "fontSize": 18.025146484375, "textAlignHorizontal": "LEFT", "textAlignVertical": "BOTTOM", "letterSpacing": 0.2816429138183594, "lineHeightPx": 21.123218536376953, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "366:2097", "name": "facebook-icon", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 753.0411376953125, "y": -578.0813598632812, "width": 17.232755661010742, "height": 37.33780288696289}, "absoluteRenderBounds": {"x": 753.0411376953125, "y": -578.0813598632812, "width": 17.23272705078125, "height": 37.337764739990234}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.287510633468628, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 736.056396484375, "y": -592.2440185546875, "width": 320.88726806640625, "height": 51.500423431396484}, "absoluteRenderBounds": {"x": 736.056396484375, "y": -592.2440185546875, "width": 320.88726806640625, "height": 51.50042724609375}, "constraints": {"vertical": "SCALE", "horizontal": "CENTER"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "372:718", "transitionDuration": 150.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "EASE_OUT"}, "duration": 0.15000000596046448}, "preserveScrollPosition": false}]}]}, {"id": "366:2098", "name": "apple", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "IMAGE", "scaleMode": "FILL", "imageRef": "7dd9308ae135c3ca180877d5b2e003bf58f7e202"}], "strokes": [], "strokeWeight": 0.9903926849365234, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 736.5516357421875, "y": -524.7435302734375, "width": 319.8968505859375, "height": 57.44277572631836}, "absoluteRenderBounds": {"x": 736.5516357421875, "y": -524.7435302734375, "width": 319.8968505859375, "height": 57.442779541015625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "372:718", "transitionDuration": 150.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": {"type": "SMART_ANIMATE", "easing": {"type": "EASE_OUT"}, "duration": 0.15000000596046448}, "preserveScrollPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "itemSpacing": 16.0, "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 736.056396484375, "y": -660.0, "width": 320.88726806640625, "height": 192.69921875}, "absoluteRenderBounds": {"x": 736.056396484375, "y": -660.0, "width": 320.88726806640625, "height": 192.69924926757812}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "366:2099", "name": "— OR —", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "styles": {"fill": "206:151", "text": "207:517"}, "absoluteBoundingBox": {"x": 733.0, "y": -435.********, "width": 327.0, "height": 19.0}, "absoluteRenderBounds": {"x": 864.0980224609375, "y": -431.74078369140625, "width": 64.80059814453125, "height": 11.67999267578125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "— OR —", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Volkhov-Bold", "fontWeight": 700, "textAutoResize": "HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 19.2**************, "lineHeightPercent": 93.**************, "lineHeightPercentFontSize": 120.**************, "lineHeightUnit": "FONT_SIZE_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "366:2100", "name": "email", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "217:282", "overrides": [{"id": "I366:2100;207:666", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "366:2100", "overriddenFields": ["name", "transitionEasing", "transitionDuration", "transitionNodeID"]}], "children": [{"id": "I366:2100;207:980", "name": "black bg", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 4.0, "strokeAlign": "CENTER", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "styles": {"fills": "206:152", "fill": "206:152"}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 4.0, "counterAxisAlignItems": "CENTER", "paddingLeft": 24.0, "paddingRight": 24.0, "paddingTop": 12.0, "paddingBottom": 12.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 735.0, "y": -380.********, "width": 325.0, "height": 48.0}, "absoluteRenderBounds": {"x": 735.0, "y": -380.********, "width": 325.0, "height": 48.0}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": null}]}]}, {"id": "I366:2100;207:664", "name": "Buttons", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I366:2100;207:665", "name": "Content", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I366:2100;207:666", "name": "Primary button", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.9882352948188782, "b": 0.9686274528503418, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "styles": {"fill": "207:372", "text": "207:518"}, "absoluteBoundingBox": {"x": 866.5, "y": -372.********, "width": 58.0, "height": 24.0}, "absoluteRenderBounds": {"x": 867.3800048828125, "y": -369.********, "width": 55.8282470703125, "height": 16.29998779296875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Email", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Volkhov-Regular", "fontWeight": 400, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 20.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 24.0, "lineHeightPercent": 93.**************, "lineHeightPercentFontSize": 120.**************, "lineHeightUnit": "FONT_SIZE_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 4.0, "counterAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 866.5, "y": -372.********, "width": 58.0, "height": 24.0}, "absoluteRenderBounds": {"x": 866.5, "y": -372.********, "width": 58.0, "height": 24.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.027450980618596077, "g": 0.14901961386203766, "b": 0.47058823704719543, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.027450980618596077, "g": 0.14901961386203766, "b": 0.47058823704719543, "a": 1.0}}], "strokes": [], "strokeWeight": 4.0, "strokeAlign": "CENTER", "backgroundColor": {"r": 0.027450980618596077, "g": 0.14901961386203766, "b": 0.47058823704719543, "a": 1.0}, "styles": {"fills": "207:363", "fill": "207:363"}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 4.0, "counterAxisAlignItems": "CENTER", "paddingLeft": 24.0, "paddingRight": 24.0, "paddingTop": 12.0, "paddingBottom": 12.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 733.0, "y": -384.********, "width": 325.0, "height": 48.0}, "absoluteRenderBounds": {"x": 733.0, "y": -384.********, "width": 325.0, "height": 48.0}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": null}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 733.0, "y": -384.********, "width": 327.0, "height": 52.0}, "absoluteRenderBounds": {"x": 733.0, "y": -384.********, "width": 327.0, "height": 52.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "366:2046", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": null}]}]}, {"id": "366:2101", "name": "I have an account already.", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.*****************, "b": 0.*****************, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "styles": {"fill": "206:151", "text": "207:537"}, "absoluteBoundingBox": {"x": 733.0, "y": -300.********, "width": 327.0, "height": 24.0}, "absoluteRenderBounds": {"x": 769.3515625, "y": -297.********, "width": 254.**************, "height": 21.***************}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "I have an account already.", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Volkhov-Bold", "fontWeight": 700, "textAutoResize": "HEIGHT", "textDecoration": "UNDERLINE", "fontSize": 20.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 24.0, "lineHeightPercent": 93.**************, "lineHeightPercentFontSize": 120.**************, "lineHeightUnit": "FONT_SIZE_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "transitionNodeID": "366:2130", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": null, "navigation": "NAVIGATE", "transition": null}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "itemSpacing": 32.0, "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 733.0, "y": -729.0, "width": 327.0, "height": 452.69921875}, "absoluteRenderBounds": {"x": 733.0, "y": -729.0, "width": 327.0, "height": 453.6792297363281}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "366:2102", "name": "batch", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2103", "name": "<PERSON><PERSON>", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0117647061124444, "g": 0.3843137323856354, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.22504794597625732, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 751.3677368164062, "y": -897.9412841796875, "width": 288.5113830566406, "height": 103.52203369140625}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "<PERSON><PERSON>", "style": {"fontFamily": "Nunito Sans", "fontPostScriptName": "NunitoSans-Black", "fontWeight": 900, "fontSize": 103.6294174194336, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 103.6294174194336, "lineHeightPercent": 73.31378173828125, "lineHeightPercentFontSize": 100.0, "lineHeightUnit": "FONT_SIZE_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "366:2104", "name": "<PERSON><PERSON>", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.22504794597625732, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 675.2518310546875, "y": -897.9412841796875, "width": 288.5113830566406, "height": 103.52203369140625}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "characters": "<PERSON><PERSON>", "style": {"fontFamily": "Nunito Sans", "fontPostScriptName": "NunitoSans-Black", "fontWeight": 900, "fontSize": 103.6294174194336, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 103.6294174194336, "lineHeightPercent": 73.31378173828125, "lineHeightPercentFontSize": 100.0, "lineHeightUnit": "FONT_SIZE_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.027450980618596077, "g": 0.14901961386203766, "b": 0.47058823704719543, "a": 1.0}}], "strokes": [], "strokeWeight": 2.0, "strokeAlign": "INSIDE", "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 758.0, "y": -885.0, "width": 276.5642395019531, "height": 74.19866180419922}, "absoluteRenderBounds": {"x": 758.0, "y": -885.0, "width": 276.564208984375, "height": 74.19866943359375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "366:2105", "name": "ui bar", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2106", "name": "Notch", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2107", "name": "BG", "visible": false, "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 44.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP_BOTTOM", "horizontal": "LEFT_RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2108", "name": "Exclude", "visible": false, "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2109", "name": "BG", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8392156958580017, "g": 0.8509804010391235, "b": 0.8666666746139526, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "styles": {"fill": "207:40"}, "absoluteBoundingBox": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 44.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP_BOTTOM", "horizontal": "LEFT_RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.1411764770746231, "g": 0.14901961386203766, "b": 0.16862745583057404, "a": 1.0}}], "strokes": [], "strokeWeight": 0.6697247624397278, "strokeAlign": "INSIDE", "styles": {"fill": "207:39"}, "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 44.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2110", "name": "Notch", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "fillOverrideTable": {"6": null, "5": null, "4": null, "3": null, "2": null, "1": null}, "strokes": [], "strokeWeight": 0.6697247624397278, "strokeAlign": "INSIDE", "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 787.0, "y": -1011.0, "width": 219.0, "height": 30.0}, "absoluteRenderBounds": {"x": 787.0, "y": -1009.0, "width": 219.0, "height": 28.0}, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "CENTER"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 709.0, "y": -1011.0, "width": 375.0, "height": 46.0}, "absoluteRenderBounds": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 44.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2111", "name": "Right Side", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2112", "name": "Battery", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2113", "name": "Rectangle", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "NORMAL", "opacity": 0.3499999940395355, "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "cornerRadius": 2.6666667461395264, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 1045.0, "y": -991.6666259765625, "width": 22.0, "height": 11.333333015441895}, "absoluteRenderBounds": {"x": 1045.0, "y": -991.6666259765625, "width": 22.0, "height": 11.33331298828125}, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2114", "name": "Combined Shape", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "NORMAL", "opacity": 0.4000000059604645, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1068.0, "y": -987.9999389648438, "width": 1.328037977218628, "height": 4.0}, "absoluteRenderBounds": {"x": 1068.0, "y": -987.9999389648438, "width": 1.3280029296875, "height": 4.0}, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2115", "name": "Rectangle", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "cornerRadius": 1.3333333730697632, "cornerSmoothing": 0.0, "absoluteBoundingBox": {"x": 1047.0, "y": -989.6666259765625, "width": 18.0, "height": 7.333333492279053}, "absoluteRenderBounds": {"x": 1047.0, "y": -989.6666259765625, "width": 18.0, "height": 7.33331298828125}, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 1045.0, "y": -991.6666259765625, "width": 24.32803726196289, "height": 11.333333015441895}, "absoluteRenderBounds": {"x": 1045.0, "y": -991.6666259765625, "width": 24.32803726196289, "height": 11.333333015441895}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "366:2116", "name": "Wifi", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2117", "name": "Wifi-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1024.6937255859375, "y": -991.6693115234375, "width": 15.27237606048584, "height": 4.743237495422363}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2118", "name": "Wifi-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1027.3570556640625, "y": -987.875244140625, "width": 9.94901180267334, "height": 3.6339809894561768}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2119", "name": "Wifi-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1030.01708984375, "y": -984.078125, "width": 4.626293659210205, "height": 3.3744072914123535}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 1024.6937255859375, "y": -991.6693115234375, "width": 15.27237606048584, "height": 10.965571403503418}, "absoluteRenderBounds": {"x": 1024.6937255859375, "y": -991.6693115234375, "width": 15.2723388671875, "height": 10.965576171875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "366:2120", "name": "Mobile Signal", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2121", "name": "Cellular_Connection-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1002.6666259765625, "y": -984.6666870117188, "width": 3.0, "height": 4.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2122", "name": "Cellular_Connection-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1007.3333129882812, "y": -986.6666870117188, "width": 3.0, "height": 6.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2123", "name": "Cellular_Connection-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1011.9999389648438, "y": -989.0000610351562, "width": 3.0, "height": 8.333333015441895}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}, {"id": "366:2124", "name": "Cellular_Connection-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8549019694328308, "g": 0.8549019694328308, "b": 0.8549019694328308, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "absoluteBoundingBox": {"x": 1016.6666259765625, "y": -991.3333740234375, "width": 3.0, "height": 10.666666984558105}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "booleanOperation": "EXCLUDE", "absoluteBoundingBox": {"x": 1002.6666259765625, "y": -991.3333740234375, "width": 17.0, "height": 10.666666984558105}, "absoluteRenderBounds": {"x": 1002.6666259765625, "y": -991.3333740234375, "width": 17.0, "height": 10.66668701171875}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "366:2125", "name": "Recording Indicator", "visible": false, "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2126", "name": "Indicator", "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 0.5843137502670288, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "styles": {"fill": "207:41"}, "absoluteBoundingBox": {"x": 1007.0, "y": -1000.9998779296875, "width": 6.0, "height": 6.0}, "absoluteRenderBounds": null, "preserveRatio": true, "constraints": {"vertical": "TOP", "horizontal": "RIGHT"}, "effects": [], "arcData": {"startingAngle": 0.0, "endingAngle": 6.2831854820251465, "innerRadius": 0.0}, "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 1007.0, "y": -1000.9998779296875, "width": 6.0, "height": 6.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "rectangleCornerRadii": [0.0, 0.0, 0.0, 0.0], "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 1002.6666259765625, "y": -1000.9998779296875, "width": 66.661376953125, "height": 20.666584014892578}, "absoluteRenderBounds": {"x": 1002.6666259765625, "y": -1000.9998779296875, "width": 66.661376953125, "height": 20.666584014892578}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "366:2127", "name": "Left Side", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2128", "name": "Time", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "366:2129", "name": "9:41", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 0.0, "strokeAlign": "CENTER", "styles": {"fill": "207:42"}, "absoluteBoundingBox": {"x": 742.4534912109375, "y": -991.83251953125, "width": 28.42616844177246, "height": 11.0888671875}, "absoluteRenderBounds": {"x": 742.4534912109375, "y": -991.83251953125, "width": 28.4261474609375, "height": 11.0888671875}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "cornerRadius": 32.0, "cornerSmoothing": 0.6000000238418579, "strokeWeight": 0.0, "strokeAlign": "CENTER", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 730.0, "y": -997.0, "width": 54.0, "height": 21.0}, "absoluteRenderBounds": {"x": 730.0, "y": -997.0, "width": 54.0, "height": 21.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "cornerRadius": 32.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 730.0, "y": -997.0, "width": 54.0, "height": 21.0}, "absoluteRenderBounds": {"x": 730.0, "y": -997.0, "width": 54.0, "height": 21.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 44.0}, "absoluteRenderBounds": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 44.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8980392217636108, "g": 0.9686274528503418, "b": 0.9921568632125854, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8980392217636108, "g": 0.9686274528503418, "b": 0.9921568632125854, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.8980392217636108, "g": 0.9686274528503418, "b": 0.9921568632125854, "a": 1.0}, "styles": {"fills": "207:364", "grid": "207:35", "fill": "207:364"}, "layoutGrids": [{"pattern": "COLUMNS", "sectionSize": 69.75, "visible": true, "color": {"r": 0.8208333253860474, "g": 0.8208333253860474, "b": 0.8208333253860474, "a": 0.10000000149011612}, "alignment": "STRETCH", "gutterSize": 16.0, "offset": 24.0, "count": 4}], "absoluteBoundingBox": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 812.0}, "absoluteRenderBounds": {"x": 709.0, "y": -1009.0, "width": 375.0, "height": 812.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/f1ec27f7-c3a5-4b72-9ab6-b5ba24e9b54b", "thumbnailUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/404c6cbd-865a-463f-bbbb-891437fb562b"}, {"frame_id": "207:0", "file_key": "atvZngn3w0bFH9gXtigpGt", "json_data": {"id": "207:0", "name": "brand", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "207:2", "name": "colour swatch", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "205:151", "overrides": [{"id": "I207:2;205:154", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I207:2;205:214", "overriddenFields": ["name", "inheritFillStyleId", "fills"]}], "children": [{"id": "I207:2;205:214", "name": "Light blue", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8980392217636108, "g": 0.9686274528503418, "b": 0.9921568632125854, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "styles": {"fill": "207:364"}, "absoluteBoundingBox": {"x": -198.0, "y": 251.0, "width": 120.0, "height": 80.0}, "absoluteRenderBounds": {"x": -198.0, "y": 251.0, "width": 120.0, "height": 80.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I207:2;205:153", "name": "text", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I207:2;205:154", "name": "Colour swatch", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": -190.0, "y": 339.0, "width": 104.0, "height": 14.0}, "absoluteRenderBounds": {"x": -188.95599365234375, "y": 341.3840026855469, "width": 54.084930419921875, "height": 11.256011962890625}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Light blue", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Rubik-Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 12.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 14.219999313354492, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "styles": {"text": "205:149"}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I207:2;205:155", "name": "Use", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": -190.0, "y": 357.0, "width": 104.0, "height": 11.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Use", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Rubik-Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 9.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 10.664999008178711, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "styles": {"text": "205:150"}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "itemSpacing": 4.0, "paddingLeft": 8.0, "paddingRight": 8.0, "paddingTop": 8.0, "paddingBottom": 8.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -198.0, "y": 331.0, "width": 120.0, "height": 30.0}, "absoluteRenderBounds": {"x": -199.0, "y": 330.0, "width": 122.0, "height": 32.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -198.0, "y": 251.0, "width": 120.0, "height": 110.0}, "absoluteRenderBounds": {"x": -199.0, "y": 250.0, "width": 122.0, "height": 112.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "207:3", "name": "colour swatch", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "205:151", "overrides": [{"id": "I207:3;205:154", "overriddenFields": ["lineIndentations", "lineTypes", "characterStyleOverrides", "styleOverrideTable", "characters"]}, {"id": "I207:3;205:214", "overriddenFields": ["name", "inheritFillStyleId", "fills"]}], "children": [{"id": "I207:3;205:214", "name": "Dark blue", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.027450980618596077, "g": 0.14901961386203766, "b": 0.47058823704719543, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "styles": {"fill": "207:363"}, "absoluteBoundingBox": {"x": -54.0, "y": 251.0, "width": 120.0, "height": 80.0}, "absoluteRenderBounds": {"x": -54.0, "y": 251.0, "width": 120.0, "height": 80.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I207:3;205:153", "name": "text", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I207:3;205:154", "name": "Colour swatch", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": -46.0, "y": 339.0, "width": 104.0, "height": 14.0}, "absoluteRenderBounds": {"x": -44.95600128173828, "y": 341.4800109863281, "width": 50.862281799316406, "height": 8.639984130859375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "characters": "Dark blue", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Rubik-Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 12.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 14.219999313354492, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "styles": {"text": "205:149"}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}, {"id": "I207:3;205:155", "name": "Use", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": -46.0, "y": 357.0, "width": 104.0, "height": 11.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Use", "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontPostScriptName": "Rubik-Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 9.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 10.664999008178711, "lineHeightPercent": 100.0, "lineHeightUnit": "INTRINSIC_%"}, "characterStyleOverrides": [], "styleOverrideTable": {}, "styles": {"text": "205:150"}, "lineTypes": ["NONE"], "lineIndentations": [0], "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "itemSpacing": 4.0, "paddingLeft": 8.0, "paddingRight": 8.0, "paddingTop": 8.0, "paddingBottom": 8.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -54.0, "y": 331.0, "width": 120.0, "height": 30.0}, "absoluteRenderBounds": {"x": -55.0, "y": 330.0, "width": 122.0, "height": 32.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -54.0, "y": 251.0, "width": 120.0, "height": 110.0}, "absoluteRenderBounds": {"x": -55.0, "y": 250.0, "width": 122.0, "height": 112.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 24.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": -198.0, "y": 251.0, "width": 264.0, "height": 110.0}, "absoluteRenderBounds": {"x": -199.0, "y": 250.0, "width": 266.0, "height": 112.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, "imageUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/5e4488e1-cb84-44f0-9329-fc3c96a103c4", "thumbnailUrl": "https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/5d09570e-a7c5-44df-9936-2d40c194f5c0"}], "design": {"classdiagram_state": "configured", "Description": "The User Management Module provides user data to the Screen Time Tracking Module and the Analytics Dashboard Module. It is designed to facilitate secure and efficient data transfer, ensuring real-time synchronization and robust error handling. The module supports user authentication and data filtering, allowing for personalized analytics and insights. The architecture is based on microservices, ensuring scalability and ease of integration with other components of the Digital Wellbeing project.", "Type": "Design", "InputsAndOutputs": "**Inputs:** 1. User Registration: { username: string, password: string, email: string, preferences: object } 2. User Login: { username: string, password: string } 3. Profile Update: { userId: string, updatedData: object } 4. Password Reset: { email: string }  \n**Outputs:** 1. Registration Response: { success: boolean, message: string } 2. Authentication Token: { token: string, userId: string } 3. Profile Data: { userId: string, username: string, preferences: object } 4. Password Reset Confirmation: { success: boolean, message: string }", "Dependencies": "1. MongoDB: For storing user data and profiles. 2. Redis: For caching user sessions and improving performance. 3. JWT: For secure token-based authentication. 4. Express.js: As the web framework for building the RESTful API.", "Title": "User Management Module Interactions", "configuration_state": "configured", "test_cases_config_state": "configured", "behavior_state": "configured", "behavior_config_state": "configured", "component_interactions_state": "configured", "FunctionalRequirements": "1. User Registration: Allow users to create an account with unique credentials. 2. User Authentication: Validate user credentials and issue JWT for session management. 3. Profile Management: Enable users to view and update their profile information. 4. Password Management: Allow users to reset their passwords securely. 5. Notifications: Provide users with notifications regarding account activities.", "NonFunctionalRequirements": "1. Performance: The module should handle up to 1000 concurrent users with response times under 200ms. 2. Scalability: The architecture must support horizontal scaling to accommodate growing user base. 3. Security: Implement strong encryption for sensitive data and secure authentication mechanisms. 4. Availability: The service should have 99.9% uptime with proper failover mechanisms in place.", "specification_state": "configured", "LinkedFigmaFrames": "[{\"id\": \"366:2080\", \"name\": \"sign up\", \"file_key\": \"atvZngn3w0bFH9gXtigpGt\", \"imageUrl\": \"https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/f1ec27f7-c3a5-4b72-9ab6-b5ba24e9b54b\", \"thumbnailUrl\": \"https://api.figma.com/v1/images/atvZngn3w0bFH9gXtigpGt?ids=366:2080&scale=0.5&format=png\"}, {\"id\": \"207:0\", \"name\": \"brand\", \"file_key\": \"atvZngn3w0bFH9gXtigpGt\", \"imageUrl\": \"https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/5e4488e1-cb84-44f0-9329-fc3c96a103c4\", \"thumbnailUrl\": \"https://api.figma.com/v1/images/atvZngn3w0bFH9gXtigpGt?ids=207:0&scale=0.5&format=png\"}]", "component_interactions_config_state": "configured", "class_diagrams_config_state": "configured", "testcases_state": "configured"}, "ClassDiagram": ["{\"Type\": \"ClassDiagram\", \"Details\": \"classDiagram\\n    class User {\\n        +String userId\\n        +String name\\n        +String email\\n        +getUserData()\\n    }\\n    class ScreenTimeData {\\n        +String userId\\n        +int totalScreenTime\\n        +int dailyAverage\\n        +getTrends()\\n    }\\n    class AnalyticsReport {\\n        +String userId\\n        +List<ScreenTimeData> screenTimeData\\n        +generateReport()\\n    }\\n    class Notification {\\n        +String userId\\n        +String message\\n        +sendNotification()\\n    }\\n    User --\\n    ScreenTimeData : provides\\n    User --\\n    AnalyticsReport : generates\\n    User --\\n    Notification : sends\\n    ScreenTimeData --\\n    AnalyticsReport : includes\\n    Notification --\\n    User : notifies\", \"Title\": \"User Management to Screen Time Tracking\", \"Diagram\": \"classDiagram\\n    class User {\\n        +String userId\\n        +String name\\n        +String email\\n        +getUserData()\\n    }\\n    class ScreenTimeData {\\n        +String userId\\n        +int totalScreenTime\\n        +int dailyAverage\\n        +getTrends()\\n    }\\n    class AnalyticsReport {\\n        +String userId\\n        +List<ScreenTimeData> screenTimeData\\n        +generateReport()\\n    }\\n    class Notification {\\n        +String userId\\n        +String message\\n        +sendNotification()\\n    }\\n    User --> ScreenTimeData : provides\\n    User --> AnalyticsReport : generates\\n    User --> Notification : sends\\n    ScreenTimeData --> AnalyticsReport : includes\\n    Notification --> User : notifies\"}"], "Algorithm": ["{\"Type\": \"Algorithm\", \"Details\": \"1. Receive user input (username, password, email, preferences).\\n2. Validate input data.\\n3. Check if username or email already exists.\\n4. Hash the password.\\n5. Store user data in MongoDB.\\n6. Return success message or error.\", \"Title\": \"User Registration\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Receive login credentials (username, password).\\n2. Validate input data.\\n3. Retrieve user data from MongoDB.\\n4. Compare hashed password with stored hash.\\n5. Generate JWT token if authentication is successful.\\n6. Return token and user ID.\", \"Title\": \"User Authentication\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Receive user ID and updated data.\\n2. Validate input data.\\n3. Update user profile in MongoDB.\\n4. Return success message or error.\", \"Title\": \"Profile Management\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Receive email for password reset.\\n2. Validate email existence in the database.\\n3. Generate password reset token.\\n4. Send email with reset instructions.\\n5. Update password in the database after token validation.\", \"Title\": \"Password Reset\"}", "{\"Type\": \"Algorithm\", \"Details\": \"1. Receive notification request (user ID, message).\\n2. Validate user ID.\\n3. Store notification in the database.\\n4. Send notification to user via preferred method (email, push notification).\", \"Title\": \"Notification Management\"}"], "Sequence": ["{\"Description\": \"This sequence diagram illustrates the interaction between the User Management Module and the Screen Time Tracking Module, detailing how user data is fetched and utilized for screen time tracking.\", \"Type\": \"Sequence\", \"Title\": \"User Management to Screen Time Tracking Sequence\", \"Diagram\": \"sequenceDiagram\\n    participant User\\n    participant UserManagement\\n    participant ScreenTimeTracking\\n    User->>UserManagement: Request User Data\\n    UserManagement->>ScreenTimeTracking: Fetch Screen Time Data\\n    ScreenTimeTracking-->>UserManagement: Return Screen Time Data\\n    UserManagement-->>User: Display Screen Time Data\"}", "{\"Description\": \"This sequence diagram shows the interaction between the User Management Module and the Analytics Dashboard Module, focusing on how user data is provided for analytics purposes.\", \"Type\": \"Sequence\", \"Title\": \"User Management to Analytics Dashboard Sequence\", \"Diagram\": \"sequenceDiagram\\n    participant User\\n    participant UserManagement\\n    participant AnalyticsDashboard\\n    User->>UserManagement: Request Analytics Data\\n    UserManagement->>AnalyticsDashboard: Provide User Data\\n    AnalyticsDashboard-->>UserManagement: Return Analytics Data\\n    UserManagement-->>User: Display Analytics Data\"}"], "StateDiagram": ["{\"Description\": \"This state diagram represents the internal states of the User Management Module as it processes user interactions and data requests.\", \"Type\": \"StateDiagram\", \"Title\": \"User Management State Diagram\", \"Diagram\": \"stateDiagram-v2\\n    [*] --> Idle: System Start\\n    Idle --> FetchingData: User Requests Data\\n    FetchingData --> DataReady: Data Retrieved\\n    DataReady --> Idle: Data Displayed\\n    FetchingData --> Error: Data Fetch Failed\\n    Error --> Idle: Reset\"}"], "UnitTest": ["{\"Description\": \"Test the API endpoint that provides user data to the Screen Time Tracking Module. Input valid user ID and check if the correct user data is returned.\", \"Type\": \"UnitTest\", \"ExpectedResult\": \"The API returns a 200 OK status with the correct user data in JSON format.\", \"Title\": \"User Management to Screen Time Tracking - Unit Test\"}"], "IntegrationTest": ["{\"Description\": \"Test the integration between User Management and Analytics Dashboard. Ensure that user data is correctly fetched and displayed in the dashboard.\", \"Type\": \"IntegrationTest\", \"ExpectedResult\": \"The Analytics Dashboard displays the correct user data and analytics without errors.\", \"Title\": \"User Management to Analytics Dashboard - Integration Test\"}"], "PerformanceTest": ["{\"Description\": \"Test the performance of the notification management system under high load. Simulate 1000 concurrent notification requests.\", \"Type\": \"PerformanceTest\", \"ExpectedResult\": \"The system should handle all requests with a response time of less than 200ms.\", \"Title\": \"User Management Notification Management - Performance Test\"}"], "RobustnessTest": ["{\"Description\": \"Test the error handling of the User Management Module when invalid user ID is provided. Input an invalid user ID and check the response.\", \"Type\": \"RobustnessTest\", \"ExpectedResult\": \"The API returns a 404 Not Found status with a meaningful error message.\", \"Title\": \"User Management Error Handling - Robustness Test\"}"]}