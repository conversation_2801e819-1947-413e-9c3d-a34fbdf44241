from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.node_version_manager import NodeVersionManager
from app.telemetry.logger_config import get_logger
from datetime import datetime
import json

class SystemContextContainersDiscussion(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="SystemContext")
        # Template will be set during retrieve_info based on architecture pattern
        self.template_name = None
        self.function_schema_type = "SystemContextContainersConfiguration"
        self.version_manager = NodeVersionManager()
        self.update_logger = get_logger(__name__)

    async def get_relationships(self, root_node_id):
        child_relationships = await self.db.get_all_relationships(root_node_id, "HAS_CHILD", self.node_type, 3)
        interface_relationships = await self.db.get_all_relationships(root_node_id, "INTERFACES_WITH", self.node_type, 3)
        return child_relationships + interface_relationships

    async def retrieve_info(self):
        """Retrieve context information for system context containers"""
        await super().retrieve_info()
        try:
            # Get project details
            project_node = self.retrieved_info['root_node']
            self.retrieved_info['project_details'] = project_node.get('properties', {})
            
            # Get architecture pattern and set appropriate template
            architecture_pattern = project_node['properties'].get('ArchitecturePattern', '').lower()
            self.retrieved_info['architecture_pattern'] = architecture_pattern
            
            # Set template based on architecture pattern
            if architecture_pattern in ['monolithic-application', 'monolithic-service']:
                self.template_name = "monolithic_system_context_container.prompt"
                self.update_logger.info(f"Using monolithic system context container template for {architecture_pattern}")
            elif architecture_pattern == 'multi-container-service':
                self.template_name = "multicontainer_service_containers.prompt"
                self.update_logger.info(f"Using multi-container service containers template for {architecture_pattern}")
            else:
                self.template_name = "system_context_containers.prompt"
                self.update_logger.info("Using standard system context containers template")

            # Get requirements context
            requirements_context = {
                'functional_requirements': [],
                'architectural_requirements': []
            }

            # Get architectural requirements root
            architecture_root = await self.db.get_child_nodes(project_node['id'], "ArchitectureRoot")
            if architecture_root:
                arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
                if arch_req_nodes:
                    # Get full requirement nodes
                    functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                    nonfunctional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "NonFunctionalRequirement")
                    
                    requirements_context = {
                        'functional_requirements': functional_reqs,
                        'architectural_requirements': nonfunctional_reqs
                    }

            self.retrieved_info['requirements_context'] = requirements_context

            # Get existing containers and their relationships if this is a reconfiguration
            if self.is_reconfig():
                system_context = await self.db.get_node_by_id(self.node_id)
                
                # Get containers
                containers = await self.db.get_child_nodes(self.node_id, "Container")
                self.retrieved_info['containers'] = containers

                # Get existing interfaces
                existing_interfaces = []
                for container in containers:
                    interfaces = await self.get_relationships(container['id'])
                    existing_interfaces.extend(interfaces)
                self.retrieved_info['interfaces'] = existing_interfaces

                # Get users and external systems for monolithic context
                self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
                self.retrieved_info['users'] = system_context['properties'].get('Users', '')

                # Structure current background info
                current_background_info = {
                    'project_context': {
                        'node_id': project_node['id'],
                        'properties': project_node['properties']
                    },
                    'system_context': {
                        'containers': containers,
                        'interfaces': existing_interfaces,
                        'users': self.retrieved_info['users'],
                        'external_systems': self.retrieved_info['external_systems']
                    },
                    'requirements_context': requirements_context
                }

                # Handle reconfiguration background info
                stored_background_info = await self.version_manager._get_context(self.node_id)
                if stored_background_info:
                    self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                    })
                    # Add safety check
                    if not self.retrieved_info['background_info']:
                        self.retrieved_info['background_info'] = current_background_info
                else:
                    self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                self.retrieved_info['new_background'] = current_background_info
            else:
                # For initial configuration
                system_context = await self.db.get_node_by_id(self.node_id)
                self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
                self.retrieved_info['users'] = system_context['properties'].get('Users', '')
                
                current_background_info = {
                    'project_context': {
                        'node_id': project_node['id'],
                        'properties': project_node['properties']
                    },
                    'system_context': {
                        'users': self.retrieved_info['users'],
                        'external_systems': self.retrieved_info['external_systems']
                    },
                    'requirements_context': requirements_context
                }
                self.retrieved_info['background_info'] = current_background_info
                self.retrieved_info['new_background'] = current_background_info

            return self.retrieved_info

        except Exception as e:
            self.update_logger.error(f"Error retrieving system context containers info: {str(e)}")
            raise

    def get_modifications_from_llm_output(self):
        """Process LLM output to extract modifications"""
        # Begin with Container as a base child node type for all architecture patterns
        self.modifications['child_node_types'] = ['Container']
        
        # Check architecture pattern to determine if Interface should be included
        architecture_pattern = self.retrieved_info.get('architecture_pattern', '').lower()
        is_monolithic = architecture_pattern in ['monolithic-application', 'monolithic-service']
        is_multicontainer = architecture_pattern == 'multi-container-service'
        
        # Add Interface as child node type for monolithic service and multi-container service
        if (is_monolithic and architecture_pattern == 'monolithic-service') or is_multicontainer:
            self.modifications['child_node_types'].append('Interface')
        
        # Extract container information from LLM output
        llm_output = self.modifications.get('modified_node', {})
        self.modifications['containers'] = llm_output.get('Containers', [])
        self.modifications['new_relationships'] = self.modifications.get('new_relationships', [])
        
        # For monolithic service, extract interfaces if available
        if is_monolithic and architecture_pattern == 'monolithic-service':
            self.modifications['interfaces'] = llm_output.get('Interfaces', [])
        
        # For multi-container service, extract interfaces
        if is_multicontainer:
            self.modifications['interfaces'] = llm_output.get('Interfaces', [])
        
        return self.modifications

    async def merge_captured_items(self):
        """Merge captured items into the database"""
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return

        await self.retrieve_info()
        user_inputs = self.modifications['modified_node'].get('user_inputs', '')
        change_reason = self.modifications['modified_node'].get('change_reason', '')

        try:
            # Check if this is a monolithic system
            architecture_pattern = self.retrieved_info.get('architecture_pattern', '').lower()
            is_monolithic = architecture_pattern in ['monolithic-application', 'monolithic-service']
            is_multicontainer = architecture_pattern == 'multi-container-service'
            
            # Set containers configuration state
            self.modifications['modified_node']['containers_config_state'] = 'configured'
            
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': datetime.now().isoformat(),
                            'action': 'reconfig',
                            'input': user_inputs,
                            'reason': change_reason
                        }
                    }
                    await self.version_manager.save_node_info(save_data)

                    # Handle container relationships
                    created_nodes = self.modifications.get('created_nodes', [])
                    for node in created_nodes:
                        if "Container" in node.get('labels', []):
                            await self.db.create_relationship(
                                self.node_id,
                                node['id'],
                                "CONTAINS",
                                {"description": f"System Context contains {node['properties'].get('Title', 'Container')}"}
                            )

                    # For monolithic service, handle interfaces specially
                    if is_monolithic and architecture_pattern == 'monolithic-service':
                        await self.handle_monolithic_service_interfaces(created_nodes)
                    elif is_multicontainer:
                        # Handle container interfaces for multi-container service
                        await self.handle_container_interfaces()
                    else:
                        # Handle container interfaces
                        interfaces = self.modifications.get('new_relationships', [])
                        if interfaces:
                            for interface in interfaces:
                                source_id = interface.get('source')
                                target_id = interface.get('target')
                                if source_id and target_id:
                                    await self.db.create_relationship(
                                        source_id,
                                        target_id,
                                        "INTERFACES_WITH",
                                        {
                                            'name': interface.get('name', 'Unnamed Interface'),
                                            'description': interface.get('description', 'Description Not available'),
                                            'interfaceType': interface.get('interfaceType', 'Unknown')
                                        }
                                    )
            else:
                await super().merge_captured_items()
                
                # Save version info for initial configuration
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': datetime.now().isoformat(),
                        'action': 'initial_config',
                        'input': user_inputs,
                        'reason': change_reason
                    }
                }
                await self.version_manager.save_node_info(save_data)

                # Handle container relationships and interfaces same as reconfig
                created_nodes = self.modifications.get('created_nodes', [])
                for node in created_nodes:
                    if "Container" in node.get('labels', []):
                        await self.db.create_relationship(
                            self.node_id,
                            node['id'],
                            "CONTAINS",
                            {"description": f"System Context contains {node['properties'].get('Title', 'Container')}"}
                        )

                # For monolithic service, handle interfaces specially
                if is_monolithic and architecture_pattern == 'monolithic-service':
                    await self.handle_monolithic_service_interfaces(created_nodes)
                elif is_multicontainer:
                    # Handle container interfaces for multi-container service
                    await self.handle_container_interfaces()
                else:
                    # Handle container interfaces
                    interfaces = self.modifications.get('new_relationships', [])
                    if interfaces:
                        for interface in interfaces:
                            source_id = interface.get('source')
                            target_id = interface.get('target')
                            if source_id and target_id:
                                await self.db.create_relationship(
                                    source_id,
                                    target_id,
                                    "INTERFACES_WITH",
                                    {
                                        'name': interface.get('name', 'Unnamed Interface'),
                                        'description': interface.get('description', 'Description Not available'),
                                        'interfaceType': interface.get('interfaceType', 'Unknown')
                                    }
                                )

        except Exception as e:
            self.update_logger.error(f"Error in merge_captured_items: {str(e)}")
            raise

    async def handle_monolithic_service_interfaces(self, created_nodes):
        """Handle interfaces for monolithic service container"""
        try:
            # Find the internal container
            containers = await self.db.get_child_nodes(self.node_id, "Container")
            internal_container = None
            
            for container in containers:
                if container['properties'].get('ContainerType', '') == 'internal':
                    internal_container = container
                    break
                    
            # If no internal container found in database, look in created nodes
            if not internal_container:
                for node in created_nodes:
                    if "Container" in node.get('labels', []) and node['properties'].get('ContainerType', '') == 'internal':
                        internal_container = node
                        break
            
            if not internal_container:
                self.update_logger.warning("No internal container found for monolithic service interfaces")
                return
                
            # Create interface node for the container
            interface_node = await self.db.create_node(
                ["Interface"],
                {
                    "Title": "Public API Interfaces",
                    "Description": "External APIs exposed by the monolithic system"
                },
                internal_container['id']
            )
            
            # Get interfaces from modifications
            interfaces = self.modifications.get('interfaces', [])
            
            # Transform interfaces into the expected format
            incoming_interfaces = []
            for interface in interfaces:
                if isinstance(interface, dict):
                    incoming_interface = {
                        'name': interface.get('name', 'Unnamed Interface'),
                        'description': interface.get('description', 'Description Not Available'),
                        'source_component_id': internal_container['id'],
                        'interfaceType': interface.get('interfaceType','Unknown')
                    }
                    incoming_interfaces.append(incoming_interface)
            
            # Update the interface node with all interfaces
            if incoming_interfaces:
                await self.db.update_node_by_id(
                    interface_node['id'],
                    {"incoming_interfaces": json.dumps(incoming_interfaces)}
                )
                
            self.update_logger.info(f"Created interface node with {len(incoming_interfaces)} interfaces for monolithic container")
            
        except Exception as e:
            self.update_logger.error(f"Error handling monolithic service interfaces: {str(e)}")
            raise

    async def handle_container_interfaces(self):
        """Handle interfaces for multi-container service"""
        try:
            # Get interfaces from modifications
            interfaces = self.modifications.get('interfaces', [])
            if interfaces:
                for interface in interfaces:
                    source_id = interface.get('source')
                    target_id = interface.get('target')
                    if source_id and target_id:
                        await self.db.create_relationship(
                            source_id,
                            target_id,
                            "INTERFACES_WITH",
                            {
                                'name': interface.get('name', 'Unnamed Interface'),
                                'description': interface.get('description', 'Description Not available'),
                                'interfaceType': interface.get('interfaceType', 'Unknown')
                            }
                        )
            
            self.update_logger.info(f"Created {len(interfaces)} interface relationships for multi-container service")
            
        except Exception as e:
            self.update_logger.error(f"Error handling multi-container service interfaces: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'

    def get_config_state(self, discussion_type, node_type, node):
        """Get configuration state for the specific discussion type."""
        if not node or 'properties' not in node:
            return 'not_configured'
            
        properties = node['properties']
        if discussion_type == 'system_context_containers':
            return properties.get('containers_config_state', 'not_configured')
        else:
            return properties.get('config_state', 'not_configured')

    def set_config_state(self, node_properties):
        """Set configuration state for the specific discussion type."""
        if self.discussion_type == 'system_context_containers':
            node_properties.setdefault('containers_config_state', 'configured')
        else:
            node_properties.setdefault('config_state', 'configured')
        return node_properties

# Register the discussion type
DiscussionFactory.register('system_context_containers', SystemContextContainersDiscussion, "SystemContext") 