from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from datetime import datetime
from app.utils.datetime_utils import generate_timestamp

class DesignBehavior(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Design")
        self.template_name = "design_behavior.prompt"
        self.function_schema_type = "DesignBehavior"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def async_initialize(self):
        # Initialization specific to this configuration
        await super().async_initialize()
        # Additional async setup specific to this class can be added here


    async def get_interface_details(self, component_id):
        """Get comprehensive interface details for a component"""
        
        # Get Interface nodes
        interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
        
        # Get interface relationships 
        interface_relationships = await self.db.get_relationships_involving_node(
            component_id, 
            "INTERFACES_WITH"
        )
        
        interfaces = []
        for interface in interface_relationships:
            interface_info = {
                'source_id': interface['source'],
                'target_id': interface['target'],
                'properties': interface['properties']
            }
            
            # Get source node details if this component is target
            if interface['target'] == component_id:
                interface_info['source_node'] = await self.db.get_node_by_id(interface['source'])
                
            # Get target node details if this component is source    
            if interface['source'] == component_id:
                interface_info['target_node'] = await self.db.get_node_by_id(interface['target'])
                
            # Get interface node details if specified
            interface_node_id = interface['properties'].get('interface_node_id')
            if interface_node_id:
                interface_info['interface_node'] = await self.db.get_node_by_id(interface_node_id)
                
            interfaces.append(interface_info)
            
        return {
            'interface_nodes': interface_nodes,
            'interface_relationships': interfaces
        }

    ## Update DesignComponentInteractions.retrieve_info():
    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Get parent component
        parent_component = await self.db.get_parent_node(self.node_id)
        if parent_component:
            self.retrieved_info['parent_component'] = parent_component
            
            # Get comprehensive interface details
            interface_details = await self.get_interface_details(parent_component['id'])
            self.retrieved_info['interface_details'] = interface_details
        
        # Get remaining architectural info as before
        project_id = self.retrieved_info['root_node']['id']
        arch_roots = await self.db.get_child_nodes(project_id, "ArchitectureRoot")
        if arch_roots:
            arch_root_id = arch_roots[0]['id']
            arch_info = await self.get_architectural_node_info(arch_root_id, parent_component['id'])
            self.retrieved_info.update(arch_info)
        
         # Get existing algorithms and state logic
        algorithms = await self.db.get_child_nodes(self.node_id, "Algorithm")
        
        if algorithms:
            self.retrieved_info['existing_algorithms'] = [{
                'title': algo['properties'].get('Title', ''),
                'details': algo['properties'].get('Details', ''),
                'description': algo['properties'].get('Description', '')
            } for algo in algorithms]
        
         # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']

        existing_session = await get_or_create_session(self.current_user, self.project_id)
        self.doc_session_id = existing_session.get("session_id")

        print(self.doc_session_id)


        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_node:
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        design_node = await self.db.get_node_by_id(self.node_id)
        
        # Retrieve existing external systems and users
        system_context = self.retrieved_info['current_node']
        self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
        self.retrieved_info['users'] = system_context['properties'].get('Users', '')

        # Retrieve existing containers
        containers = await self.db.get_child_nodes(system_context['id'], "Container")
        parent_component_node = await self.db.get_parent_node(self.node_id)
        parent_container_node = await self.db.get_parent_node(parent_component_node['id'])
        components = await self.db.get_child_nodes(parent_container_node['id'], "Component")

        container_component_details = []
        container_component_details.append({
            'container': parent_container_node['properties'],
            'components': components
        })

        design_node = await self.db.get_node_by_id(self.node_id)
        algorithms = design_node['properties'].get('Algorithms', '')
        class_diagrams = await self.db.get_child_nodes(self.node_id, "ClassDiagram") or None
        state_diagrams = await self.db.get_child_nodes(self.node_id, "StateDiagram") or None
        sequence_diagrams = await self.db.get_child_nodes(self.node_id, "SequenceDiagram") or None
        test_cases = await self.db.get_child_nodes(self.node_id, "Test") or None

        # Structure current background info with complete node information
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'system_context': {
                'users': self.retrieved_info['users'],
                'external_systems': self.retrieved_info['external_systems'],
                'parent_node': parent_component_node,  # Already storing full container nodes
                'container_component_details': container_component_details,
            },
            'requirements_context': {
                'functional_requirements': architectural_reqs['functional_requirements'],
                'architectural_requirements': architectural_reqs['architectural_requirements'],
                'parent_node': arch_req_node[0] if arch_req_node else None  # Store parent node for context
            },
            'design_context': {
                'design_node': design_node['properties'],
                'algorithms': algorithms,
                'state_diagrams': state_diagrams,
                'sequence_diagrams': sequence_diagrams,
                'class_diagrams': class_diagrams,   
                'test_cases': test_cases

            }
        }

        # Handle reconfiguration
        if self.is_reconfig():
            self.model_name = 'gpt-4o'
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info
    

    def get_modifications_from_llm_output(self):

       # super().get_modifications_from_llm_output(node_type, root_node_type, discussion_type, llm_output)

        #child_nodes = llm_output.get("AlgorithmicDetails", [])
        #child_nodes = child_nodes + llm_output.get("StateManagementLogic", [])
        self.modifications['child_node_types'] = ["Algorithm", "StateLogic"]

        return self.modifications      

    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
                await super().merge_captured_items()
                # Save version info
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'reconfig',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)
        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
# Registering the subclass in the factory
DiscussionFactory.register('behavior', DesignBehavior, "Design")