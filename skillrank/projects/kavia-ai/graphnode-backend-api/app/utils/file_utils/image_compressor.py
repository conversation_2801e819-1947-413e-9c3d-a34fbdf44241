import os
from PIL import Image
import io
import base64
from typing import Tuple, Optional
import requests


def compress_image(image_data: bytes, target_ratio: float = 0.5) -> Tuple[bytes, str, float]:
    """
    Compresses an image to target size ratio using multiple techniques.
    
    Args:
        image_data (bytes): The original image data
        target_ratio (float): Target compression ratio (0-1)
        
    Returns:
        Tuple[bytes, str, float]: Compressed image data, content type, and compression ratio
    """
    original_size = len(image_data)
    
    # If image is already small, use more aggressive compression
    small_image_threshold = 100 * 1024  # 100KB
    is_small_image = original_size < small_image_threshold
    
    try:
        # Open the image
        img = Image.open(io.BytesIO(image_data))
        original_format = img.format or 'JPEG'
        content_type = f"image/{original_format.lower()}"
        
        # Try multiple compression techniques and keep the smallest result
        best_compressed_data = image_data
        best_ratio = 1.0
        
        # Technique 1: Quality reduction for JPEGs
        if original_format in ['JPEG', 'JPG']:
            # Use more aggressive quality reduction for small images
            quality_levels = [50, 40, 30, 20] if is_small_image else [70, 60, 50, 40]
            
            for quality in quality_levels:
                output = io.BytesIO()
                img.save(output, format='JPEG', quality=quality, optimize=True)
                output.seek(0)
                compressed_data = output.getvalue()
                ratio = len(compressed_data) / original_size
                
                # If we've reached our target or have a better result, update our best
                if ratio <= best_ratio:
                    best_compressed_data = compressed_data
                    best_ratio = ratio
                    content_type = "image/jpeg"
                
                # If we've hit our target, we can stop
                if ratio <= target_ratio:
                    break
        
        # Technique 2: PNG optimization with quantization
        if original_format == 'PNG' or best_ratio > target_ratio:
            # Convert to palette mode (8-bit) if the image has suitable content
            if img.mode in ['RGB', 'RGBA'] and not img.mode == 'P':
                try:
                    # Quantize to reduce colors to 256
                    img_quant = img.convert('P', palette=Image.ADAPTIVE, colors=256)
                    
                    output = io.BytesIO()
                    img_quant.save(output, format='PNG', optimize=True)
                    output.seek(0)
                    compressed_data = output.getvalue()
                    ratio = len(compressed_data) / original_size
                    
                    if ratio < best_ratio:
                        best_compressed_data = compressed_data
                        best_ratio = ratio
                        content_type = "image/png"
                except Exception as e:
                    print(f"PNG palette conversion failed: {str(e)}")
        
        # Technique 3: Resize if we still haven't hit the target
        if best_ratio > target_ratio:
            # Calculate scale factor based on target
            scale_factor = max(0.1, target_ratio)  # Don't go below 10%
            
            # Resize the image
            width, height = img.size
            new_width = max(1, int(width * scale_factor))
            new_height = max(1, int(height * scale_factor))
            
            # Use high-quality downsampling
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # Try saving in both JPEG and PNG formats and pick the smaller one
            output_jpg = io.BytesIO()
            if img.mode in ['RGB', 'RGBA', 'L']:
                if img.mode == 'RGBA':
                    # Convert RGBA to RGB with white background for JPEG
                    bg = Image.new('RGB', resized_img.size, (255, 255, 255))
                    bg.paste(resized_img, mask=resized_img.split()[3])
                    bg.save(output_jpg, format='JPEG', quality=50, optimize=True)
                else:
                    resized_img.convert('RGB').save(output_jpg, format='JPEG', quality=50, optimize=True)
                
                output_jpg.seek(0)
                jpg_data = output_jpg.getvalue()
                jpg_ratio = len(jpg_data) / original_size
                
                if jpg_ratio < best_ratio:
                    best_compressed_data = jpg_data
                    best_ratio = jpg_ratio
                    content_type = "image/jpeg"
            
            output_png = io.BytesIO()
            resized_img.save(output_png, format='PNG', optimize=True)
            output_png.seek(0)
            png_data = output_png.getvalue()
            png_ratio = len(png_data) / original_size
            
            if png_ratio < best_ratio:
                best_compressed_data = png_data
                best_ratio = png_ratio
                content_type = "image/png"
        
        # Always ensure we're not making the file larger
        if best_ratio >= 1.0:
            return image_data, content_type, 1.0
            
        return best_compressed_data, content_type, best_ratio
        
    except Exception as e:
        print(f"Error compressing image: {str(e)}")
        return image_data, "image/jpeg", 1.0
    
def download_and_compress_image(image_url: str, target_ratio: float = 0.5) -> Tuple[str, Optional[str]]:
    """
    Downloads an image from a URL, compresses it, and returns it as a base64 string.
    Falls back to the original URL if download or compression fails.
    
    Args:
        image_url (str): The URL of the image to download
        target_ratio (float): Target compression ratio for the image (0-1)
        
    Returns:
        Tuple[str, Optional[str]]: Base64-encoded compressed image data and content type,
                                  or (original_url, None) if the process fails
    """
    try:
        # Download the image
        response = requests.get(image_url, timeout=10)
        response.raise_for_status()  # Raise an exception for HTTP errors
        
        # Compress the image
        image_data = response.content
        compressed_data, content_type, compression_ratio = compress_image(image_data, target_ratio)
        
        # Convert to base64
        base64_data = base64.b64encode(compressed_data).decode('utf-8')
        
        # Log compression results
        original_size = len(image_data) / 1024  # KB
        compressed_size = len(compressed_data) / 1024  # KB
        print(f"Image compressed: {original_size:.2f}KB → {compressed_size:.2f}KB (ratio: {compression_ratio:.2f})")
        
        # Return as data URL
        return f"data:{content_type};base64,{base64_data}", content_type
    except Exception as e:
        print(f"Error downloading/compressing image from {image_url}: {str(e)}")
        # Return the original URL if anything fails
        return image_url, None
    

def test_small_image_compression():
    """Test function specifically for small images"""
    
    def test_file(image_path):
        with open(image_path, 'rb') as f:
            original_data = f.read()
        
        original_size = len(original_data)
        print(f"\nTesting: {image_path} - Original size: {original_size/1024:.2f} KB")
        
        compressed_data, content_type, ratio = compress_image(original_data, 0.5)
        compressed_size = len(compressed_data)
        
        print(f"Compressed size: {compressed_size/1024:.2f} KB")
        print(f"Compression ratio: {ratio:.2%}")
        print(f"Size change: {(compressed_size - original_size)/1024:.2f} KB")
        
        # # Save the compressed file for inspection
        # filename, ext = os.path.splitext(image_path)
        # if content_type == "image/jpeg":
        #     save_ext = ".jpg"
        # elif content_type == "image/png":
        #     save_ext = ".png"
        # else:
        #     save_ext = ext
            
        # compressed_path = f"{filename}_compressed{save_ext}"
        # with open(compressed_path, 'wb') as f:
        #     f.write(compressed_data)
        
        # print(f"Saved to: {compressed_path}")
        return original_size, compressed_size, ratio
    
    # Test on a provided image or let user input a path
    if __name__ == "__main__":
        import sys
        
        if len(sys.argv) > 1:
            test_file(sys.argv[1])
        else:
            path = input("Enter path to a small image file: ")
            test_file(path)

# Run the test
test_small_image_compression()