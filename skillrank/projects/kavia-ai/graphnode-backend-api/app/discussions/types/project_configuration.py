from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
# system_context_configuration.py
from app.core.Settings import settings
from datetime import datetime
import os
from app.connection.establish_db_connection import get_mongo_db
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
from app.llm import LLMInterface
from app.utils.kg_inspect.kg_tool import KgTools
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.kg_inspect.knowledge import Knowledge
from app.utils.prodefn.docs_tool import DocsTools
import boto3
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id

from app.core.Settings import settings
from app.utils.logs_utils import get_path  # Import settings
import asyncio
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.node_version_manager import NodeVersionManager
from app.utils.datetime_utils import generate_timestamp
class ProjectConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Project")
        self.template_name = "project_configuration.prompt"
        self.function_schema_type = "ProjectConfiguration"

        # self.model_name = 'claude-3-5-haiku-20241022'
        # self.model_name = 'claude-3-5-sonnet-20241022'
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.s3_client = boto3.client('s3',
                          aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                          region_name=settings.AWS_REGION
                          )
        self.tenant_id = None
        self.version_manager = NodeVersionManager()

    async def async_initialize(self):
        # Initialization specific to this configuration
        await super().async_initialize()
        # Additional async setup specific to this class can be added here

    async def retrieve_info(self):
        await super().retrieve_info()
        
         # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']

        # Structure current background info
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            }
        }

        # Handle reconfiguration
        if self.is_reconfig():
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info
        
        await self.check_reconfiguration_needs()

        return self.retrieved_info



    async def merge_captured_items(self):
         # Get background info by calling retrieve_info again
        await self.retrieve_info()
        user_inputs = self.modifications['modified_node'].get('user_inputs', '')
        change_reason = self.modifications['modified_node'].get('change_reason', '')
        try:
            if self.is_reconfig():
                # if self.modifications['modified_node'].get('changes_needed', False):
                await super().merge_captured_items()
                # Get user inputs and change reason from LLM output
                
                
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'reconfig',
                        'input': user_inputs,  # Use LLM output's user_inputs
                        'reason': change_reason  # Use LLM output's change_reason
                    }
                }
                await self.version_manager.save_node_info(save_data)
                
                if getattr(self, 'is_flagged_for_reconfig', False):
                    await self.version_manager.clear_reconfig_flag(self.node_id)

                # Flag downstream nodes that may need reconfiguration due to our changes
                await self.version_manager.flag_downstream_nodes(
                    self.node_id,
                    f"Parent {self.node_type} was modified: {change_reason}"
                )
            else:
               
                await super().merge_captured_items()
                 # First create the node using super().merge_captured_items()
                save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'initial_reconfig',
                            'input': user_inputs,  # Use LLM output's user_inputs
                            'reason': change_reason  # Use LLM output's change_reason
                        }
                    }
                await self.version_manager.save_node_info(save_data)
                
            self.update_logger.info(f"Successfully saved changes to node {self.node_id}")
                
        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise
    
    def get_modifications_from_llm_output(self):
        pass

        #self.modifications['modified_node'] = llm_output.get('Modified-Node')
       # return self.modifications

    def is_reconfig(self):
            """
            Check if this is a reconfiguration scenario.
            
            Returns True if:
            1. Discussion type is 'interactive' or 'auto' AND
            2. Node is already configured (has configuration state)
            """
            #self.model_name = 'gpt-4o'
            config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
            is_configured = config_state == 'configured'
            
            self.update_logger.info(f"Reconfig check - Type: {self.discussion_type}, State: {config_state}")

            
            return is_configured 
# Registering the subclass in the factory
DiscussionFactory.register('configuration', ProjectConfiguration, "Project")