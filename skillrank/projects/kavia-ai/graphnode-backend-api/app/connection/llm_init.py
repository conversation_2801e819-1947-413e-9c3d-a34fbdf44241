import app.llm.LLMInterface as LLMInterface
from app.core.Settings import settings


initialized = False

def connect_llm():
    """Connect to the LLM."""
    global initialized
    LLMInterface.init_interface(settings.OPENAI_API_KEY)
    initialized = True
    return LLMInterface

def get_llm_interface():
    """Get the LLMInterface instance."""
    if not initialized:
        connect_llm()

    return LLMInterface