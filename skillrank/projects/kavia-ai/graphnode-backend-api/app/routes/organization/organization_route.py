from fastapi import APIRouter
from app.models.organization_models import Plan, Configurations, Organization, Group, User
from app.models.organization_models import generate_org_id, generate_group_id, generate_user_id
from app.models.organization_models import Permissions
from pydantic import BaseModel
from app.utils.aws.cognito_main import TenantService
from typing import List, Dict, Optional
from app.utils.aws.cognito_group_manager import CognitoGroupManager
from app.utils.aws.cognito_main import TenantService
from app.utils.aws.cognito_user_manager import CognitoUserManager
from fastapi import HTTPException, Query
from app.routes.organization.super_route import random_password
from app.routes.authentication_route import add_user_node_db
from app.connection.tenant_middleware import get_tenant_id
from app.core.Settings import settings
from app.utils.auth_utils import get_current_user
from fastapi import Depends
import re
import urllib.parse
from app.utils.hash import encrypt_tenant_id
from fastapi import Request

router = APIRouter(
    prefix="/manage/org",
    tags=["Organization Management"],
    responses={404: {"description": "Not found"}}
)

tenant_service = TenantService()

class GroupCreate(BaseModel):
    name: str
    description: str
    permissions : Dict[str, Dict[str, bool]]
    group_type: str
    access_level: str = "Viewer"

class UserCreate(BaseModel):
    email: str
    name: str
    contact_number: str
    temporary_password: str
    department: str
    is_admin: bool = False

@router.get("/referred/users/")
async def get_all_referred_users(
    latest: Optional[bool] = Query(default=True),
    organization_filter: Optional[str] = Query(default=None, description="Filter by organization ID")
):
    """Get all referred users across organizations (Super admin only)"""
    current_tenant = get_tenant_id()
    
    # Only super admin can access this endpoint
    if current_tenant != settings.KAVIA_SUPER_TENANT_ID:
        raise HTTPException(status_code=403, detail="Super admin access required")
    
    try:
        if organization_filter:
            # Get users for specific organization
            users = await Organization.get_users_with_referral_data(
                organization_filter, 
                include_inactive=True, 
                latest=latest
            )
        else:
            # Get all referred users across all organizations
            users = await Organization.get_all_users_with_referral_data(latest=latest)
        
        return users
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{organization_id}")
async def get_organization(organization_id: str):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    return await Organization.get(organization_id)

@router.get("/{organization_id}/groups/")
async def get_groups(
    organization_id: str,
    latest: Optional[bool] = Query(default=True)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    org = await Organization.get(organization_id)
    group_ids = org.get("group_ids",[])
    return await Group.get_batch_with_users(group_ids, latest=latest)

@router.get("/{organization_id}/groups/{group_id}")
async def get_group(organization_id: str, group_id: str):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    # Validate organization exists
    org = await Organization.get(organization_id)
    if not org:
        raise HTTPException(status_code=404, detail="Organization not found")
    
    # Validate group exists and belongs to organization
    if group_id not in org.get("group_ids", []):
        raise HTTPException(status_code=404, detail="Group not found in this organization")
        
    # Get group with users
    group = await Group.get_with_users(group_id)
    if group.get("created_by"):
        created_by_user = await User.get(group.get("created_by"))
        if created_by_user:
            group.update({"created_by_user": created_by_user})

    # If group has permissions, fetch and include them
    if group.get("permission_id"):
        permissions = await Permissions.get(group["permission_id"])
        if permissions:
            if type(permissions) == dict:
                group.update(permissions)
            else:
                group.update({"permissions": permissions})
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
        
    return group

@router.get("/{organization_id}/users/")
async def get_users(
    organization_id: str,
    latest: Optional[bool] = Query(default=True)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    users = await Organization.get_users(organization_id, include_inactive=True, latest=latest)
    return users

@router.get("/{organization_id}/users/{user_id}")
async def get_user(organization_id: str, user_id: str):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    return await User.get(user_id)

@router.get("/{organization_id}/{group_id}/users/")
async def get_users_by_group(
    organization_id: str, 
    group_id: str,
    latest: Optional[bool] = Query(default=True)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    users = await Organization.get_users_by_group(organization_id, group_id, latest=latest)
    return users

@router.post("/{organization_id}/groups/")
async def create_group(organization_id: str, group: GroupCreate,current_user = Depends(get_current_user)):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    tenant_user = current_user.get("cognito:username")
    group_name = group.name
    group_description = group.description
    group_permissions = group.permissions
    group_type = group.group_type
    access_level = group.access_level
    permission_id = await Permissions.create(group_permissions)
    group_data = Group(
        name=group_name, 
        description=group_description, 
        group_type=group_type, 
        access_level=access_level, 
        permission_id=permission_id,
        created_by=tenant_user)
    creds = await tenant_service.get_tenant_cred(organization_id)
    group_manager = CognitoGroupManager(creds['user_pool_id'])
    group_manager.create_group(group_name, group_description)
    group_id = await Group.create(group_data.model_dump())
    await Organization.add_group(organization_id, group_id)
    return {
        "group_id": group_id,
    }


@router.post("/{organization_id}/users/")
async def create_user(organization_id: str, user_create: UserCreate, request: Request):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    try:    
        # Validate email format
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, user_create.email):
            raise HTTPException(status_code=400, detail="Invalid email format")

        # Validate contact number format (assumes international format with optional +)
        phone_pattern = r'^\+?[1-9]\d{9,14}$'
        if not re.match(phone_pattern, user_create.contact_number):
            raise HTTPException(
                status_code=400,
                detail="Invalid contact number. Must be 10-15 digits with optional + prefix"
            )

        # Validate name is not empty
        if not user_create.name.strip():
            raise HTTPException(status_code=400, detail="Name cannot be empty")

        # Validate department is not empty 
        if not user_create.department.strip():
            raise HTTPException(status_code=400, detail="Department cannot be empty")
        
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(organization_id)
        email = user_create.email
        name = user_create.name
        contact_number = user_create.contact_number
        department = user_create.department
        designation = "Admin" if user_create.is_admin else "Developer"

        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        ui_url = request.headers.get('origin', str(request.base_url))
        set_password_url = f"{ui_url}/users/set_password?tenant_id={organization_id}&email={urllib.parse.quote(email)}"
        login_url = f"{ui_url}/users/login?tenant_id={organization_id}&email={urllib.parse.quote(email)}"
        organization = await Organization.get(organization_id)
        tenant_name = organization["name"]
        user = cognito_user_manager.create_user(
            email=email,
            temporary_password=f"Temp@2024{random_password()}",
            set_password_url=set_password_url,
            login_url=login_url,
            organization_name=tenant_name,
            custom_attributes={
                "tenant_id": organization_id,
                "is_admin": user_create.is_admin,
                "Name": name,
                "Department": department,
                "Designation": designation
            }
        )
        
        user_id = user['Username']
        user_data = User(
            _id=user_id,
            email=email,
            name=name,
            contact_number=contact_number,
            department=department,
            organization_id=organization_id,
            group_ids=[],
            is_admin=user_create.is_admin,
            designation=designation
        )
        await add_user_node_db(user_id, organization_id)
        await User.create(user_data.model_dump())
        return {
            "user_id": user_id,
        }
    except Exception as e:
        print(e)
        raise HTTPException(status_code=500, detail=f"{str(e)}")

@router.put("/{organization_id}/add_user_to_group")
async def add_user_to_group(organization_id: str, group_id: str, user_id: str):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    try:
        # Check if user exists
        user = await User.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if group exists
        group = await Group.get_with_users(group_id)
        if not group:
            raise HTTPException(status_code=404, detail="Group not found")

        # Verify user belongs to the organization
        if user['organization_id'] != organization_id:
            raise HTTPException(status_code=403, detail="User does not belong to this organization")
        await Group.add_user(group_id, user_id)
        user_group_ids = user.get('group_ids',[])
        user_group_ids.append(group_id)
        await User.update(user_id,{"group_ids": user_group_ids})
        return {"message": f"User added to group successfully {group_id}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.put("/{organization_id}/add_users_to_group")
async def add_users_to_group(organization_id: str, group_id: str, user_ids: List[str]):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    try:
        # Check if group exists
        group = await Group.get_with_users(group_id)
        if not group:
            raise HTTPException(status_code=404, detail="Group not found")

        failed_users = []
        success_count = 0

        for user_id in user_ids:
            try:
                # Check if user exists
                user = await User.get(user_id)
                if not user:
                    failed_users.append({"user_id": user_id, "reason": "User not found"})
                    continue

                # Verify user belongs to the organization
                if user['organization_id'] != organization_id:
                    failed_users.append({"user_id": user_id, "reason": "User does not belong to this organization"})
                    continue

                await Group.add_user(group_id, user_id)
                user_group_ids = user.get('group_ids', [])
                if group_id not in user_group_ids:
                    user_group_ids.append(group_id)
                    await User.update(user_id, {"group_ids": user_group_ids})
                success_count += 1

            except Exception as e:
                failed_users.append({"user_id": user_id, "reason": str(e)})

        return {
            "message": f"Added {success_count} users to group {group_id}",
            "success_count": success_count,
            "failed_users": failed_users
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.delete("/{organization_id}/groups/{group_id}")
async def delete_group(
    organization_id: str, 
    group_id: str,
    current_user = Depends(get_current_user)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    
    try:
        # Verify organization exists
        org = await Organization.get(organization_id)
        if not org:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Verify group exists and belongs to organization
        if group_id not in org.get("group_ids", []):
            raise HTTPException(status_code=404, detail="Group not found in this organization")
        
        # Get group details
        group = await Group.get(group_id)
        if not group:
            raise HTTPException(status_code=404, detail="Group not found")
        
        tenant_service = TenantService()
        # Delete group from Cognito
        creds = await tenant_service.get_tenant_cred(organization_id)
        group_manager = CognitoGroupManager(creds['user_pool_id'])
        group_manager.delete_group(group['name'])
        
        # Delete group from database
        deleted = await Group.delete(group_id)
        if not deleted:
            raise HTTPException(status_code=500, detail="Failed to delete group")
        
        # Remove group from organization
        await Organization.remove_group(organization_id, group_id)
        
        return {"message": "Group deleted successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{organization_id}/users/{user_id}")
async def delete_user(
    organization_id: str, 
    user_id: str,
    current_user = Depends(get_current_user)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    
    try:
        # Verify organization exists
        org = await Organization.get(organization_id)
        if not org:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Get user details
        user = await User.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if user belongs to the organization
        if user.get('organization_id') != organization_id:
            raise HTTPException(status_code=403, detail="User does not belong to this organization")
        
        # Check if user is the admin (admin cannot be deleted)
        if org.get('admin_id') == user_id:
            raise HTTPException(status_code=403, detail="Organization admin cannot be deleted")
        
        tenant_service = TenantService()
        # Delete user from Cognito
        creds = await tenant_service.get_tenant_cred(organization_id)
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        user_manager.delete_user(user_id)
        
        # Remove user from all groups
        if user.get('group_ids'):
            for group_id in user['group_ids']:
                group = await Group.get(group_id)
                if group and user_id in group.get('user_ids', []):
                    group_user_ids = group.get('user_ids', [])
                    group_user_ids.remove(user_id)
                    await Group.update(group_id, {"user_ids": group_user_ids})
        
        # Delete user from database
        deleted = await User.delete(user_id)
        if not deleted:
            raise HTTPException(status_code=500, detail="Failed to delete user")
        
        return {"message": "User deleted successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{organization_id}/users/{user_id}/promote-to-admin")
async def promote_to_admin(
    organization_id: str,
    user_id: str,
    current_user = Depends(get_current_user)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    
    try:
        # Verify organization exists
        org = await Organization.get(organization_id)
        if not org:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Get user details
        user = await User.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if user belongs to the organization
        if user.get('organization_id') != organization_id:
            raise HTTPException(status_code=403, detail="User does not belong to this organization")
        
        # Check if user is already an admin
        if user.get('is_admin'):
            raise HTTPException(status_code=400, detail="User is already an admin")
        
        # Update Cognito user attributes
        tenant_service = TenantService()
        creds = await tenant_service.get_tenant_cred(organization_id)
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        # Update Cognito custom attributes
        user_manager.update_user_attributes(
            user_id,
            {
                "custom:is_admin": "true",
                "custom:Designation": "Admin"
            }
        )
        
        # Update user in database
        await User.update(user_id, {
            "is_admin": True,
            "designation": "Admin"
        })
        
        return {
            "message": "User successfully promoted to admin",
            "user_id": user_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{organization_id}/users/{user_id}/demote-from-admin")
async def demote_from_admin(
    organization_id: str,
    user_id: str,
    current_user = Depends(get_current_user)
):
    if get_tenant_id() not in [organization_id, settings.KAVIA_SUPER_TENANT_ID]:
        raise HTTPException(status_code=403, detail="Access denied for this organization")
    
    try:
        # Verify organization exists
        org = await Organization.get(organization_id)
        if not org:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Get user details
        user = await User.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if user belongs to the organization
        if user.get('organization_id') != organization_id:
            raise HTTPException(status_code=403, detail="User does not belong to this organization")
        
        # Check if user is the organization admin (cannot demote org admin)
        if org.get('admin_id') == user_id:
            raise HTTPException(status_code=403, detail="Organization admin cannot be demoted")
        
        # Check if user is already not an admin
        if not user.get('is_admin'):
            raise HTTPException(status_code=400, detail="User is not an admin")
        
        # Update Cognito user attributes
        tenant_service = TenantService()
        creds = await tenant_service.get_tenant_cred(organization_id)
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        # Update Cognito custom attributes
        user_manager.update_user_attributes(
            user_id,
            {
                "custom:is_admin": "false",
                "custom:Designation": "Developer"
            }
        )
        
        # Update user in database
        await User.update(user_id, {
            "is_admin": False,
            "designation": "Developer"
        })
        
        return {
            "message": "User successfully demoted from admin",
            "user_id": user_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

