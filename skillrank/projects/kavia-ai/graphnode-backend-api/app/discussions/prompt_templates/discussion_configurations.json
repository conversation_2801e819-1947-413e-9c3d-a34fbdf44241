{"UserStoryConfiguration": {"modified_node_type": "UserStory", "modifiable_fields": ["Type", "Title", "Description", "Priority", "AcceptanceCriteria", "StoryPoints", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": true}, "TestCaseGenerationConfiguration": {"modified_node_type": "TestCaseRoot", "modifiable_fields": ["Title", "Description", "CommonTags", "existing_test_case_ids", "FunctionalCategories", "NonFunctionalCategories", "AutomationCategories"], "new_children_type": ["FunctionalTestCase", "NonFunctionalTestCase", "StressTest", "StabilityTest", "InteroperabilityTest"], "new_child_fields": {"FunctionalTestCase": ["Title", "Type", "Tags", "Category", "Description", "PreConditions", "Steps", "Expected<PERSON><PERSON>ult", "Priority", "CanBeAutomated", "TestLevel", "MethodsToTest", "ComponentsInvolved", "InterfacesToTest", "ExternalSystemsInvolved", "MockedDependencies"], "NonFunctionalTestCase": ["Title", "Type", "Tags", "Category", "Description", "TestEnvironment", "TestProcedure", "AcceptanceCriteria", "MeasurementMetrics", "ExpectedResults", "Priority", "CanBeAutomated", "TestLevel", "ComponentsInvolved", "InterfacesToTest", "ExternalSystemsInvolved"], "StressTest": ["Title", "Type", "Description", "Tags", "StressConditions", "TestProcedure", "Expected<PERSON><PERSON><PERSON><PERSON>", "MeasurementMetrics", "Priority", "CanBeAutomated", "TestLevel", "ComponentsInvolved", "InterfacesToTest", "ExternalSystemsInvolved"], "StabilityTest": ["Title", "Type", "Tags", "Description", "Duration", "TestConditions", "TestProcedure", "Expected<PERSON><PERSON><PERSON><PERSON>", "MeasurementMetrics", "Priority", "CanBeAutomated", "TestLevel", "ComponentsInvolved", "InterfacesToTest", "ExternalSystemsInvolved"], "InteroperabilityTest": ["Title", "Type", "Tags", "Description", "SystemsInvolved", "TestProcedure", "ExpectedInteraction", "Standards", "Priority", "CanBeAutomated", "TestLevel", "ComponentsInvolved", "InterfacesToTest", "ExternalSystemsInvolved"]}, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["VERIFIES", "UNIT_TEST_FOR", "SYSTEM_TEST_INVOLVES", "COMPONENT_TEST_FOR"], "modified_relationship_types": false}, "EpicConfiguration": {"modified_node_type": "Epic", "modifiable_fields": ["Title", "Description", "Priority", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["UserStory"], "new_child_fields": {"UserStory": ["Title", "Description", "Type", "Priority"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": true, "soft_delete": true}, "ProjectConfiguration": {"modified_node_type": "Project", "modifiable_fields": ["Title", "Description", "Objective", "<PERSON><PERSON>", "ArchitecturePattern", "ArchitectureStrategy", "TestAutomationFramework", "AdditionalDetails", "change_log", "user_inputs", "change_reason", "changes_needed"], "required_new_children_type": true, "new_children_type": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false, "soft_delete": false}, "RequirementRootConfiguration": {"modified_node_type": "RequirementRoot", "modifiable_fields": ["Title", "Description", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Epic"], "new_child_fields": {"Epic": ["Title", "Description", "Type", "Priority"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false, "soft_delete": true}, "WorkItemConfiguration": {"modified_node_type": "WorkItemRoot", "modifiable_fields": ["Title", "Description", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["WorkItem"], "new_child_fields": {"WorkItem": ["Title", "Description", "Type", "Priority", "Assignee", "EstimatedDuration"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "SystemContextOverviewConfiguration": {"modified_node_type": "SystemContext", "modifiable_fields": ["Title", "Description", "ExternalSystems", "Users", "SystemContextDiagram", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Container"], "new_child_fields": {"Container": ["ID", "Type", "Title", "Description", "Technology", "ContainerType"]}, "required_new_children_type": false, "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["interfacesWith"], "new_relationship_fields": {"interfacesWith": ["source", "target", "name", "type", "description"]}, "modified_relationship_types": false}, "SystemContextContainersConfiguration": {"modified_node_type": "SystemContext", "modifiable_fields": ["Title", "Description", "ContainerDiagram", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Container", "Interface"], "new_child_fields": {"Container": ["ID", "Type", "Title", "Description", "Technology", "ContainerType"], "Interface": ["Type", "Title", "Description", "ApiSpecification", "InterfaceType", "incoming_interfaces"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["interfacesWith", "CONTAINS"], "new_relationship_fields": {"interfacesWith": ["source", "target", "name", "type", "description"], "CONTAINS": ["type", "description"]}, "modified_relationship_types": false}, "ContainerConfiguration": {"modified_node_type": "Container", "modifiable_fields": ["Title", "Description", "ContainerType", "Selected_Tech_Stack", "UserInteractions", "ExternalSystemInteractions", "ImplementedRequirementIDs", "HasDatabase", "Repository_Name", "ContainerDiagram", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Architecture", "Interface", "Database"], "new_child_fields": {"Architecture": ["ID", "Title", "Description", "Type"], "Interface": ["Type", "Title", "Description", "ApiSpecification", "InterfaceType"], "Database": ["Title", "Type", "Description", "DatabaseType", "DatabaseVersion", "ConnectionString", "Host", "Port", "DatabaseName", "<PERSON><PERSON>aName", "AuthenticationType", "Username", "PasswordRef", "SSLEnabled", "ConnectionPoolSize", "QueryTimeout", "EntityDefinitions", "RelationshipMappings", "EntityRelationshipDiagram", "DatabaseSchema"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["interfacesWith", "USES"], "new_relationship_fields": {"interfacesWith": ["source", "target", "name", "type", "description"], "USES": ["name", "type", "description", "source", "target", "technology"]}, "modified_relationship_types": true}, "ComponentConfiguration": {"modified_node_type": "Component", "modifiable_fields": ["Title", "Description", "Technology", "ComponentPath", "ComponentDiagram", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Class", "Interface"], "new_child_fields": {"Class": ["Title", "Description", "Attributes", "Methods", "implements", "extends", "uses"], "Interface": ["Title", "Description", "Methods", "extends"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["IMPLEMENTS", "EXTENDS", "USES"], "new_relationship_fields": {"IMPLEMENTS": ["type", "description"], "EXTENDS": ["type", "description"], "USES": ["type", "description", "technology", "target"]}, "modified_relationship_types": true}, "ArchitectureConfiguration": {"modified_node_type": "Architecture", "modifiable_fields": ["ImplementedRequirementIDs", "Root_Folder", "IsArchitecturalLeaf", "Description", "ComponentDiagram", "ExternalSystemInteractions", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Architecture"], "new_child_fields": {"Architecture": ["ID", "Title", "Type", "Description"]}, "required_new_children_type": false, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["interfacesWith", "USES"], "new_relationship_fields": {"interfacesWith": ["interface_node_id", "source", "target", "name", "type", "description"], "USES": ["name", "type", "description", "source", "target", "technology"]}, "modified_relationship_types": true}, "ArchitectureDesignDetails": {"modified_node_type": "Architecture", "modifiable_fields": ["Title", "Design_Details", "Functionality", "change_log", "user_inputs", "change_reason", "changes_needed"], "required_new_children_type": true, "new_children_type": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DeploymentConfiguration": {"modified_node_type": "Deployment", "modifiable_fields": ["Title", "Description", "deployment_type", "main_tf", "outputs_tf", "providers_tf", "variables_tf", "workflow_file", "docker_file", "docker_compose", "configuration_state"], "new_children_type": [], "new_child_fields": {}, "required_new_children_type": true, "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DocumentationConfiguration": {"modified_node_type": "Documentation", "modifiable_fields": ["Title", "Description", "Content", "Version"], "new_children_type": ["Documentation", "Sub_Section"], "new_child_fields": {}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "SubsectionManagementConfiguration": {"modified_node_type": "Sub_Section", "modifiable_fields": ["Description"], "new_children_type": ["Sub_Section"], "new_child_fields": {"Sub_Section": ["Title", "Description", "Content", "Version"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "InterfaceDesignDetails": {"modified_node_type": "Interface", "modifiable_fields": ["Title", "Description", "FunctionalRequirements", "TechnicalRequirements", "DesignDetails", "InterfaceDescription", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": [], "required_new_children_type": true, "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignSpecification": {"modified_node_type": "Design", "modifiable_fields": ["Description", "PurposeAndResponsibilities", "InputsAndOutputs", "FunctionalRequirements", "NonFunctionalRequirements", "Dependencies", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": [], "required_new_children_type": true, "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["USES"], "new_relationship_fields": {"USES": ["name", "type", "description", "source", "target", "technology"]}, "modified_relationship_types": false}, "InterfaceDefinition": {"modified_node_type": "Interface", "modifiable_fields": ["Title", "Description", "PublicAPIDetails", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["Method", "HttpRoute", "DataContract", "Protocol"], "required_new_children_type": true, "new_child_fields": {"Method": ["Title", "Type", "Signature"], "HttpRoute": ["Title", "Type", "Route"], "DataContract": ["Title", "Type", "DataContract", "<PERSON><PERSON><PERSON>"], "Protocol": ["Title", "Type", "ProtocolDescription"]}, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": true}, "DesignBehavior": {"modified_node_type": "Design", "modifiable_fields": ["BehaviorDescription"], "new_children_type": ["Algorithm"], "required_new_children_type": true, "new_child_fields": {"Algorithm": ["Title", "Type", "Details"]}, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignComponentInteractions": {"modified_node_type": "Design", "modifiable_fields": ["ComponentInteractionsDescription", "changes_needed"], "new_children_type": ["SequenceDiagram", "StateMachineDiagram"], "required_new_children_type": true, "new_child_fields": {"SequenceDiagram": ["Title", "Type", "Description", "Diagram"], "StateMachineDiagram": ["Title", "Type", "Diagram"]}, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignTestCases": {"modified_node_type": "Design", "modifiable_fields": ["TestCasesDescription"], "new_children_type": ["UnitTest", "IntegrationTest", "PerformanceTest", "RobustnessTest"], "new_child_fields": {"UnitTest": ["Title", "Type", "Description", "Expected<PERSON><PERSON>ult"], "IntegrationTest": ["Title", "Type", "Description", "Expected<PERSON><PERSON>ult"], "PerformanceTest": ["Title", "Type", "Description", "Expected<PERSON><PERSON>ult"], "RobustnessTest": ["Title", "Type", "Description", "Expected<PERSON><PERSON>ult"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignClassDiagrams": {"modified_node_type": "Design", "modifiable_fields": ["ClassDiagramDescription"], "new_children_type": ["ClassDiagram"], "new_child_fields": {"ClassDiagram": ["Title", "Type", "Diagram"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignAPIDocs": {"modified_node_type": "Design", "modifiable_fields": ["Description"], "new_children_type": ["APIDoc"], "new_child_fields": {"APIDoc": ["Title", "Type", "api-doc-in-text"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignSADDocs": {"modified_node_type": "Design", "modifiable_fields": ["Description"], "new_children_type": ["SADDoc"], "new_child_fields": {"SADDoc": ["Title", "Type", "software-architecture-doc-in-text"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "ArchitecturalRequirement": {"modified_node_type": "ArchitecturalRequirement", "modifiable_fields": ["Title", "Description", "change_log", "user_inputs", "change_reason", "changes_needed"], "new_children_type": ["FunctionalRequirement", "NonFunctionalRequirement"], "new_child_fields": {"FunctionalRequirement": ["Type", "Title", "Description", "RelatedUserStoryIDs"], "NonFunctionalRequirement": ["Type", "Title", "Description"]}, "required_new_children_type": true, "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DatabaseModelConfiguration": {"modified_node_type": "DatabaseModel", "modifiable_fields": ["Title", "Description", "EntityDefinitions", "RelationshipMappings", "EntityRelationshipDiagram", "DatabaseSchema", "DataStorageStrategy", "DatabaseEngine", "<PERSON><PERSON>aName", "Indices", "<PERSON><PERSON><PERSON><PERSON>", "ForeignKeys", "Constraints"]}, "DatabaseConfiguration": {"modified_node_type": "Container", "modifiable_fields": ["change_reason"], "new_children_type": ["Interface"], "new_child_fields": {"Interface": ["Type", "Title", "Description", "InterfaceType", "PublicAPIDetails"]}, "required_new_children_type": true, "modified_children": false, "modified_siblings": false, "modified_other_nodes": true, "new_relationship_types": ["USES"], "new_relationship_fields": {"USES": ["name", "type", "description", "source", "target", "technology"]}, "modified_relationship_types": false}}