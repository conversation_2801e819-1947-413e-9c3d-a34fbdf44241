import asyncio
from typing import Dict, List, Optional, Union, Any, Callable
from datetime import datetime
import logging

class BackgroundTaskManager:
    """
    Manages background tasks for NodeVersionManager to allow non-blocking operations.
    """
    def __init__(self):
        self.tasks = set()
        self.logger = logging.getLogger(__name__)
        
    def create_task(self, coro, task_name: str = None) -> asyncio.Task:
        """
        Create a background task that won't block the main flow.
        
        Args:
            coro: The coroutine to run in the background
            task_name: Optional name for the task for logging purposes
            
        Returns:
            The created task
        """
        # Create task with proper error handling
        task = asyncio.create_task(self._task_wrapper(coro, task_name))
        
        # Add to our set of tasks
        self.tasks.add(task)
        
        # Remove task from set when done
        task.add_done_callback(self.tasks.discard)
        
        return task
    
    async def _task_wrapper(self, coro, task_name: str = None):
        """
        Wrapper for tasks to handle exceptions and provide logging.
        """
        try:
            return await coro
        except Exception as e:
            task_info = f" '{task_name}'" if task_name else ""
            self.logger.error(f"Error in background task{task_info}: {str(e)}", exc_info=True)
            # Re-raise so the task is marked as failed
            raise