from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database
from typing import Dict, Optional, List, Any
from app.core.constants import TaskStatus
from bson import ObjectId
from app.utils.datetime_utils import generate_timestamp
class TaskRepository(BaseMongoRepository):
    def __init__(self, collection: str = "tf_tasks"):
        super().__init__(collection_name=collection)
        self.collection = collection

    async def get_task_by_id(self, task_id: str, db: Database) -> Optional[Dict]:
        """Get task by ID with exact original format."""
        try:
            collection = await self.get_collection(db)
            task = await collection.find_one({"_id": task_id})
            if task:
                task["task_id"] = str(task["_id"])
            return task
        except Exception as e:
            print(f"Error getting task by ID: {e}")
            return None

    async def get_task_by_task_id(self, task_id: str, db: Database) -> Optional[Dict]:
        """Get task by task_id field."""
        try:
            collection = await self.get_collection(db)
            return await collection.find_one({"task_id": task_id})
        except Exception as e:
            print(f"Error getting task by task_id: {e}")
            return None

    async def get_pending_by_node_id(self, node_id: int, active_statuses: List[str], db: Database) -> Optional[Dict]:
        """Get pending task by node ID with specific statuses."""
        try:
            collection = await self.get_collection(db)
            return await collection.find_one({
                "node_id": node_id,
                "status": {"$in": active_statuses}
            })
        except Exception as e:
            print(f"Error getting pending task: {e}")
            return None

    async def get_pending_by_project_id(self, project_id: int, active_statuses: List[str], db: Database) -> Optional[Dict]:
        """Get pending task by project ID with specific statuses."""
        try:
            collection = await self.get_collection(db)
            return await collection.find_one({
                "project_id": project_id,
                "status": {"$in": active_statuses}
            })
        except Exception as e:
            print(f"Error getting project pending task: {e}")
            return None
            
    async def update_by_task_id(self, task_id: str, update_data: Dict, db: Database) -> bool:
        """Update task by task_id field."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating task by task_id: {e}")
            return False

    async def update_task_status(self, task_id: str, update_data: Dict, db: Database) -> bool:
        """Update task status maintaining exact update format."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating task status: {e}")
            return False

    async def update_task_context(self, task_id: str, context: Dict, db: Database) -> bool:
        """Save task context to database."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": {"context": context}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating task context: {e}")
            return False

    async def append_terminal_output(self, task_id: str, output: str, db: Database) -> bool:
        """Append terminal output to task."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$push": {"terminal_output": output}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error appending terminal output: {e}")
            return False

    async def append_browser_output(self, task_id: str, output: str, db: Database) -> bool:
        """Append browser output to task."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$push": {"browser_output": output}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error appending browser output: {e}")
            return False

    async def update_cost_info(self, task_id: str, agents_cost: Dict[str, float], total_cost: float, db: Database) -> bool:
        """Update task cost information."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": {
                    "agents_cost": agents_cost,
                    "total_cost": total_cost
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating cost info: {e}")
            return False

    async def update_task_description(self, task_id: str, description: str, db: Database) -> bool:
        """Update task description."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": {"description": description}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating task description: {e}")
            return False

    async def get_detailed_report(self, task_id: str, db: Database) -> Optional[Dict]:
        """Get detailed task report without terminal output."""
        try:
            collection = await self.get_collection(db)
            return await collection.find_one(
                {"_id": task_id},
                {"terminal_output": 0}
            )
        except Exception as e:
            print(f"Error getting detailed report: {e}")
            return None

    async def get_subtasks(self, task_id: str, db: Database) -> List[Dict]:
        """Get all subtasks for a given task."""
        try:
            collection = await self.get_collection(db)
            cursor = collection.find({"parent_task_id": task_id})
            return await cursor.to_list(length=None)
        except Exception as e:
            print(f"Error getting subtasks: {e}")
            return []

    async def create_task(self, task_data: Dict, db: Database) -> str:
        """Create new task."""
        try:
            collection = await self.get_collection(db)
            if 'start_time' not in task_data:
                task_data['start_time'] = generate_timestamp()
            result = await collection.insert_one(task_data)
            return str(result.inserted_id)
        except Exception as e:
            print(f"Error creating task: {e}")
            return None

    async def delete_task(self, task_id: str, db: Database) -> bool:
        """Delete a task."""
        try:
            collection = await self.get_collection(db)
            result = await collection.delete_one({"_id": task_id})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting task: {e}")
            return False

    async def update_configuration_status(self, task_id: str, configuration_status: Dict, db: Database) -> bool:
        """Update configuration status."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": {"configuration_status": configuration_status}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating configuration status: {e}")
            return False

    async def get_latest_completed_task(self, project_id: int, db: Database) -> Optional[Dict]:
        """Get most recent completed task."""
        try:
            collection = await self.get_collection(db)
            cursor = collection.find({
                "project_id": project_id,
                "status": {"$regex": "^(complete)$", "$options": "i"}
            }).sort('start_time', -1).limit(1)
            
            tasks = await cursor.to_list(length=1)
            return tasks[0] if tasks else None
        except Exception as e:
            print(f"Error getting latest completed task: {e}")
            return None

    async def cancel_task(self, task_id: str, db: Database) -> bool:
        """Cancel task and mark as complete."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": {
                    "status": TaskStatus.CANCELLED,
                    "cancel": True,
                    "run_completed": True
                }}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error canceling task: {e}")
            return False

    async def update_task_fields(self, task_id: str, fields: Dict[str, Any], db: Database) -> bool:
        """Update multiple task fields atomically."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_one(
                {"_id": task_id},
                {"$set": fields}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating task fields: {e}")
            return False