# app/models/announcement_model.py
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
from bson import ObjectId
from app.utils.datetime_utils import generate_timestamp, to_utc, from_isoformat
class AnnouncementType(str, Enum):
    ALERT = "alert"
    MAINTENANCE = "maintenance"
    ANNOUNCEMENT = "announcement"

class AnnouncementBase(BaseModel):
    content: str
    detailed_content: Optional[str] = None
    type: AnnouncementType
    published_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

    def to_mongo(self) -> Dict[str, Any]:
        data = self.model_dump(exclude_none=True)
        # Convert datetime fields to UTC
        if data.get('published_at'):
            data['published_at'] = to_utc(data['published_at'])
        if data.get('expires_at'):
            data['expires_at'] = to_utc(data['expires_at'])
        return {
            **data,
            "type": self.type.value
        }

# app/models/announcement_model.py (continued)

    @classmethod
    def from_mongo(cls, data: Dict[str, Any]) -> "AnnouncementBase":
        """Create from MongoDB document"""
        mongo_data = data.copy()
        if "type" in mongo_data:
            mongo_data["type"] = AnnouncementType(mongo_data["type"])
        if "_id" in mongo_data:
            mongo_data["id"] = str(mongo_data.pop("_id"))
        return cls(**mongo_data)

class AnnouncementDraft(AnnouncementBase):
    id: str = Field(default_factory=lambda: str(ObjectId()))
    created_at: datetime = Field(default_factory=generate_timestamp)
    last_saved: datetime = Field(default_factory=generate_timestamp)
    user_id: str
    tenant_id: str

    def to_mongo(self) -> Dict[str, Any]:
        """Convert to MongoDB document format"""
        mongo_data = super().to_mongo()
        return {
            **mongo_data,
            "_id": ObjectId(self.id) if not isinstance(self.id, ObjectId) else self.id,
            "created_at": self.created_at,
            "last_saved": self.last_saved,
            "user_id": self.user_id,
            "tenant_id": self.tenant_id
        }

class AnnouncementPublish(AnnouncementBase):
    id: str = Field(default_factory=lambda: str(ObjectId()))
    created_at: datetime = Field(default_factory=generate_timestamp)
    published_at: Optional[datetime]= Field(default_factory=generate_timestamp)
    expires_at: Optional[datetime] = None
    is_active: bool = True
    is_published: bool = True
    user_id: str
    tenant_id: str

    def to_mongo(self) -> Dict[str, Any]:
        """Convert to MongoDB document format"""
        mongo_data = super().to_mongo()
        return {
            **mongo_data,
            "_id": ObjectId(self.id) if not isinstance(self.id, ObjectId) else self.id,
            "created_at": self.created_at,
            "published_at": self.published_at,
            "expires_at": self.expires_at,
            "is_active": self.is_active,
            "is_published": self.is_published,
            "user_id": self.user_id,
            "tenant_id": self.tenant_id
        }

    @property
    def is_scheduled(self) -> bool:
        return self.published_at >= generate_timestamp()

    @property
    def is_expired(self) -> bool:
        if self.expires_at is None:
            return False
        return generate_timestamp() > self.expires_at

# Request/Response Models
class AnnouncementDraftCreate(AnnouncementBase):
    pass

class AnnouncementDraftUpdate(AnnouncementBase):
    pass

class AnnouncementPublishCreate(AnnouncementBase):
    published_at: Optional[datetime] = Field(default_factory=generate_timestamp)
    expires_at: Optional[datetime] = None

# MongoDB Collection Info
MONGO_INDEXES = {
    "announcement_drafts": [
        [("user_id", 1), ("tenant_id", 1)],
        [("last_saved", -1)],
    ],
    "announcements": [
        [("user_id", 1), ("tenant_id", 1)],
        [("is_active", 1), ("expires_at", 1)],
        [("published_at", -1)],
        [("is_published", 1)],
    ],
    "announcement_acknowledgments": [
        ("announcement_id", 1),
        ("user_id", 1),
        ("tenant_id", 1)
    ]
}

# Helper function to create MongoDB indexes
def create_indexes(db):
    try:
        """Create MongoDB indexes for announcements collections"""
        for collection_name, indexes in MONGO_INDEXES.items():
            collection = db[collection_name]
            for index in indexes:
                collection.create_index(index)
    except Exception as e: 
        print("Error creating indexes", e)
        

class AnnouncementAcknowledgment(BaseModel):
    announcement_id: str
    user_id: str
    tenant_id: str
    do_not_show_again: bool
    acknowledged_at: datetime = Field(default_factory=generate_timestamp)

    def to_mongo(self):
        return {
            "announcement_id": ObjectId(self.announcement_id),
            "user_id": self.user_id,
            "tenant_id": self.tenant_id,
            "do_not_show_again": self.do_not_show_again,
            "acknowledged_at": self.acknowledged_at
        }

class AnnouncementAcknowledgmentCreate(BaseModel):
    do_not_show_again: bool