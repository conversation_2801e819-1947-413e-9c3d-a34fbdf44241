from typing import List, Dict, Optional, <PERSON><PERSON>
import boto3
from botocore.exceptions import ClientError
from app.core.Settings import settings as base_settings

class CognitoGroupManager:
    def __init__(self,user_pool_id):
        settings = base_settings
        self.cognito = boto3.client(
            'cognito-idp',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self.user_pool_id = user_pool_id

    def create_group(self, group_name: str, description: str = None, 
                    precedence: int = None) -> Dict:
        """
        Create a new Cognito group
        
        Args:
            group_name (str): Name of the group to create
            description (str, optional): Description of the group
            precedence (int, optional): Precedence of the group
            
        Returns:
            Dict: Information about the created group
            
        Raises:
            Exception: If group creation fails
        """
        try:
            params = {
                'UserPoolId': self.user_pool_id,
                'GroupName': group_name
            }
            
            if description:
                params['Description'] = description
            if precedence is not None:
                params['Precedence'] = precedence
                
            response = self.cognito.create_group(**params)
            return response['Group']
            
        except ClientError as e:
            raise Exception(f"Failed to create group: {str(e)}")

    def list_groups(self) -> List[Dict]:
        """List all Cognito groups"""
        try:
            response = self.cognito.list_groups(UserPoolId=self.user_pool_id)
            return response['Groups']
        except ClientError as e:
            raise Exception(f"Failed to list groups: {str(e)}")
    
    def update_group(self, group_name: str, description: str = None, 
                    precedence: int = None) -> Dict:
        """Update an existing Cognito group"""
        try:
            params = {
                'UserPoolId': self.user_pool_id,
                'GroupName': group_name
            }
            if description:
                params['Description'] = description
            if precedence is not None:
                params['Precedence'] = precedence
                
            response = self.cognito.update_group(**params)
            return response['Group']
        except ClientError as e:
            raise Exception(f"Failed to update group: {str(e)}")
    
    def delete_group(self, group_name: str) -> None:
        """Delete a Cognito group"""
        try:
            self.cognito.delete_group(
                UserPoolId=self.user_pool_id,
                GroupName=group_name
            )
        except ClientError as e:
            raise Exception(f"Failed to delete group: {str(e)}")

    # def rename_group(self, old_group_name: str, new_group_name: str) -> Dict:
    #     """
    #     Rename a Cognito group by creating a new group and transferring settings
        
    #     Args:
    #         old_group_name (str): The current name of the group
    #         new_group_name (str): The desired new name for the group
            
    #     Returns:
    #         Dict: Information about the new group
        
    #     Raises:
    #         Exception: If any step of the rename process fails
    #     """
    #     try:
    #         # Get the existing group's details
    #         old_group = self.cognito.get_group(
    #             GroupName=old_group_name,
    #             UserPoolId=self.user_pool_id
    #         )['Group']
            
    #         # Create new group with same settings
    #         params = {
    #             'UserPoolId': self.user_pool_id,
    #             'GroupName': new_group_name
    #         }
            
    #         # Copy over description and precedence if they exist
    #         if 'Description' in old_group:
    #             params['Description'] = old_group['Description']
    #         if 'Precedence' in old_group:
    #             params['Precedence'] = old_group['Precedence']
                
    #         # Create the new group
    #         new_group = self.cognito.create_group(**params)['Group']
            
    #         # List all users in the old group
    #         users = self.cognito.list_users_in_group(
    #             UserPoolId=self.user_pool_id,
    #             GroupName=old_group_name
    #         )['Users']
            
    #         # Transfer users to the new group
    #         for user in users:
    #             self.cognito.admin_add_user_to_group(
    #                 UserPoolId=self.user_pool_id,
    #                 Username=user['Username'],
    #                 GroupName=new_group_name
    #             )
    #             self.cognito.admin_remove_user_from_group(
    #                 UserPoolId=self.user_pool_id,
    #                 Username=user['Username'],
    #                 GroupName=old_group_name
    #             )
            
    #         # Delete the old group
    #         self.delete_group(old_group_name)
            
    #         return new_group
            
    #     except ClientError as e:
    #         raise Exception(f"Failed to rename group: {str(e)}")

    def add_user_to_group(self, identifier: str, group_name: str) -> None:
        """
        Add a user to a specified Cognito group
        
        Args:
            identifier: Username or email of the user
            group_name: Name of the group to add the user to
            
        Raises:
            Exception: If adding user to group fails
        """
        try:
            # Get user by identifier (username or email)
            user = self.get_user_by_identifier(identifier)
            username = user['Username']
            
            self.cognito.admin_add_user_to_group(
                UserPoolId=self.user_pool_id,
                Username=username,
                GroupName=group_name
            )
        except ClientError as e:
            raise Exception(f"Failed to add user to group: {str(e)}")

    def remove_user_from_group(self, identifier: str, group_name: str) -> None:
        """
        Remove a user from a specified Cognito group
        
        Args:
            identifier: Username or email of the user
            group_name: Name of the group to remove the user from
            
        Raises:
            Exception: If removing user from group fails
        """
        try:
            # Get user by identifier (username or email)
            user = self.get_user_by_identifier(identifier)
            username = user['Username']
            
            self.cognito.admin_remove_user_from_group(
                UserPoolId=self.user_pool_id,
                Username=username,
                GroupName=group_name
            )
        except ClientError as e:
            raise Exception(f"Failed to remove user from group: {str(e)}")

    def list_user_groups(self, identifier: str) -> List[Dict]:
        """
        List all groups that a user belongs to
        
        Args:
            identifier: Username or email of the user
            
        Returns:
            List of dictionaries containing group information
            
        Raises:
            Exception: If listing user groups fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            username = user['Username']
            
            response = self.cognito.admin_list_groups_for_user(
                Username=username,
                UserPoolId=self.user_pool_id
            )
            return response['Groups']
        except ClientError as e:
            raise Exception(f"Failed to list user groups: {str(e)}")

    def list_group_users(self, group_name: str) -> List[Dict]:
        """
        List all users in a specified group
        
        Args:
            group_name: Name of the group
            
        Returns:
            List of dictionaries containing user information
            
        Raises:
            Exception: If listing group users fails
        """
        try:
            response = self.cognito.list_users_in_group(
                UserPoolId=self.user_pool_id,
                GroupName=group_name
            )
            return response['Users']
        except ClientError as e:
            raise Exception(f"Failed to list users in group: {str(e)}")
        
