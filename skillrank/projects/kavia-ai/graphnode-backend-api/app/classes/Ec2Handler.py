# app/classes/Ec2Handler.py
import boto3
import asyncio
import json
import aiohttp
from typing import AsyncGenerator, Dict, Optional
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id
from app.utils.project_utils import get_stage


class Ec2Handler:
    def __init__(self):
        self.ec2_client = boto3.client('ec2',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        self.ec2_resource = boto3.resource('ec2',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        self.base_url = f"https://bxjr1ncbui.execute-api.us-east-1.amazonaws.com/codegen-staged-{get_stage()}"
    
    async def repeat_message(self, message: str, duration: int = 10, interval: int = 2) -> AsyncGenerator[Dict, None]:
        """Helper to repeatedly yield a message for a duration."""
        end_time = asyncio.get_event_loop().time() + duration
        while asyncio.get_event_loop().time() < end_time:
            yield {'message': message, 'end': False}
            await asyncio.sleep(interval)


    
    async def get_instance_state(self, instance_name: str, platform: str = "common") -> Optional[Dict]:
        """Get the current state of an EC2 instance by its name tag."""
        try:
            filters = [ 
                {'Name': 'tag:Name', 'Values': [instance_name]},
                {'Name': 'instance-state-name', 'Values': ['running', 'stopped']}
            ]
            if platform != "common":
                filters.append({'Name': 'tag:Platform', 'Values': ["flutter","android","ios"]})
            
            instances = self.ec2_resource.instances.filter(
                Filters=filters
            )
            ready_instance = None
            for instance in instances:
                print(instance.id, instance.state['Name'])  
                print("s")# Debug print
                if platform == "common":
                    # Skip if platform tag is flutter/android/ios
                    platform_tag = next((tag['Value'] for tag in instance.tags if tag['Key'] == 'Platform'), None)
                    if platform_tag in ["flutter", "android", "ios"]:
                        continue
                    else:
                        ready_instance = {
                    'id': instance.id,
                    'state': instance.state['Name'],
                    'public_ip': instance.private_ip_address,
                    'tags': instance.tags
                }
                    print("ready_instance common", instance.id)
                else: 
                    ready_instance = {
                    'id': instance.id,
                    'state': instance.state['Name'],
                    'public_ip': instance.private_ip_address,
                    'tags': instance.tags,
                    'special_instance':True
                }      
                    print("ready_instance android", instance.id)
                return ready_instance
            return None
        except Exception as e:
            raise Exception(f"Error checking instance state: {str(e)}")

    async def wait_for_instance_running(self, instance_id: str) -> None:
        """Wait for an EC2 instance to enter running state."""
        waiter = self.ec2_client.get_waiter('instance_running')
        await asyncio.to_thread(waiter.wait, InstanceIds=[instance_id])

    async def start_instance(self, instance_id: str) -> None:
        """Start an EC2 instance."""
        try:
            await asyncio.to_thread(
                self.ec2_client.start_instances,
                InstanceIds=[instance_id]
            )
        except Exception as e:
            raise Exception(f"Error starting instance: {str(e)}")

    async def wake_up_instance(self, instance_name:str) -> None:
        """Wake up a stopped EC2 instance."""
        try:
            instance_id = await self.get_instance_state(instance_name)
            if instance_id:
                platform_tag = next((tag['Value'] for tag in instance_id['tags'] if tag['Key'] == 'Platform'), None)
                if platform_tag in ["flutter", "android", "ios"]:
                    print("Skipping wake up for platform instance:", platform_tag)  # Debug print
                    return
                await self.start_instance(instance_id['id'])
                print("Instance initiated")  # Debug print
        except Exception as e:
            raise Exception(f"Error waking up instance: {str(e)}")
        
    async def create_instance_via_url(self, stage: str, project_id: str, tenant_id: str, platform: str = "common") -> Dict:
        """Create a new EC2 instance using the API URL."""
        try:
            url = (
                f"{self.base_url}"
                f"?project_id={project_id}"
                f"&stage={stage}"
                f"&tenant_id={tenant_id}"
                f"&platform={platform}"
            )
            
            print(f"Calling URL: {url}")  # Debug print
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        response_text = await response.text()
                        raise Exception(f"API call failed with status {response.status}: {response_text}")
                    return await response.text()
            
        except Exception as e:
            raise Exception(f"Error creating instance via URL: {str(e)}")

    async def get_project(self, project_id: str, stage: str, platform: str= "common") -> AsyncGenerator[Dict, None]:
        """
        Main method to handle EC2 instance operations for a project.
        Yields status updates throughout the process.
        """

        #overriding platform
        platform = "common"
        tenant_id = get_tenant_id()
        instance_name = f"{tenant_id}-{project_id}-{stage}"
        
        # Check if instance exists and is available
        instance_info = await self.get_instance_state(instance_name, platform=platform)
        
        if instance_info:
            async for msg in self.repeat_message('install|provision', duration=4):
                yield msg
            
            if instance_info['state'] != 'running':
                async for msg in self.repeat_message('install|provision', duration=6):
                    yield msg
                    
                await self.start_instance(instance_info['id'])
                await self.wait_for_instance_running(instance_info['id'])
                
                async for msg in self.repeat_message('install|provision', duration=4):
                    yield msg
                
                # Add 10-second delay with code generation message
                async for msg in self.repeat_message('install|provision', duration=10):
                    yield msg
                
                # Get updated instance info after starting
                instance_info = await self.get_instance_state(instance_name, platform=platform)
            
            if instance_info['state'] == 'running':
      
                yield {
                    'message': 'install|provision|config',
                    'ip': instance_info['public_ip'],
                    'end': True
                }
        else:
            # Instance not available, create new one via URL
            async for msg in self.repeat_message('install', duration=6):
                yield msg
            print(f"Creating new instance for platform {platform} !!!")
            await self.create_instance_via_url(
                stage, project_id, tenant_id, platform=platform
            )
            
            # Wait for instance to be available and return to available state logic
            retry_count = 0
            while retry_count < 30:  # Maximum 5 minutes wait (10 second intervals)
                instance_info = await self.get_instance_state(instance_name, platform=platform)
                if instance_info:
                    async for msg in self.repeat_message('install|provision', duration=4):
                        yield msg
                        
                    if instance_info['state'] == 'running':
                        # Add 10-second delay with code generation message
                        async for msg in self.repeat_message('install|provision', duration=10):
                            yield msg
                            
                        yield {
                            'message': 'install|provision|config',
                            'ip': instance_info['public_ip'],
                            'end': True
                        }
                        break
                        
                # If instance not ready, repeat waiting message
                async for msg in self.repeat_message('install|provision|config', duration=10):
                    yield msg
                    
                retry_count += 1
            else:
                raise TimeoutError("error")
