import io
import pdfplumber
import re

def extract_text_from_pdf(content):
    try:
        with pdfplumber.open(io.BytesIO(content)) as pdf_inv:
            no_of_pages = len(pdf_inv.pages)
            consolidated_text = ""
            
            for page_no in range(0, no_of_pages):
                data = pdf_inv.pages[page_no].extract_text()
                if data:
                    # Add page number and cleaned text to the consolidated string
                    consolidated_text += f"Page {page_no + 1}:\n{data}\n\n"

        return consolidated_text.strip()

    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}")
        return None



