# app/utils/background_tasks.py
from fastapi import BackgroundTasks
import asyncio
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# Store running tasks
running_tasks = {}

async def track_task(task_id: str, func, *args, **kwargs):
    """Track the execution of a background task"""
    start_time = datetime.now()
    logger.info(f"Task {task_id} started at {start_time}")
    
    try:
        result = await func(*args, **kwargs)
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"Task {task_id} completed at {end_time}. Duration: {duration}")
        return result
    except Exception as e:
        logger.error(f"Task {task_id} failed: {str(e)}", exc_info=True)
        raise
    finally:
        if task_id in running_tasks:
            del running_tasks[task_id]

def add_background_task(func, *args, **kwargs):
    """Enhanced background task creation with tracking"""
    background_tasks = BackgroundTasks()
    task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{id(func)}"
    
    # Store task info
    running_tasks[task_id] = {
        'start_time': datetime.now(),
        'function': func.__name__,
        'args': args,
        'kwargs': kwargs
    }
    
    # Add the tracked task
    background_tasks.add_task(track_task, task_id, func, *args, **kwargs)
    
    logger.info(f"Background task {task_id} scheduled")
    return background_tasks

# Add endpoint to check task status
async def get_running_tasks():
    """Get information about currently running tasks"""
    return {
        task_id: {
            'running_time': str(datetime.now() - info['start_time']),
            'function': info['function'],
            'args': str(info['args']),
            'kwargs': str(info['kwargs'])
        }
        for task_id, info in running_tasks.items()
    }