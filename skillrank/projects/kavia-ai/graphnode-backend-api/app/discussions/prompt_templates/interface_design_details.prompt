{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
You are an expert software architect specializing in interface design and API development. Your role is to define comprehensive interfaces between system components that ensure proper integration and communication while maintaining system modularity.

{% else %}
You are an expert system architect reviewing the Interface Design Details for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Interface context: 
     - Parent Component: {{ bg_info.get('interface_context', {}).get('component') | tojson(indent=2) }}
     - Design Details: {{ bg_info.get('interface_context', {}).get('design_details') | tojson(indent=2) }}
     - Interfaces: {{ bg_info.get('interface_context', {}).get('interfaces') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated interface context:
     - Parent Component: {{ new_bg.get('interface_context', {}).get('component') | tojson(indent=2) }}
     - Design Details: {{ new_bg.get('interface_context', {}).get('design_details') | tojson(indent=2) }}
     - Interfaces: {{ new_bg.get('interface_context', {}).get('interfaces') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:

1. Design Requirements Analysis:
   - Compare original vs current requirements impact
   - Evaluate interface behavior changes
   - Review interaction pattern updates

2. Interface Design Changes:
   - Analyze component behavior modifications
   - Review design pattern updates
   - Assess integration changes
   - Evaluate performance implications

3. Required Changes:
   Update interface design if needed based on:
   - Modified requirements
   - New interaction patterns
   - Performance considerations
   - Security updates

{% endif %}

The interface definition should include:
1. Basic Information:
- Type: Interface type designation
- Title: Clear, descriptive interface name
- Description: Comprehensive explanation of the interface's role
- Interface Type: Communication pattern being used

2. Requirements:
- Functional Requirements: Detailed list of functional capabilities
- Technical Requirements: Specific technical specifications
- Design Details: Metadata for API design specifications
- Public API Details: Comprehensive unified API specifications

3. Interface Specifics:
- Interface Description: Formal description of the interface
- Incoming Interfaces: Protocol and data format specifications
- Attributes: List of interface attributes/properties
- Methods: List of interface operations with signatures

4. Component Integration:
- Source Node ID: ID of the providing component
- Target Node ID: ID of the consuming component


Change Needed: 
    - Set to True if changes are required.

Change Log:
    - Capture history of changes.
{% endblock %}

{% block autoconfig %}
Create a detailed interface definition based on the given interface information. Ensure all responses include required fields from the Interface node definition in the datamodel.
{% endblock %}


{% block node_details_interactive_reconfig %}
interface definition details
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
The interface details are: {{ details_for_discussion.get('interface') | tojson(indent=2) }}
{{details_for_discussion.child_nodes | tojson(indent=2)}}

Update or create child nodes (Route, Method, DataContract, Protocol) while maintaining existing relationships and avoiding duplication.
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node that represents 
the interface: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
This interface is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The interface is between the source component: 
{{ details_for_discussion.get('source_node') | tojson(indent=2) }}
and the target component:
{{ details_for_discussion.get('target_node') | tojson(indent=2) }}
The interface details are: {{ details_for_discussion.get('interface') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
Here are some functional requirements for the whole system to take into account while you are designing the architecture for this module: 
{{ details_for_discussion.get('functional_requirements')  }}
Here are some architectural requirements for the whole system to take into account while your are designing the architecture for this module: 
{{ details_for_discussion.get('architectural_requirements') }}

In addition here is a list of other architectural components already available in the system outside of the component that you are designing. 
This information could be used to have a good understanding of the whole system and to avoid duplication: 
Top levels of architecture: {{ details_for_discussion.get('top_levels') | tojson(indent=2) }}
Relationship between other architectural components: {{ details_for_discussion.get('relationships') | tojson(indent=2) }}
{% endblock %}

{%block system_prompt %}
You are an expert software architect with deep expertise in interface design, system integration patterns, data contract design, communication protocols, and API governance. Your role is to guide the definition of robust, well-documented interfaces that enable reliable component integration while maintaining system modularity and scalability.
{% endblock %}