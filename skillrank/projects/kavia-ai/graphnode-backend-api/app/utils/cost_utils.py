from app.core.Settings import settings
from app.connection.establish_db_connection import get_mongo_db
from app.models.product_model import ProductListResponse
from pymongo import MongoClient
import asyncio

#All the agents are separated based on their plans. While adding a new agent separate it in its respective plan
AGENT_PLANS = {
    "Plan": ["configuration", "architecture_requirement", "design_details", "definition", "specification", "behavior", "testcases", "component_interactions", "classdiagram"],
    "Inspect": ["code_query", "DocumentationAgent"],
    "Build": ["InitialSetupAgent", "CodeAnalysisAgent", "CodeWritingAgent", "TestCodeWritingAgent", "TestCaseCreationAgent", "TestDataGenerationAgent", "TestExecutionAgent", "ExecutionReviewAgent", "VisualVerificationAgent", "SearchAgent", "AssetExtractorAgent", "Knowledge", "ContainerBuilderAgent", "Summarizer", "Orchestrator"]
}

def find_agent_plan(agent_name):
    planName = next((p for p in AGENT_PLANS if agent_name in AGENT_PLANS[p]), "Build")
    return planName


async def check_free_credit_limits_crossed(tenant_id, current_user=None):
    try:
        db_prefix = settings.MONGO_DB_NAME
        if db_prefix.startswith('develop'):
            db_name = 'develop_kaviaroot'
        elif db_prefix.startswith('qa'):
            db_name = 'qa_kaviaroot'
        elif db_prefix.startswith('pre_prod'):
            db_name = 'pre_prod_kaviaroot'
        else:
            db_name = db_prefix + '_kaviaroot'

        # Special handling for B2C tenants
        if tenant_id == settings.KAVIA_B2C_CLIENT_ID and current_user:
            # Get the user ID from the current user object
            user_id = current_user if isinstance(current_user, str) else (
                current_user.get('sub') or current_user.get('cognito:username') 
                if current_user else None
            )
            
            if not user_id:
                print("no user id")
                return False  # No user ID, can't check limits
            
            # Fetch B2C tenant's document from llm_costs collection
            mongo_handler = get_mongo_db(
                db_name=db_name,
                collection_name="llm_costs"
            )
            
            b2c_doc = await mongo_handler.get_one({'organization_id': tenant_id}, mongo_handler.db)
            
            if not b2c_doc or 'users' not in b2c_doc:
                print("no b2c_doc")
                return False  # No document or no users array found
            
            # Find the current user in the users array
            user_entry = next((user for user in b2c_doc['users'] if user.get('user_id') == user_id), None)
            
            if not user_entry or 'cost' not in user_entry or 'current_plan' not in user_entry:
                print("no user entry")
                return False  # User not found or missing required fields
            
            # Get user's cost and current plan
            user_cost = float(user_entry['cost'].replace('$', ''))
            current_plan_id = user_entry['current_plan']
            
            # Fetch all products from Stripe using your existing endpoint
            from app.routes.products_route import get_products  # Import your existing function
            
            products = await get_products()

            # Find the specific product that matches the user's current plan
            user_plan = next((product for product in products if product.price_id == current_plan_id), None)

            
            if not user_plan:
                if current_plan_id == "free_plan": #adding stripe's free plan package
                    user_plan=next((product for product in products if product.price_id == "price_1RWBNuCI2zbViAE2N6TkeNVB"), None)
                else:
                    print("couldn't find user_plan")
                    return False  # Couldn't find the user's plan
            
            
            # Get the credit limit for this plan 
            plan_credits = float(user_plan.credits)  
            if user_plan and user_plan.product_id =="free_plan":
                plan_credits = float(50000) #free users can use 50000 credits
            
            # Apply the same threshold calculation (5% of credits)
            credit_threshold = plan_credits / 20000
            
            # Return True if user has exceeded their limit
            return user_cost >= credit_threshold
        
        # Standard handling for regular tenants
        else:
            # First get organization's credits
            org_mongo_handler = get_mongo_db(
                db_name=db_name,
                collection_name="organizations"
            )
            
            org_details = await org_mongo_handler.get_one({'_id': tenant_id}, org_mongo_handler.db)
            if not org_details or 'credits' not in org_details:
                return False
                
            CREDITS = org_details['credits']
            
            # Then get organization's cost
            cost_mongo_handler = get_mongo_db(
                db_name=db_name,
                collection_name="llm_costs"
            )
            
            org_details = await cost_mongo_handler.get_one({'organization_id': tenant_id}, cost_mongo_handler.db)
            if not org_details or not org_details.get('organization_cost'):
                return False
                
            org_cost = float(org_details['organization_cost'].replace('$', ''))
            
            # Apply threshold calculation
            return org_cost >= CREDITS / 20
            
    except Exception as e:
        print("Exception in check_free_credit_limits_crossed:", e)
        return False
    
    

    
def check_free_credit_limits_crossed_sync(tenant_id, current_user = None):
    try:
        db_prefix = settings.MONGO_DB_NAME

        if db_prefix.startswith('develop'):
            db_name = 'develop_kaviaroot'
        elif db_prefix.startswith('qa'):
            db_name = 'qa_kaviaroot'
        elif db_prefix.startswith('pre_prod'):
            db_name = 'pre_prod_kaviaroot'
        else:
            db_name = db_prefix + '_kaviaroot'
        
         # Connect using pymongo (sync client)
        client = MongoClient(settings.MONGO_CONNECTION_URI)

        if tenant_id == settings.KAVIA_B2C_CLIENT_ID and current_user:
            # Get the user ID from the current user object
            user_id = current_user.get('sub') or current_user.get('cognito:username')
            
            if not user_id:
                print("no user id")
                return True  # No user ID, can't check limits
            
            # Fetch B2C tenant's document from llm_costs collection
            db = client[db_name]
            llm_costs_collection = db['llm_costs']

            b2c_doc = llm_costs_collection.find_one({'organization_id': tenant_id})

            if not b2c_doc or 'users' not in b2c_doc:
                print("no b2c_doc")
                return True
            
            # Find the current user in the users array
            user_entry = next((user for user in b2c_doc['users'] if user.get('user_id') == user_id), None)

            if not user_entry or 'cost' not in user_entry or 'current_plan' not in user_entry:
                print("no user entry")
                return True  # User not found or missing required fields
            
            # Get user's cost and current plan
            user_cost = float(user_entry['cost'])
            current_plan_id = user_entry['current_plan']

            # Fetch all products from Stripe using your existing endpoint
            from app.routes.products_route import get_products  # Import your existing function
            
            products = []
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                products = loop.run_until_complete(get_products())
            else:
                products = asyncio.ensure_future(get_products())

            if not products:
                print("failed to fetch stripe products")
                return True

            user_plan = next((product for product in products if product.price_id == current_plan_id), None)

            if not user_plan:
                if current_plan_id == "free_plan": #adding stripe's free plan package
                    user_plan=next((product for product in products if product.price_id == "price_1RWBNuCI2zbViAE2N6TkeNVB"), None)
                else:
                    print("couldn't find user_plan")
                    return True  # Couldn't find the user's plan
                
            # Get the credit limit for this plan 
            plan_credits = float(user_plan.credits)  
            if user_plan and user_plan.product_id =="free_plan":
                plan_credits = float(50000) #free users can use 50000 credits
            
            # Apply the same threshold calculation (5% of credits)
            credit_threshold = plan_credits / 20000
            
            # Return True if user has exceeded their limit
            return user_cost >= credit_threshold
            
        else:
            db = client[db_name]

            # Collection: organizations
            org_collection = db['organizations']
            org_details = org_collection.find_one({'_id': tenant_id})

            if not org_details or 'credits' not in org_details:
                print("Org details not found")
                return True

            CREDITS = org_details['credits']

            # Collection: llm_costs
            cost_collection = db['llm_costs']
            org_details = cost_collection.find_one({'organization_id': tenant_id})

            if not org_details or not org_details.get('organization_cost'):
                print("Org details not found in llm_costs")
                return True
            
            org_cost = float(org_details['organization_cost'].replace('$', ''))

            # Apply threshold calculation
            return org_cost >= CREDITS / 20

    except Exception as e:
        print("Exception", e)
        return False