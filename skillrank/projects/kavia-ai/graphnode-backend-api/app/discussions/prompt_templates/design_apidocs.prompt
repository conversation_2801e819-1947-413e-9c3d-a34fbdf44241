{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
Create comprehensive API documentation for the current component of the software application development workflow manager.
Consider the unique aspects of workflow management in your design, such as task orchestration, state management, and system integration.
Apply your knowledge of API design best practices and derive appropriate details where necessary.
{% endblock %}

{% block task_description_post_script %}
Ensure the API design aligns with the overall system architecture and facilitates efficient workflow management.
Consider scalability, security, and ease of use for developers integrating with this API.
{% endblock %}

{% block autoconfig %}
Design API endpoints covering all necessary workflow management operations.
Think through the typical lifecycle of a development workflow and ensure your API supports all crucial stages.
Derive any additional endpoints or features that would enhance the system's functionality.
{% endblock %}

{% block auto_reconfig %}
Update the API documentation based on changes in requirements, architecture, or workflow processes.
Analyze the current documentation and system needs to identify areas for improvement or expansion.
Consider emerging trends in workflow management that could be incorporated.
{% endblock %}

{% block node_details_interactive_config %}
API endpoints, focusing on their specific workflow needs
{% endblock %}

{% block node_details_interactive_config_update_specifications %}
development process and derive appropriate API structures
{% endblock %}


{% block information_about_task %}
Current component interfaces:
{{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}

Use this information to ensure your API design integrates seamlessly with existing components.
Derive any necessary additions or modifications to these interfaces to improve overall system functionality.
{% endblock %}