from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.kg_inspect.kg_tool import KgTools
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.kg_inspect.knowledge import Knowledge

class ProjectExtraction(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None, project_id=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Project")
        self.template_name = "project_extraction.prompt"
        self.function_schema_type = "ProjectConfiguration"
        self.model_name = 'claude-3.5'
        self.project_id = project_id
        self.kg_tools = None
        self.discussion_tools = None

    async def initialize(self):
        """Initialize discussion with required tools and schemas"""
        await super().async_initialize()
        
        self.kg_tools = KgTools(
            base_path=self.base_path,
            logger=self.logger,
            user_id=self.session_id
        )

        self.discussion_tools = DiscussionTools(
            base_path=self.base_path,
            logger=self.logger,
            user_id=self.session_id,
            discussion=self
        )

        self.function_schemas = self.kg_tools.function_schemas + self.discussion_tools.function_schemas

    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Get project knowledge
        knowledge = Knowledge.getKnowledge(id=self.project_id)
        if knowledge:
            self.retrieved_info['source_languages'] = knowledge.getKeyValue('source-languages')
            self.retrieved_info['search_terms'] = knowledge.getKeyValue('search-terms')
            
        return self.retrieved_info

    def get_modifications_from_llm_output(self):
        """Process LLM output and extract project properties"""
        llm_output = self.modifications.get('modified_node', {})
        self.modifications['modified_node'] = llm_output
        return self.modifications

    async def merge_captured_items(self):
        """Update existing project node"""
        await super().merge_captured_items()
        
        # Update project properties
        if self.node_id:
            project_props = self.modifications.get('modified_node', {})
            await self.db.update_node_by_id(self.node_id, project_props)

# Register with factory
DiscussionFactory.register('extraction', ProjectExtraction, "Project")