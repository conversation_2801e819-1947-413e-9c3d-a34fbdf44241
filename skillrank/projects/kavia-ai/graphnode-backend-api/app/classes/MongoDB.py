from pymongo import  ASCENDING, DESCENDING
from app.connection.tenant_middleware import get_tenant_based_db_name
from app.connection.mongo_client import get_db_client
from app.utils.datetime_utils import generate_timestamp
from bson.json_util import dumps, loads
from app.utils.b2c_utils import get_collection_name

class MongoDBHandler:
    def __init__(self, mongo_connection_uri, db_name, collection_name, user_id=None):
        db_name = get_tenant_based_db_name(db_name)
        self.client = get_db_client()
        self.db_name = db_name
        self.db = self.client[db_name]
        self.collection = get_collection_name(db_name, collection_name, user_id)

    @property
    def db(self):
        return self.client[self.db_name]
    
    @db.setter
    def db(self, value):
        self._db = value
    
    async def set_collection(self, collection_name):
        self.collection = collection_name
    
    async def set_db(self, db_name):
        self.db_name = db_name
        self.db = self.client[db_name]
    
    def change_db(self, db_name):
        # db_name = get_tenant_based_db_name(db_name)
        print(f"Changing db to: {db_name}")
        self.db = self.client[db_name]
        self.db_name = db_name
    
    def change_collection(self, collection_name):
        self.collection = collection_name
    
    async def create(self, element, db):
        """ Create a new element in the repository. """
        insert_id = await self.insert(element, db)
        current_insert = await self.get_by_id(insert_id, db)
        return current_insert
    
    async def insert(self, element, db) -> str:
        """ Insert a new element in the repository. """
        print("Inserting element")
        print(element , db)
        return (db[self.collection].insert_one(element)).inserted_id
    async def insert_extract_data(self,collection_name: str, element, db) -> str:
        """ Insert a new element in the repository. """
        collection_name = get_collection_name(self.db_name, collection_name)
        print("Inserting element")
        print(element , db)
        return (db[collection_name].insert_one(element)).inserted_id

    async def create_many(self, elements: list, db) -> list:
        """ Create a new element in the repository. """
        ids_list = await self.insert_many(elements, db)
        return db[self.collection].find({"_id": {"$in": ids_list}}).to_list(None)

    async def insert_many(self, elements: list, db) -> list:
        """ Insert a new element in the repository. """
        return (db[self.collection].insert_many(elements)).inserted_ids

    async def get_by_id(self, id: str, db):
        """ Get an element by id. """
        return db[self.collection].find_one({'_id': id})

    async def get_by_task_id(self, uuid: str, db=None):
        if db is None:
            db = self.db
        """ Get an element by id. """
        return db[self.collection].find_one({'task_id': uuid})

    async def get_one(self, filter: dict, db, sort=None):
        """ Get an element by filter with optional sorting. """
        if sort:
            return db[self.collection].find_one(filter, sort=sort)
        return db[self.collection].find_one(filter)
    async def get_extract_data(self, filter: dict, collection_name:str,db, sort=None):
        """ Get an element by filter with optional sorting. """
        collection_name = get_collection_name(self.db_name, collection_name)
        if sort:
            result =  db[collection_name].find_one(filter, sort=sort)
        else:
            result =  db[collection_name].find_one(filter)
        if result:
            if '_id' in result:
                result['_id'] = str(result['_id'])
            return loads(dumps(result))
        return None
    
    
    async def get_latest(self, filter: dict, db):
        """ Get an element by filter with optional sorting. """
        cursor = db[self.collection].find(filter).sort("created_at", -1)
        return list(cursor)  # Convert cursor to list directly

    async def get_by(self, filter: dict, db) -> list:
        """ Get an element by id. """
        return db[self.collection].find(filter).to_list(None)

    async def get_by_projection(self, filter: dict,proj: dict, db) -> list:
        """ Get an element by id. """
        return db[self.collection].find(filter,proj).to_list(None)
    
    async def get_by_id_filter(self, id: str,filter: dict, db):
        return db[self.collection].find_one({'_id': id},filter)
        
    async def get_all_by_filter(self, filter:dict, db):
        """ Update an element in the repository. """
        return db[self.collection].find({},filter).to_list(None)
    
    async def get_adj_without_filters(self,filter,sort:int,limit:int,db):
        return db[self.collection].find(filter,{"_id": 1}).sort("_id",sort).limit(limit).to_list(None)
    async def get_by_and_sort(self, filter: dict, sort_field, db) -> list:
        """ Get an element by id. """
        return db[self.collection].find(filter).sort(sort_field).to_list(None)

    async def get_all(self, db) -> list:
        """ Get all elements in the repository. """
        return db[self.collection].find().to_list(None)

    async def get_all_by(self, filter: dict, db, page: int, per_page: int) -> list:
        """Get all elements matching the filter with pagination."""
        skip_amount = (page - 1) * per_page
        cursor = db[self.collection].find(filter).sort('_id', -1).skip(skip_amount).limit(per_page)  # Get the cursor
        resp = list(cursor)                                                                 # Convert cursor to list
        return resp
    
    async def get_all_by_projection(self, proj: dict, db,page: int,per_page :int):
        skip_amount = (page - 1) * per_page
        resp = db[self.collection].find({},proj).skip(skip_amount).limit(per_page).to_list(None)
        return resp
    async def run_aggregate_list(self, pipeline: list, db):
        cursor = db[self.collection].aggregate(pipeline)  # This returns an async cursor
        result = list(cursor)
        return result
    
    async def update_with_nested_object_and_filters(self, filter: dict, update: dict, array_filters: list, db=None):
        """
        Update nested objects in MongoDB document with array filters.
        
        Args:
            filter (dict): The filter to identify documents to update
            update (dict): The update operation to perform
            array_filters (list): Array filters for targeting specific elements
            db: Database connection (optional)
        """
        if db is None:
            db = self.db
        
        return db[self.collection].update_one(
            filter, 
            {"$set": update}, 
            array_filters=array_filters
        )
        
    async def update_one(self, filter: dict, element, upsert = False, db = None):
        
        """ Update an element in the repository. """
        if db is None:
            db = self.db
        if upsert:
            return db[self.collection].update_one(filter,{"$set":element}, upsert=True)
        return db[self.collection].update_one(filter,{"$set":element})
    async def update_one_data(self, filter: dict, element, upsert = False, db = None):
        
        """ Update an element in the repository. """
        if db is None:
            db = self.db
        if upsert:
            return db[self.collection].update_one(filter,element, upsert=True)
        return db[self.collection].update_one(filter,element)
    
    async def update_reconfig_status(self,collection:str, filter: dict, element, upsert = False, db = None):
        collection = get_collection_name(self.db_name, collection)
        print("collection",collection)
        print("filter",filter)
        print("element",element)
        
        """ Update an element in the repository. """
       
        if upsert:
            return  await self.db[collection].update_one(filter,{"$set":element}, upsert=True)
        return await  self.db[collection].update_one(filter,{"$set":element})
    
    async def push_to_array(self, filter: dict, array_field: str, value, upsert=False, db=None):
        """Add an element to an array field in the repository.
        
        Args:
            filter: The query to select the document
            array_field: The name of the array field to push to
            value: The value to push into the array
            upsert: Whether to create the document if it doesn't exist
            db: The database to use (optional)
        
        Returns:
            The result of the update operation
        """
        if db is None:
            db = self.db
        
        push_operation = {"$push": {array_field: value}}
        return db[self.collection].update_one(filter, push_operation, upsert=upsert)
                                              
    async def update(self, id: str, element, db):
        """ Update an element in the repository. """
        return db[self.collection].update_one({'_id': id}, {'$set': element})
    
    async def update_by_task_id(self, uuid: str, element, db=None, collection = None):
        if db is None:
            db = self.db
        if collection is None:
            collection = self.collection
        """ Update an element in the repository. """
        return db[collection].update_one({'task_id': uuid}, {'$set': element})
    
    async def update_many(self, filter: dict, element, db):
        """ Update an element in the repository. """
        return db[self.collection].update_many(filter, {'$set': element})
    
    
    async def update_filter(self, id: str, filter, db):
        """ Update an element in the repository. """
        return db[self.collection].update_one({'_id': id}, filter)

    async def update_list(self, elements: list, db) -> list:
        """ Update a list of elements in the repository. """
        results = []
        for element in elements:
            result = await self.update(element['_id'], element, db)
            results.append(result)
        return results

    async def delete(self, id: str, db):
        """ Delete an element by id. """
        return db[self.collection].delete_one({'_id': id})
    
    async def get_all_documents(self,filter: dict, db, projection=None):
        cursor = db[self.collection].find(filter, projection)
        return  cursor
    
    async def delete_by_filter(self, filter:dict, db):
        """ Delete an element by uuid. """
        return db[self.collection].delete_many(filter)

    async def delete_by_uuid(self, uuid: str, db):
        """ Delete an element by uuid. """
        return db[self.collection].delete_one({'uuid': uuid})

    async def delete_many(self, filter: dict, db):
        """ Delete an element by id. """
        return db[self.collection].delete_many(filter)

    async def get_by_and_sort_with_pagination(self, filter: dict, sort_field, page: int, per_page: int, db):
        """ Get an element by id. """
        return db[self.collection].find(filter).sort(sort_field).skip(page * per_page).limit(per_page).to_list(None)

    async def exists(self, filter: dict, db) -> bool:
        """ Check if an element exists. """
        return db[self.collection].count_documents(filter) > 0
    
    async def get_by_querry_filter(self, querry:dict, filter: dict, db):
        return db[self.collection].find_one(querry,filter)

    async def get_all_by_filter(self, filter:dict, db):
        """ Update an element in the repository. """
        return db[self.collection].find({},filter).to_list(None)

    async def get_all_by_querry_filter(self, querry:dict, filter: dict, db):
        return db[self.collection].find(querry,filter).to_list(None)
    
    async def get_by_and_sort_with_pagination_projection(self, filter: dict, project:dict, sort_field, page: int, per_page: int,sort:str, direction:str, db):
        """ Get an element by id. """
        return db[self.collection].find(filter,project).sort(sort,int(direction)).skip(page * per_page).limit(per_page).to_list(None)
    
    async def get_by_and_sort_with_pagination_projection_without_page(self, filter: dict, project:dict,sort:str, direction:str, db):
        """ Get an element by id. """
        return db[self.collection].find(filter,project).sort(sort,int(direction)).to_list(None)
    async def get_by_and_sort_with_pagination_projection_with_limit(self, filter: dict, project:dict,sort:str, direction:str,lim:int, db):
        """ Get an element by id. """
        return db[self.collection].find(filter,project).sort(sort,int(direction)).limit(lim).to_list(None)

    async def count(self, filter: dict, db) -> int:
        """ Check if an element exists. """
        return db[self.collection].count_documents(filter)
    
    async def get_one_projection(self, filter: dict, projection: dict, db):
        """ Get an element by filted and projection """
        return db[self.collection].find_one(filter,projection)
    
    async def get_all_by_projection(self, projection:dict, db) -> list:
        """ Get all elements in the repository. """
        return db[self.collection].find({},projection).to_list(None)
    
    async def update_by_filter_unset(self, filter: dict, element: dict, db):
        return db[self.collection].update_many(filter,{'$set': element})
    
    async def update_by_filter(self, filter: dict, element: dict, db):
        """ Update an element in the repository. """
        return db[self.collection].update_many(filter,{'$set': element})

    async def run_aggregate(self, pipeline: list, db):
        return db[self.collection].aggregate(pipeline).to_list(None)

    async def create_index(self,colName: str,field: list, name: str, db):
        colName = get_collection_name(self.db_name, colName)
        return db[colName].create_index(field, name=name)

    async def run_update_query(self, query: dict, db):
        return db[self.collection].update(query)
    
    async def push_to_array(self, filter: dict, array_field: str, new_element,  db=None):
        """
        Pushes an element to an array field in a MongoDB document.

        Args:
            filter (dict): Filter to identify the document to modify.
            array_field (str): Name of the array field within the document.
            new_element: The element to be pushed.
            max_size (int, optional): The maximum size of the array. If provided, will limit the array
            db: An optional pre-established database connection. Defaults to using the class's 'db' attribute. 
        Returns:
            bool: True if the update was successful, False otherwise.
        """

        if db is None:
            db = self.db  # Use the default database connection

        update_query = {
            "$push": { array_field: new_element }
        }

        try:
            result = db[self.collection].update_one(filter, update_query)
            return result.modified_count == 1  
        except Exception as e:
            print(f"Error updating array: {e}")
            return False
    
    async def get_all_by_distinct(self, key:str, db) -> list:
        """ Get all elements in the repository. """
        return db[self.collection].distinct(key,{})  

    async def get_active_task(self, node_id: str, db=None):
        if db is None:
            db = self.db
    
        active_task = db[self.collection].find_one({'node_id': node_id, 'run_completed': False})
        if active_task:
            active_task.pop('_id', None)
        return active_task

    # Recommended to use the following class to track project usage
    async def track_project_usage(self, user_id: str, project_id: int, project_name: str = None, max_entries=20):
        """Tracks project usage for users in a single collection."""
        try:
            # Create unique compound index if not exists
            # self.db[self.collection].create_index(
            #     [("user_id", ASCENDING), ("project_id", ASCENDING)],
            #     unique=True,
            #     name="user_project_usage_index"
            # )

            # Upsert the usage record (update if exists, insert if not)
            upsert_query = {
                "user_id": user_id,
                "project_id": project_id
            }

            set_query = {
                "timestamp": generate_timestamp()
            }
            if project_name:
                set_query["project_name"] = project_name

            # Update existing or insert new record
            self.db[self.collection].update_one(
                upsert_query,
                {"$set": set_query},
                upsert=True
            )

            # Get the total count of entries for the user
            count = self.db[self.collection].count_documents({"user_id": user_id})
            
            # If count exceeds max_entries, delete oldest entries
            if count > max_entries:
                oldest_entry = self.db[self.collection].find_one(
                    {"user_id": user_id},
                    sort=[("timestamp", ASCENDING)]  # Sort by timestamp ascending
                )
                if oldest_entry:
                    self.db[self.collection].delete_one({"_id": oldest_entry["_id"]})

        except Exception as e:
            print("Error tracking project usage: ", e)

    async def get_recent_project_usage(self, user_id: str, max_entries=20):
        """Retrieves the most recent project usage records for the user."""
        try:
            recent_projects = list(self.db[self.collection].find(
                {"user_id": user_id},
                projection={"_id": 0},  # Exclude the _id field
                sort=[("timestamp", DESCENDING)],
                limit=max_entries
            ))
            
            return recent_projects
        except Exception as e:
            print("Error fetching recent project usage:", e)
            return []

    async def delete_project_usage(self, project_id: int):
        """Deletes all usage records for the given project_id."""
        try:
            result = self.db[self.collection].delete_many({"project_id": int(project_id)})
            return result.deleted_count > 0
        except Exception as e:
            print("Error deleting project usage:", e)
            return False
        
    async def delete_notifications(self, project_id: str):
        """Deletes all notifications for the given user_id."""
        try:
            # Delete all notifications for the user
            result = self.db[self.collection].delete_many({"data.project_id": project_id})
        except Exception as e:
            print("Error deleting notifications:", e)
            return False
        

    def _serialize_object_id(self, doc):
        if doc and '_id' in doc:
            doc['_id'] = str(doc['_id'])
        return doc

    async def git_get_by(self, github_id: int):
        """Get a user by their GitHub ID."""
        user = self.db[self.collection].find_one({'github_id': github_id})
        return self._serialize_object_id(user)

    async def git_get_by_user_id(self, user_id: str):
        """Get a user by their userId."""
        user = self.db[self.collection].find_one({'userId': user_id})
        return self._serialize_object_id(user)
    async def git_get_by_scm_id(self, scm_id: str):
        """Get a repo by scm id ."""
        scm_val = self.db[self.collection].find_one({'scm_id': scm_id})
        return self._serialize_object_id(scm_val)

    async def git_update(self, github_id: int, element: dict):
        """Update a GitHub user in the repository."""
        result = self.db[self.collection].update_one({'github_id': github_id}, {'$set': element})
        return result.modified_count > 0

    async def git_create(self, user_data: dict):
        """Create a new GitHub user in the repository."""
        try:
            result = self.db[self.collection].insert_one(user_data)
            user_data["_id"] = str(result.inserted_id)
            return user_data
        except Exception as e:
            print(f"Error inserting user data: {e}")
            return None

    async def git_exists(self, github_id: int) -> bool:
        """Check if a GitHub user exists in the repository by their GitHub ID."""
        count = self.db[self.collection].count_documents({'github_id': github_id})
        return count > 0
    
    async def git_delete(self, github_id: int):
        """Delete a GitHub user from the repository."""
        result = self.db[self.collection].delete_one({'github_id': github_id})
        return result.deleted_count > 0
    async def git_delete_by_userId(self,userId :str,collection :str):
        """Delete a GitHub user from the repository."""
        collection = get_collection_name(self.db_name, collection)
        result = self.db[collection].delete_one({'userId': userId})
        return result
    
    
    async def create_project_trace(self, project_data: dict) -> dict:
        """
        Create or update a project trace document in MongoDB.
        
        Args:
            project_data (dict): The project data to store
            
        Returns:
            dict: The created/updated document
        """
        try:
            # Use langtrace_project_id as the unique identifier
            result = self.db[self.collection].update_one(
                {"langtrace_project_id": project_data["langtrace_project_id"]},  # Filter by langtrace_project_id
                {"$set": project_data},      # Update/insert the entire project data
                upsert=True                  # Create if doesn't exist, update if it does
            )
            
            # Retrieve and return the document
            if result.upserted_id or result.modified_count > 0:
                stored_document = self.db[self.collection].find_one({"langtrace_project_id": project_data["langtrace_project_id"]})
                return self._serialize_object_id(stored_document)
            
            return None
            
        except Exception as e:
            print(f"Error creating/updating project trace: {e}")
            return None

    async def get_project_trace(self, project_id: str) -> dict:
        """
        Retrieve a project trace by its ID.
        
        Args:
            project_id (str): The project's ID
            
        Returns:
            dict: The project trace document or None if not found
        """
        try:
            document = self.db[self.collection].find_one({"id": project_id})
            return self._serialize_object_id(document)
        except Exception as e:
            print(f"Error retrieving project trace: {e}")
            return None

    async def get_project_traces_by_team(self, team_id: str) -> list:
        """
        Retrieve all project traces for a specific team.
        
        Args:
            team_id (str): The team's ID
            
        Returns:
            list: List of project trace documents
        """
        try:
            cursor = self.db[self.collection].find({"teamId": team_id})
            documents = list(cursor)
            return [self._serialize_object_id(doc) for doc in documents]
        except Exception as e:
            print(f"Error retrieving team project traces: {e}")
            return []