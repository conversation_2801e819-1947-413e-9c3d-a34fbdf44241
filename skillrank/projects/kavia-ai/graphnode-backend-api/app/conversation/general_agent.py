import logging
from jinja2 import Environment, FileSystemLoader
from app.connection.establish_db_connection import get_node_db
from typing import Any, List, AsyncGenerator
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from app.conversation.tools.project_tool import ProjectTools
from app.models.chat_model import Chat<PERSON>ontext
from app.core.Settings import settings
from openai import OpenAI
import json
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.conversation_utils import ConversationUtils
from app.connection.establish_db_connection import get_mongo_db
from app.utils.logs_utils import get_path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]  # This ensures only one StreamHandler
)

# Create a logger instance
logger = logging.getLogger(__name__)

client = OpenAI(api_key=settings.OPENAI_API_KEY)


class GeneralAgent():
    
    def __init__(self, context: ChatContext) -> None:
        
        template = Environment(loader=FileSystemLoader('app/conversation/prompts')).get_template('conversation.prompt')
        self.model_name = 'gpt-4-turbo'
        self.node_types = ['project', 'requirement', 'architecture', 'design']
        self.system_prompt = template.render(prompt_type="system", node_types=self.node_types)
        self.node_db = get_node_db()
        self.utils = ConversationUtils(context)
        self.user_id = context.user_id
        
    async def _submit_completion(self, context, messages: List[Any]) -> AsyncGenerator[str, None]:

        # tools methods
        general_registry = ToolRegistry()
        general_registry.register_tool("ProjectTools", ProjectTools)

        general_factory = DynamicToolFactory(general_registry)
        general_exec_agent = general_factory.create_dynamic_tool(["ProjectTools"])
        llm = LLMInterface(str(get_path()), 'general_agent', context.user_id, context.project_id, context.agent_name, mongo_handler = get_mongo_db())
        general_tool = general_exec_agent('/test', logger=None, user_id=context.user_id)
        
        llm_response = ""
        cp = messages.copy()
        messages.append({"role": "user","content":  context.message})
        async for res in await llm.llm_interaction_wrapper(
            messages=cp,
            user_prompt=context.message,
            system_prompt=self.system_prompt,
            response_format={'type': 'text'},
            model=self.model_name,
            stream=True,
            function_schemas=general_tool.function_schemas,
            function_executor=general_tool.function_executor
        ):
            if isinstance(res, dict):
                
                status = res['status']
                message = res['message']

                if status == 200:
                    llm_response = 'Asking for the confirmation'
                    yield f"data: {json.dumps({'content': llm_response})}\n\n"

                    yield f"data: {json.dumps({'function_call' : message} )}\n\n"
                else:
                    llm_response += message
                    yield f"data: {json.dumps({'content': message})}\n\n"
                    
            elif "Checking the data" in res:
                #TODO: need to handle the indication in proper way
                yield f"data: {json.dumps({'content':  'Your Project-related insights will be available shortly...'})}\n\n"

            elif "Fetching the data" in res: 
                yield f"data: {json.dumps({'content': 'Please wait...'})}\n\n"

            else:
                llm_response += res
                yield f"data: {json.dumps({'content': llm_response})}\n\n"
                    
        messages.append({"role": "assistant","content":  llm_response})
        
        await self.utils._ask_for_title(context.discussion_id, messages.copy())
        await self.node_db.update_node_by_id(context.discussion_id, {'Discussion': json.dumps(messages)})
        
        yield f"data: {json.dumps({'discussion_id': context.discussion_id})}\n\n"
        yield f"event: stop\n"
        yield f"data: stopped\n\n"
                
    async def interface(self, context: ChatContext) -> AsyncGenerator[bytes, None]:
        try:
            messages = await self.utils._load_discussion(context.discussion_id,count=5)
            logger.info(f"GENERAL AGENT: Loaded {len(messages)} messages for discussion ID {context.discussion_id}")
            
            # set the limit for user discussion as 20 iteration
            if len(messages) > 40:
                logger.warning(f"Discussion limit reached for discussion ID {context.discussion_id}")
                async for event in self.utils._yield_event('Discussion limit reached'):
                    yield event
                return
            
            async for response in self._submit_completion(context, messages):
                yield response
                
        except Exception as e:
            logger.error(f"Error in interface method: {str(e)}", exc_info=True)
            yield f"data: {json.dumps({'error': 'An error occurred'})}\n\n".encode('utf-8')
                        
        