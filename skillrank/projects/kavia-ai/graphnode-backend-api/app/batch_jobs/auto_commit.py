import os
import json
from datetime import datetime
import asyncio
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import get_tenant_id, tenant_context
from app.core.git_tools import EnhancedGitTools
from app.utils.message_utils import send_agent_message_custom, send_git_message
from app.utils.datetime_utils import generate_timestamp

class AutoCommitRunner:
    def __init__(self, task_id: str, repository_metadata: dict, base_path: str, context: dict = None, auto_push: bool = True):
        self.task_id = task_id
        self.repository_metadata = repository_metadata
        self.base_path = base_path
        self.context = context or {}
        self.auto_push = auto_push
        self.ws_client = None
        self.git_tool = None
        
    def initialize(self):
        """Initialize necessary components"""
        try:
            # Initialize Git Tool only - no LLM needed
            self.git_tool = EnhancedGitTools(
                callback_functions=None,
                base_path=self.base_path,
                logger=None,
                repository_metadata=self.repository_metadata,
                tenant_id=get_tenant_id()
            )
            
            return True
        except Exception as e:
            print(f"Error initializing auto commit runner: {e}")
            return False

    async def execute_auto_commit(self):
        """Execute the auto commit operation"""
        try:
            # Get current status
            status = self.git_tool.git_status(repository_path=self.base_path)
            print(f"Git status: {status}")
            # Check if there are changes to commit
            if "nothing to commit" not in status.lower() and "working tree clean" not in status.lower():
                # Generate simple timestamp-based commit message
                timestamp = generate_timestamp()
                commit_message = f"Auto-commit: Changes as of {timestamp}"
                
                # Add step info to message if available
                step_info = self.context.get("step_info", {})
                if step_info and isinstance(step_info, dict):
                    step_title = step_info.get("title", "")
                    if step_title:
                        commit_message = f"Auto-commit: {step_title} - {timestamp}"
                
                # Add all changes
                try:
                    add_result = self.git_tool.git_add_all(repository_path=self.base_path)
                    send_git_message(add_result, task_id=self.task_id, repository_metadata=self.repository_metadata, base_path=self.base_path, ws_client=self.ws_client, db=None)
                except Exception as e:
                    print(f"Error adding files: {e}")
                    # Continue anyway - try to commit what we can
                
                # Commit changes
                try:
                    commit_result = self.git_tool.git_commit(
                        commit_message, 
                        repository_path=self.base_path
                    )
                    send_git_message(commit_result, task_id=self.task_id, repository_metadata=self.repository_metadata, base_path=self.base_path, ws_client=self.ws_client, db=None)
                except Exception as e:
                    print(f"Error committing changes: {e}")
                    return False

                # Push changes only if auto_push is enabled
                if self.auto_push:
                    try:
                        push_result = self.git_tool.git_push(repository_path=self.base_path)
                        send_git_message(push_result, task_id=self.task_id, repository_metadata=self.repository_metadata, base_path=self.base_path, ws_client=self.ws_client, db=None)
                    except Exception as e:
                        print(f"Error pushing changes: {e}")
                        # Continue anyway - commit succeeded

                return True
            
            return False
            
        except Exception as e:
            error_msg = f"Error in auto-commit: {str(e)}"
            print(error_msg)
            try:
                if self.ws_client:
                    send_agent_message_custom(
                        self.ws_client,
                        self.task_id,
                        error_msg,
                        None
                    )
            except:
                pass
            return False

def run_auto_commit():
    """Main function to run auto commit"""
    try:
        # Get environment variables
        input_args = os.environ.get("input_arguments")
        if not input_args:
            print("No input arguments found")
            return
        
        try:
            input_args = json.loads(input_args)
        except json.JSONDecodeError:
            print("Invalid input arguments JSON")
            return
            
        task_id = input_args.get("task_id")
        repository_metadata = input_args.get("repository_metadata")
        base_path = input_args.get("base_path")
        tenant_id = input_args.get("tenant_id")
        context = input_args.get("context", {})
        auto_push = input_args.get("auto_push", True)
        
        if not all([task_id, repository_metadata, base_path, tenant_id]):
            print(f"Missing required arguments. TaskID: {task_id}, RepoMeta: {bool(repository_metadata)}, BasePath: {base_path}, TenantID: {tenant_id}")
            return
        
        # Set tenant context
        tenant_context.set(tenant_id)
        
        # Initialize runner with context and auto_push settings
        runner = AutoCommitRunner(
            task_id=task_id, 
            repository_metadata=repository_metadata, 
            base_path=base_path,
            context=context,
            auto_push=auto_push
        )
        
        if not runner.initialize():
            print("Failed to initialize auto commit runner")
            return
        
        # Execute auto commit
        # asyncio.run(runner.execute_auto_commit()) TODO:ROLLBACK
        
    except Exception as e:
        print(f"Error in run_auto_commit: {e}")
    finally:
        # Cleanup
        if 'runner' in locals() and hasattr(runner, 'ws_client') and runner.ws_client:
            try:
                runner.ws_client.disconnect()
            except:
                pass

if __name__ == "__main__":
    run_auto_commit()