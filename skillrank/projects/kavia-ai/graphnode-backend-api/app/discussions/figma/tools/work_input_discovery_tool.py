import json
import hashlib
from enum import Enum
from pathlib import Path
from typing import Dict, Literal

import requests
from functools import lru_cache
from llm_wrapper.utils.base_tools import BaseTool
from dnparsers.figma.models import Component


class WorkInputMetadata:
    """Type definition for component metadata"""

    type: Literal['design_note', 'file_asset']
    status: Literal['completed', 'needs_processing']
    sha256: str
    work_dir: str
    input: Dict

    def __init__(self, type: Literal['design_note', 'file_asset'],
                 status: Literal['completed', 'needs_processing'],
                 sha256: str,
                 work_dir: str,
                 input: Dict):
        self.type = type
        self.status = status
        self.sha256 = sha256
        self.work_dir = work_dir
        self.input = input



class WorkInputDiscovery(BaseTool):
    """
    A tool for discovering and tracking Figma components from JSON input.
    Manages the processing state of components and provides access to them via SHA256 hashes.
    Creates and maintains a structured asset directory for component processing.
    """

    """A tool for discovering and tracking Figma components from JSON input."""

    def get_tool_name(self) -> str:
        return "WorkInputDiscovery"

    _components: Dict[str, WorkInputMetadata] = {}

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.components: Dict[str, WorkInputMetadata] = {}
        self.logger = logger
        self.assets_dir = Path(base_path) / '.assets'
        self._ensure_assets_directory()

    def as_json(self, obj):
        """Custom JSON dumps that handles our special types."""
        if hasattr(obj, 'for_json'):
            return obj.for_json()
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, (list, tuple)):
            return [self.as_json(x) for x in obj]
        if isinstance(obj, dict):
            return {k: self.as_json(v) for k, v in obj.items()}
        return obj

    def _calculate_sha256(self, data: Dict) -> str:
        """Calculate SHA256 hash of a dictionary by converting it to a sorted JSON string."""
        # First convert all special types to basic Python types
        serializable_data = self.as_json(data)
        json_str = json.dumps(serializable_data, sort_keys=True)
        return hashlib.sha256(json_str.encode('utf-8')).hexdigest()[0:8]

    def _save_component_json(self, sha256: str, component_data: Dict) -> None:
        """Save the component JSON data in its working directory."""
        component_path = Path(self.components[sha256].work_dir) / 'component.json'
        serialized_data = self.as_json(component_data)
        with open(component_path, 'w', encoding='utf-8') as f:
            json.dump(serialized_data, f, indent=2)

    def _download_images(self, frames, sha256):
        """Download images from frames, scale them, and store as base64 in the component's image directory.
        Returns dict mapping frame names to absolute image paths."""
        from PIL import Image
        import io
        import base64

        image_dir = Path(self.assets_dir) / 'images'
        image_dir.mkdir(parents=True, exist_ok=True)
        image_paths = {}

        for frame in frames:
            try:
                if not frame.get('imageUrl') or not frame.get('name'):
                    continue

                filename = frame['name'].replace(' ', '_') + '.txt'
                image_path = image_dir / filename

                response = requests.get(frame['imageUrl'])
                response.raise_for_status()

                # Open and scale image
                img = Image.open(io.BytesIO(response.content))
                width, height = img.size

                if width > 360:
                    ratio = 360 / width
                    new_size = (360, int(height * ratio))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)

                # Convert to base64
                buffered = io.BytesIO()
                img.save(buffered, format="PNG")
                img_base64 = base64.b64encode(buffered.getvalue()).decode()

                # Save base64 string to file
                with open(image_path, 'w') as f:
                    f.write(img_base64)

                image_paths[frame['name']] = str(image_path.absolute())

            except Exception as e:
                self.logger.error(f"Error processing image for frame {frame.get('name', 'unknown')}: {str(e)}")

        return image_paths
 
    def process_figma_json(self, figma_json):
        """Process Figma JSON by processing document children and downloading frame images."""
        try:
            components = [figma_json]
            component_hashes = []
            image_paths = {}
            for component in components:
                document = component.get('document')
                frames = component.get('frames', [])

                if not document:
                    continue

                children = document.get('children', [])
                sha256 = self._calculate_sha256(component)
                work_dir = self._create_component_directory(sha256)
                image_paths.update(self._download_images(frames, sha256))

                for child in children:
                        self.components[sha256] = WorkInputMetadata(
                            type=self._determine_component_type_from_json(child),
                            status='needs_processing',
                            sha256=sha256,
                            work_dir=work_dir,
                            input=child
                        )
                        self._save_component_json(sha256, child)
                        component_hashes.append(sha256)

            figma_json_without_components = {k: v for k, v in figma_json.items() if k != "figma_components"}
            figma_json_without_components['asset_images_b64'] = image_paths
            return component_hashes, figma_json_without_components
        except Exception as e:
            self.logger.error(f"Error processing Figma JSON: {str(e)}")
            raise

    def _determine_component_type_from_json(self, component: dict) -> Literal['design_note', 'file_asset']:
        """Determine component type from the original component JSON."""
        # Check the name in the json_data if it exists
        component_name = component.get('json_data', {}).get('name', '').lower()
        if any(keyword in component_name for keyword in ['note', 'documentation', 'comment']):
            return 'design_note'
        return 'file_asset'

    def _component_to_dict(self, node) -> Dict:
        """Convert a component node to a dictionary representation."""
        return {
            'id': node.id,
            'name': node.name,
            'type': node.type,
            'visible': node.visible,
            'properties': node.properties,
            'children': [self._component_to_dict(child) for child in node.children],
            'fills': node.fills,
            'strokes': node.strokes,
            'effects': node.effects,
            'characters': node.characters,
            'text_style': node.text_style,
            'interactions': node.interactions
        }

    def _layout_to_dict(self, layout) -> Dict:
        """Convert a layout object to a dictionary representation."""
        return layout.__dict__ if layout else None

    def _constraints_to_dict(self, constraints) -> Dict:
        """Convert constraints object to a dictionary representation."""
        return constraints.__dict__ if constraints else None

    def _create_component_directory(self, sha256: str) -> str:
        """
        Create a dedicated directory for a component based on its SHA256 hash.
        Returns the path to the created directory.
        """
        component_dir = self.assets_dir / sha256
        image_dir = component_dir / 'images'
        component_dir.mkdir(parents=True, exist_ok=True)
        image_dir.mkdir(parents=True, exist_ok=True)
        return str(component_dir)

    def _determine_component_type(self, component: Component) -> Literal['design_note', 'file_asset']:
        """Determine the type of a component based on its properties."""
        # This is a basic implementation - extend based on your specific needs
        if any(keyword in component.hierarchy.name.lower() for keyword in ['note', 'documentation', 'comment']):
            return 'design_note'
        return 'file_asset'

    def get_next_input(self) -> WorkInputMetadata | None:
        """Get the next uncompleted component."""
        try:
            for sha256, component in self.components.items():
                if component.status == 'needs_processing':
                    return component

            return None

        except Exception as e:
            import traceback
            traceback.print_exc()
            self.logger.error(f"Error getting next input: {str(e)}")
            return None

    def get_input(self, sha256: str) -> Dict:
        """Get a specific component by its SHA256 hash."""
        try:
            if sha256 in self.components:
                return {
                    "status": "SUCCESS",
                    "message": "Component retrieved successfully",
                    "component": self.components[sha256]
                }

            return {
                "status": "ERROR",
                "message": f"Component with SHA256 {sha256} not found"
            }

        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Error retrieving component: {str(e)}"
            }

    def set_completed(self, sha256: str) -> Dict:
        """Mark a component as completed using its SHA256 hash."""
        try:
            if sha256 not in self.components:
                return {
                    "status": "ERROR",
                    "message": f"Component with SHA256 {sha256} not found"
                }

            self.components[sha256].status = 'completed'
            # Remove component.json file, if it exists
            component_path = Path(self.components[sha256].work_dir) / 'component.json'
            if component_path.exists():
                component_path.unlink()

            return {
                "status": "SUCCESS",
                "message": f"Component {sha256} marked as completed"
            }

        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Error marking component as completed: {str(e)}"
            }

    def _ensure_assets_directory(self) -> None:
        """Ensure the .assets directory exists."""
        self.assets_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"Asset directory ensured at: {self.assets_dir}")