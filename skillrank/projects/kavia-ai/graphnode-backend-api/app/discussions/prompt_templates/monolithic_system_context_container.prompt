{% extends "base_discussion.prompt" %}

You are an expert software architect configuring a monolithic system container.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, design the monolithic container architecture based on the C4 model:

{% else %}
You are an expert system architect reviewing the Monolithic Container architecture for potential reconfiguration.

1. Original Container Architecture:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
   - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}

2. Current Architecture (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated containers: {{ new_bg.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
   - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:
1. Impact Analysis:
   - Compare original and new container architecture
   - Evaluate if changes to the monolithic container are required
   
2. Required Changes:
   - Modify existing container as needed based on the above analysis
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %}

GUIDELINES:

1. Review Requirements Context:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Users and External Systems:
     - Users: {{ details_for_discussion.get('users') | tojson(indent=2) }}
     - External Systems: {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
   - Requirements:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Monolithic Container Architecture:
   - CRITICAL ARCHITECTURAL CONSTRAINT: Create EXACTLY ONE internal container
   - This single container MUST encapsulate ALL system functionality
   - Container Properties:
     * Title: Clear, descriptive name
     * Description: Purpose and responsibilities
     * ContainerType: Set to "internal"
     * Technology: Specify appropriate technology stack
     * For new Container:
       * Use ID like 'NEW-CONTAINER-1'

{% if details_for_discussion.get('architecture_pattern', '').lower() == "monolithic-service" %}
3. Define Interfaces:
   - This container is designed as a microservice
   - Define Provider interfaces that expose functionality to external entities
   - Review functional requirements to ensure interfaces for core functionality
   - For each interface, determine and provide:
     * Appropriate interface type based on:
       - Communication patterns required
       - Performance requirements
       - Reliability needs
       - Integration constraints
     * Clear descriptive name
     * Detailed description
     * API signature examples if applicable

   Example interface definitions:
   - Arithmetic Operations Interface
     Name: ArithmeticOperationsAPI
     Description: Provides basic arithmetic operations such as addition, subtraction, multiplication, and division. This interface ensures accuracy and handles edge cases.
     API Signature:
     POST /api/arithmetic
     Request Body: { "operation": "add", "operands": [5, 3] }
     Response: { "result": 8 }

   - User Authentication Interface
     Name: UserAuthAPI
     Description: Manages user authentication, including login, password recovery, and two-factor authentication.
     API Signature:
     POST /api/auth/login
     Request Body: { "username": "user", "password": "pass" }
     Response: { "token": "jwt-token" }
{% else %}
3. Interfaces:
   - Interfaces are not required for this type of monolithic system
   - Focus on the internal container structure and responsibilities
{% endif %}

4. Define interactions between:
   - The monolithic container and users
   - The monolithic container and external systems

5. Generate C4 Container Diagram:
   - Use Mermaid syntax
   - Show the monolithic container
   - Include interfaces if applicable
   - Show relationships and dependencies

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The actual monolithic container identified
   - The interfaces if applicable
   - The specific technologies and protocols used
   - The external systems and users from the context

IMPORTANT
The diagram syntax should be provided as raw Mermaid code without the keyword "mermaid" at the beginning and without any surrounding quotes. Start directly with 'graph TB'.

{% if details_for_discussion.get('architecture_pattern', '').lower() == "monolithic-service" %}
   Monolithic Service Container Diagram Reference:
```
graph TB
    %% External API Consumers
    Consumer1["Client System 1\n[System]"]
    Consumer2["Client System 2\n[System]"]

    subgraph ServiceBoundary[Monolithic Service]
        MonolithicContainer["Service Container\n[Container: Your Technology Stack]\nImplements all service functionality"]
    end

    %% External System Containers
    ExtSystem1["External System\n[Container: External]"]
    ExtSystem2["External System\n[Container: External]"]
    
    %% Interactions
    Consumer1 -->|"REST/gRPC"| MonolithicContainer
    Consumer2 -->|"REST/gRPC"| MonolithicContainer
    MonolithicContainer -->|"Uses External API"| ExtSystem1
    MonolithicContainer -->|"Uses External API"| ExtSystem2

    %% Styling
    classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#0b4884,color:#ffffff
    classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

    class Consumer1,Consumer2 system
    class MonolithicContainer container
    class ExtSystem1,ExtSystem2 external
    class ServiceBoundary boundary
```
{% else %}
   Monolithic Container Diagram Reference:
```
graph TB
    %% Users
    ActualUser["Your Actual User Type\n[Person]\n<i>Replace with your system's\nreal user description</i>"]

    subgraph YourSystemName[Replace With Your System Name]
        %% Single monolithic container - Customize for your system
        MainContainer["Your System Name\n[Container: Your Actual Technology Stack]\n<i>Describe your system's actual\nfunctionality and responsibilities</i>"]
    end

    %% External systems - Include ONLY if your system needs it
    ExternalSystem["Your Required External System\n[System_Ext]\n<i>Only if actually needed</i>"]

    %% Relationships - Replace with your actual interactions
    ActualUser -->|"Describe actual interaction\nReal protocol"| MainContainer
    MainContainer -->|"Only if this interaction exists\nActual protocol"| ExternalSystem

    %% Styling
    classDef person fill:#08427b,stroke:#052e56,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#0b4884,color:#ffffff
    classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4
```
{% endif %}

Note: When generating the diagram:
1. Replace generic names with actual container and system names
2. Use actual technologies in container descriptions
3. Show correct protocols in relationships
4. Include only the single monolithic container
5. Represent actual system boundaries and dependencies

Change Needed: 
    - Set to False if changes are not required.

Change Log:
    - Capture history of changes.

{% endblock %}

{% block autoconfig %}
Design a monolithic container architecture that encapsulates all system functionality in a single container while ensuring proper integration with users and external systems.
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines to make the required changes to the monolithic container architecture.
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Monolithic container architecture configuration adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %} 