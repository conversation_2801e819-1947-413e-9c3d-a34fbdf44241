# GraphBuilder.py
import json
# from knowledge_graph_generator import KnowledgeGraphGenerator
# from knowledge_graph_generator.config.environment import load_environment
import os
from pathlib import Path
import logging
from pymongo import MongoClient
from app.core.Settings import settings


class BuildGraph:
    def __init__(self):
        from app.connection.establish_db_connection import get_mongo_db

        # Initialize MongoDB connection using the established connection pattern
        self.mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='users_github'
        )

    # Configure logging
    def setup_logging(self,):
        # Create logs directory if it doesn't exist
        log_dir = Path(__file__).parent / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        log_file = log_dir / "graph_generation.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(str(log_file)),
                logging.StreamHandler()  # This will also print to console
            ]
        )
        return logging.getLogger(__name__)
    
    async def generate_graph(self, project_id, username, data):
        logger = self.setup_logging()

        try:
            logger.info("Starting graph generation process")
            
            github_token = self.get_user_token(username)
            
            # Ensure data is a list of dictionaries
            if isinstance(data, str):
                config_data = json.loads(data)
            else:
                config_data = data
                        
            # Append auth_token to each repository in the configuration
            for repo in config_data:  # Since data is a list of repositories
                if repo.get('type') == 'github_repository':
                    repo['auth_token'] = github_token

            # Create the final configuration structure
            final_config = {
                "project_id": project_id,
                "project_name": "KAVIA",
                "project_description": "Kavia is a project that aims to help developers to create a better documentation for their projects",
                "repositories": config_data
            }
            
            print("CONFIG DATA : ", final_config)
            
            # kg = KnowledgeGraphGenerator(
            #     config_file=final_config,
            # )
            # logger.info("KnowledgeGraphGenerator initialized successfully")

            # flag = kg.generate()
            # logger.info("Graph generation completed successfully")
            return True

        except Exception as e:
            logger.error(
                f"Error during graph generation: {str(e)}", exc_info=True)
            raise