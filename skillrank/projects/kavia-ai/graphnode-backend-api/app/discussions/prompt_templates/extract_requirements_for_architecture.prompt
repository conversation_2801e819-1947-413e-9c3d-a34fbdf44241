{% extends "base_discussion.prompt" %}

{% block task_description %}
Extract functional and architectural requirements from the provided Epics and UserStories.
These requirements will be used to create a high-level architectural decomposition, API definitions, and component diagrams for the system or component specified in the current node.

Key points:
1. Analyze Epics and UserStories carefully.
2. Extract explicit functional requirements.
3. Infer architectural requirements, even if not explicitly stated.
4. Use your knowledge to create comprehensive architectural requirements.
5. Focus on aspects such as scalability, availability, performance, security, modularity, data storage, interfaces/APIs, infrastructure, and deployment.
6. IMPORTANT - Capture the output in JSON format include in <JSON> tag. 

{% endblock %}

{% block information_about_task %}
Epics and UserStories:
{{requirement_nodes | tojson(indent=2)}}

Existing requirements:
FunctionalRequirements: {{current_requirements.get('functional_requirements', '') | tojson(indent=2)}} 
ArchitecturalRequirements: {{current_requirements.get('architectural_requirements', '') | tojson(indent=2)}}
{% endblock %}

{% block system_prompt %}
You are an AI agent specializing in extracting and synthesizing software requirements. Your task is to analyze Epics and UserStories, extract relevant requirements, and merge them with existing requirements. Provide the output in the specified format without any additional commentary.
{% endblock %}