import os
import tempfile
import logging
from git import Repo
from urllib.parse import urlparse
from app.utils.kg_build.knowledge_helper import Knowledge_Helper
from app.utils.kg_build.knowledge import Knowledge
import time
import shutil
import uuid
import random
import string
from datetime import datetime
from app.core.Settings import settings
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import get_tenant_id
from app.utils.auth_utils import get_current_user
from app.core.task_framework import Task
from app.tasks import clone
from fastapi import HTTPException
from app.utils.datetime_utils import generate_timestamp
# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def clone_repository(git_url: str, token: str = None, target_dir: str = None) -> str:
    """
    Clone a git repository using token authentication and return its base path.
    
    Args:
        git_url (str): The URL of the git repository to clone
        token (str): Personal access token for authentication
        target_dir (str, optional): Target directory for cloning. If None, creates a temp directory
        
    Returns:
        str: The base path of the cloned repository
    """
    try:
        # If no target directory is specified, create one in the system's temp directory
        if target_dir is None:
            target_dir = tempfile.mkdtemp()
        
        # Extract repository name from git URL
        repo_name = git_url.split('/')[-1].replace('.git', '')
        repo_path = os.path.join(target_dir, repo_name)
        
        # Modify URL to include token if provided
        if token:
            parsed_url = urlparse(git_url)
            auth_url = f"https://{token}@{parsed_url.netloc}{parsed_url.path}"
        else:
            auth_url = git_url
        
        # Clone the repository
        logging.info(f"Cloning repository from {git_url} to {repo_path}")
        Repo.clone_from(auth_url, repo_path)
        logging.info("Repository cloned successfully")
        
        return repo_path
        
    except Exception as e:
        logging.error(f"Failed to clone repository: {str(e)}")
        raise

def kg_build(github_token, git_urls):
    """
    Build knowledge graph for multiple repositories in a single directory.
    
    Args:
        git_urls (list): List of git repository URLs
    """
    try:
        
        # Setup base directory
        base_dir = '/home/<USER>/workspace'
        
        # Clone all repositories
        for git_url in git_urls:
            clone_repository(git_url, token=github_token, target_dir=base_dir)
            print(f"Repository {git_url} cloned successfully")
        
        print("KG started")
        # Initialize Knowledge_Helper with the base directory
        knowledge_helper = Knowledge_Helper(base_dir)
        knowledge = Knowledge.getKnowledge()
        knowledge.start()
        print("KG completed")

        # Wait for knowledge graph generation to complete
        while not knowledge.is_complete():
            time.sleep(1)
        
        return base_dir
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return None
    
async def import_codebase(project_id, repository_data, user_id, upstream=False):
    from app.routes.kg_route import generate_build_id
    try:
        build_session_id = build_session_id = uuid.uuid4().hex

        document = {
            'project_id': project_id,
            'created_at': generate_timestamp(),
            'build_session_id': build_session_id
        }
        
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )

        # Get existing document
        existing_doc = await mongo_handler.get_one(
            filter={'project_id': project_id},
            db=mongo_handler.db
        )
        
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        data_dir = os.path.join(root_dir, 'data', get_tenant_id(), str(project_id))
        
        repositories = []
        tasks = []
        
        for repo_data in repository_data:
            _git_url = f"https://github.com/{repo_data['repo_name']}.git"

            repository = {
                "service": "github",
                'repo_id': repo_data['repo_id'],
                "repository_name": repo_data['repo_name'],
                "associated": repo_data['associated'],
                'git_url': _git_url,
                "repositoryStatus": "initialized",
                "clone_url_ssh": f"**************:{repo_data['repo_name']}.git",
                'repo_type': repo_data['repo_type'],
                'branches': [
                    {
                        'name': repo_data['branch_name'],
                        'latest_commit_hash': None,
                        'builds': {
                            'build_id': generate_build_id(),
                            'build_session_id': build_session_id,
                            'path': data_dir + "/" + repo_data['repo_name'],
                            'kg_creation_status': 1,
                            'build_info': {
                                'start_time': generate_timestamp(),
                                'end_time': None,
                                'last_updated': None,
                                'duration_seconds': None
                            },
                            'last_updated': generate_timestamp(),
                            'user_id': user_id,
                            'error': None
                        }
                    }
                ]
            }
            repositories.append(repository)
            
            # Schedule individual task for each repository
            task = Task.schedule_task(
                clone,
                project_id=project_id,
                build_session_id=build_session_id,
                data_dir=data_dir,
                repository=repository,  # Pass single repository instead of list
                tenant_id=get_tenant_id(),
                current_user=user_id, 
                upstream=upstream
            )
            tasks.append(task.to_dict())

        # Update MongoDB with all repositories
        if existing_doc:
            existing_repos = existing_doc.get('repositories', [])
            for new_repo in repositories:
                repo_exists = False
                for existing_repo in existing_repos:
                    if existing_repo['git_url'].lower() == new_repo['git_url'].lower():
                        repo_exists = True
                        new_branch = new_repo['branches'][0]
                        branch_exists = False
                        for existing_branch in existing_repo['branches']:
                            if existing_branch['name'] == new_branch['name']:
                                branch_exists = True
                                break
                        if not branch_exists:
                            existing_repo['branches'].append(new_branch)
                        break
                if not repo_exists:
                    existing_repos.append(new_repo)
            repositories = existing_repos

        document['repositories'] = repositories

        await mongo_handler.update_one(
            filter={'project_id': project_id},
            element=document,
            upsert=True,
            db=mongo_handler.db
        )

        return {
            "status": "success",
            "message": f"Scheduled {len(tasks)} repository tasks successfully",
            "build_session_id": build_session_id,
            "tasks": tasks,
            "data": document
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to import codebase: {str(e)}"
        )