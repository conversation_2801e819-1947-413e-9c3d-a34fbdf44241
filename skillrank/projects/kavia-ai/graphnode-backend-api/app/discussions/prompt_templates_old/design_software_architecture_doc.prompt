{% extends "base_discussion.prompt" %}

{% block task_description %}

To provide your response, please use the capture_discussion_output function, Now, for your specific task:

Your task is to create a software architecture document using the diagrams and documents provided here {{ details_for_discussion.get('diagrams_and_docs') | to<PERSON><PERSON>(indent=2) }}

<Architecture-Doc>
Component Name: [Name of the component]
Purpose: [A concise description of the component's purpose within the AI-based software application development workflow manager]
Responsibilities: [List of primary responsibilities and functions of this component]
Interactions: [Description of how this component interacts with other components in the system]
Data Flow: [Explanation of data inputs, outputs, and flow through this component]
Technologies: [List of technologies, frameworks, or libraries used in this component]
Scalability Considerations: [How this component addresses scalability requirements]
Security Considerations: [Security measures implemented in this component]
Performance Considerations: [How this component addresses performance requirements]
Constraints: [Any limitations or constraints of this component]
Dependencies: [List of external dependencies or services required by this component]
</Architecture-Doc>

Repeat this structure for each major component or subsystem in the architecture. Additionally, provide an overview of the entire system architecture, including:

1. High-level system diagram
2. Data model overview
3. API gateway and external interfaces
4. Deployment architecture
5. Scalability and fault tolerance strategies
6. Security architecture
7. Monitoring and logging approach

This architecture document is for an AI-based software application development workflow manager. Consider including the following components and aspects in your architecture:

1. User Interface (Web/Mobile applications)
2. API Gateway
3. Authentication and Authorization Service
4. Project Management Service
5. Task Management Service
6. AI Agent Orchestrator
7. Code Generation Service
8. Version Control Integration Service
9. Deployment and Testing Service
10. Workflow Engine
11. Data Storage (Databases, File Storage)
12. Caching Layer
13. Message Queue / Event Bus
14. Monitoring and Logging Service
15. Analytics and Reporting Service

Ensure that the architecture design takes into account the specific functionalities of the current component and its interactions with other parts of the system. Consider microservices architecture, serverless computing, and containerization where appropriate.
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
The current node is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The current node is a part of the following tree of nodes of information: {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
Here are all the interfaces that are defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{% endblock %}


{% block output_format %}
Use the function capture_discussion_output to save the output.
{% endblock %}



{%block system_prompt %}
You are an expert software designer who has a deep understanding of software component design. You have several years of software development experience. 
You are engaging in a conversation with a software architect regarding the job of creating the detailed software design 
for a component. Output any comments to  the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}