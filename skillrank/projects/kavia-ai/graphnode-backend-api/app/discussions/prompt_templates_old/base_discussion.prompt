{% if prompt_type == "user" %}
{% block user_prompt %}

{% block task_description %}
{# It is mandatory to provide a task description when extending this template.#}
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the Node that stores the information for the current node:
{{ details_for_discussion.get('current_node') | tojson(indent=2) }}

The current node is a part of a {{ root_node_type }} with the following information:
{{ details_for_discussion.get('root_node') | tojson(indent=2) }}

The current node is a part of the following tree of nodes of information:
{{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}

{% if invocation == "autoconfig" %}
    {% block autoconfig %}
    {# Content for autoconfig goes here #}
    {% endblock %}
{% elif invocation == "configuration" %}
    {% if config_state == "configured" %}
        {% block reconfig %}
        {# Content for reconfig goes here #}
        {% endblock %}
    {% else %}
        {% block config %}
        {# Content for config goes here #}
        {% endblock %}
    {% endif %}
{% endif %}
{% endblock %}

{% block background_information %}
Here are some additional Background information about the current node:

Here is the list of sibling nodes:
{{ details_for_discussion.get('sibling_nodes') | tojson(indent=2) }}

Here is the list of child nodes:
{{ details_for_discussion.get('child_nodes') | tojson(indent=2) }}

Here is the list of other relevant nodes found through a vector similarity search:
{{ details_for_discussion.get('other_relevant_nodes') | tojson(indent=2) }}

The background information above should be used primarily to make sure new nodes created are not duplicates of existing nodes.
{% endblock %}

{% block ai_agents_info %}
This project will be executed by a single human and a group of AI agents. Here are the list of AI agents that will be involved in the project:
{{ ai_agents | tojson }}
{% endblock %}

{% block latest_modifications %}
{% if details_for_discussion.get('latest_modifications') %}
Please base your response and output primarily on these latest modifications:
{{ details_for_discussion.latest_modifications | tojson(indent=2) }}

Use this information as the most up-to-date context for the discussion. Any previous data should be considered outdated if it conflicts with these modifications.
{% endif %}
{% endblock %}

{% block task_specific_instructions %}
{% endblock %}

{% endblock %}
{% endif %}

{% if prompt_type == "system" %}
{% block system_prompt %}
You are an AI assistant specializing in software development and project management. Your task is to analyze, review, and help improve various aspects of software projects. Engage in a dialogue with the user, asking questions and providing suggestions to refine the project details. Always respond in natural language.

Key instructions:
1. Analyze the provided information critically.
2. Ask clarifying questions if any aspect is unclear.
3. Suggest improvements or additions based on best practices.
4. When the user requests changes, summarize them clearly.
5. Use the capture_discussion_output function to save changes when appropriate.
6. Provide periodic progress updates during your processing. These updates should be prefixed with "[PROGRESS UPDATE]" and give insight into your current task.
7. Prioritize the latest information over initial data when discussing or suggesting changes.

{% block additional_system_instructions %}
{% endblock %}
{% endblock %}
{% endif %}