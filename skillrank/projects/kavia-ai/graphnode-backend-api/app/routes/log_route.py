from fastapi import  HTTPException, APIRouter
from fastapi.responses import HTMLResponse,PlainTextResponse
import os

_SHOW_NAME = "logs"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)
# Get the current directory (where this file is located)
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# Go up two levels to reach the project root
PROJECT_ROOT = os.path.dirname(os.path.dirname(CURRENT_DIR))

# Join with "logs" to get the logs folder path
LOGS_FOLDER = os.path.join(PROJECT_ROOT, "logs")

@router.get("/", response_class=HTMLResponse)
async def list_logs():
    try:
        files = os.listdir(LOGS_FOLDER)
        log_files = [f for f in files if f.endswith('.log')]
        
        html_content = """
        <html>
        <head>
            <title>Log Files</title>
            <script>
                function viewLog(filename) {
                    window.location.href = '/logs/' + filename;
                }
            </script>
        </head>
        <body>
            <h1>Log Files</h1>
            <ul>
        """
        
        for log_file in log_files:
            html_content += f'<li><a href="#" onclick="viewLog(\'{log_file}\'); return false;">{log_file}</a></li>'
        
        html_content += """
            </ul>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/{filename}", response_class=PlainTextResponse)
async def get_log(filename: str):
    file_path = os.path.join(LOGS_FOLDER, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Log file not found")
    
    try:
        with open(file_path, 'r') as file:
            content = file.read()
        return PlainTextResponse(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))