import asyncio
import json
import re
from typing import Dict, List, Union
import anthropic
from llm_wrapper.core.llm_interface import LLMInterface
import os
from app.utils.logs_utils import get_path
from .validateType import read_json_file, validate_mermaid_code

async def llm_fix_attempt(llm, mermaid_code, max_attempts=2):
    """
    Attempt to fix invalid Mermaid diagram code using Claude 3.5 Sonnet model via LLM wrapper.
    Args:
        llm: LLMInterface instance
        mermaid_code (str): The invalid Mermaid diagram code
        max_attempts (int): Maximum number of fix attempts
    Returns:
        tuple: (corrected_code, is_fixed)
    """
    attempts = 0
    current_code = mermaid_code
    while attempts < max_attempts:
        system_prompt = "You are an expert in Mermaid diagram syntax. Fix the provided Mermaid diagram code that has syntax errors. Return only the corrected code without any explanations or additional text. Preserve the original structure and meaning of the diagram while ensuring valid syntax."
        user_content = f"Fix this Mermaid diagram code:\n```mermaid\n{current_code}\n```"
        try:
            response = await llm.llm_interaction_wrapper(
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_content
                    }
                ],
                user_prompt=f"Fix Mermaid syntax: {current_code}",
                system_prompt=system_prompt,
                response_format=None,  # Remove response_format for Claude 3
                model="claude-3-5-sonnet-20241022",
                stream=False
            )
            # Handle the response
            completion = str(response)
            # Clean up the response
            corrected_code = completion.strip()
            if corrected_code.startswith("```mermaid"):
                corrected_code = corrected_code.replace("```mermaid", "").replace("```", "").strip()
            # Validate the corrected code
            is_valid, _ = validate_mermaid_code(corrected_code)
            if is_valid:
                return corrected_code, True
        except Exception as e:
            print(f"Error in LLM fix attempt: {str(e)}")
        attempts += 1
    # If we couldn't fix it after max attempts, return the original code
    return mermaid_code, False


async def fix_error_code():
    invalid_diagram = await read_json_file('')
    current_dir = os.getcwd()
    path = os.path.join(current_dir)

    client = anthropic.Anthropic(
        # defaults to os.environ.get("ANTHROPIC_API_KEY")
    )

    results = []
    
    for diagram in invalid_diagram['invalid_container_diagram']:
        mermaid_code = diagram['diagram']
        is_fixed = False
        
        for attempt in range(3):
            print(f"Attempt {attempt + 1} to fix Mermaid code...")
            
            corrected_code, is_valid = await llm_fix_attempt(client, mermaid_code, attempt)
            
            if is_valid:
                print(f"Successfully fixed on attempt {attempt + 1}")
                results.append({
                    "original_code": mermaid_code,
                    "corrected_code": corrected_code,
                    "attempts_needed": attempt + 1
                })
                is_fixed = True
                break
            
            print(f"Attempt {attempt + 1} failed, trying different approach...")
            
        if not is_fixed:
            print("Failed to fix code after 3 attempts")
            results.append({
                "original_code": mermaid_code,
                "corrected_code": None,
                "attempts_needed": 3,
                "status": "failed"
            })
    
    return results

if __name__ == "__main__":
    results = asyncio.run(fix_error_code())
    
    # Print summary
    print("\nResults Summary:")
    for idx, result in enumerate(results, 1):
        print(f"\nDiagram {idx}:")
        print(f"Fixed: {result['corrected_code'] is not None}")
        print(f"Attempts needed: {result['attempts_needed']}")