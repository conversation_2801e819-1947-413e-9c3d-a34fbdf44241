{% extends "base_discussion.prompt" %}

{% block task_description %}
Review the current architectural node and determine whether to split it into smaller child components. Consider:
1. Component hierarchy:
   - Multiple components at the top level
    Split if:
   - Functionality is complex, requiring several unique functionalities
   - It's a core and unique part of the system

    If not splitting, set IsArchitecturalLeaf to 'yes'.

    If splitting:
    1. Create new sub-components
    2. For each component/sub-component:
       - List technology choices and recommend one
    3. Update information about the current node
{% endblock %}

{% block background_information %}
Functional requirements: {{ details_for_discussion.get('functional_requirements') }}
Architectural requirements: {{ details_for_discussion.get('architectural_requirements') }}
Existing architecture:
Top levels: {{ details_for_discussion.get('top_levels') | tojson(indent=2) }}
Relationships: {{ details_for_discussion.get('relationships') | tojson(indent=2) }}
{% endblock %}

{% block system_prompt %}
As an expert software architect, design scalable and maintainable architectures.
Consider performance, scalability, and maintainability.
Address all key functional and non-functional requirements.
Create a mermaid chart for the architecture and include it in corresponding field in schema.
{% endblock %}

{% block output_format %}
Special output format for this task:
- ArchitecturalElements array should be empty if IsArchitecturalLeaf is "yes"
- New architectural element IDs: NEW-ARCH-1, NEW-ARCH-2, etc.
- For interfaces with existing nodes, use EXISTING-[node_id]
- Include the mermaid chart in the Mermaid-chart and also enclose it in <Mermaid> tags
{% endblock %}