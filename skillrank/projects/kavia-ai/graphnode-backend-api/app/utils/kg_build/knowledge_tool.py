from code_generation_core_agent.agents.tools.base_tool_interface import BaseToolInterface
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from app.utils.kg_build.knowledge import Knowledge

class KnowledgeTools(BaseToolInterface):
    def get_tool_name(self):
        return "KnowledgeTools"

    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.base_path = base_path
        self.output_callback = callback_functions.get("terminal_output_callback", None)
        self.executor = executor
        self.logger = logger

    def set_output_callback(self, callback):
        self.output_callback = callback

    @register_function(
        description="Get the list of available knowledge keys"
                    "Example: KnowledgeTools_get_keys() ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
            },
            "required": []
        }
    )
    def get_keys(self):
        knowledge = Knowledge.getKnowledge()
        result = {
                "status": "SUCCESS",
                "keys": f"{knowledge.getKeys()}"
        }
        return result

    @register_function(
        description="Get the value associated with a specified knowledge keys"
                    "Example: KnowledgeTools_get_key_value('search-terms') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "key": {"type": "string", "description": "The knowledge key to being queried."},
            },
            "required": ["key"]
        }
    )
    def get_key_value(self, key):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.getKeyValue(key)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Find relevant files"
                    "Example: KnowledgeTools_find_relevant_files([<list of search terms])",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "search_terms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of search terms.",
                },
                "and_search": {
                    "type": "boolean",
                    "description": "True for AND search, False for OR search",
                }
            },
            "required": ["search_terms", "and_search"]
        }
    )
    def find_relevant_files(self, search_terms, and_search):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.findRelevantFiles(search_terms, and_search)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Get source file description"
                    "Example: KnowledgeTools_get_source_file_description('<fully_qualified_source_file_name>') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {"type": "string", "description": "The fully qualified path to the file."},
            },
            "required": ["file_path"]
        }
    )
    def get_source_file_description(self, file_path):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.getSourceFileDescription(file_path)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result

    @register_function(
        description="Get source file knowledge"
                    "Example: KnowledgeTools_get_source_file_knowledge('<fully_qualified_source_file_name>') ",
        parameters={
            "type": "object",
            "strict": True,
            "properties": {
                "file_path": {"type": "string", "description": "The fully qualified path to the file."},
            },
            "required": ["file_path"]
        }
    )
    def get_source_file_knowledge(self, file_path):
        knowledge = Knowledge.getKnowledge()
        status = "ERROR"
        value = knowledge.getSourceFileKnowledge(file_path)
        if value:
            status = "SUCCESS"
        result = {
                "status": f"{status}",
                "value": f"{value}"
        }
        return result