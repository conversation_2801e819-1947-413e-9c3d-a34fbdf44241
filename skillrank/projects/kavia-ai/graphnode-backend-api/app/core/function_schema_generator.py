import json, os
from app.core.data_model_helper import data_model

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
discussion_config_path = os.path.join(base_dir, 'app', 'discussions', 'prompt_templates', 'discussion_configurations.json')

data_type_instructions = {'rich_text': "1) Use actual newlines `\n` for line breaks, NEVER escaped backslashes like \\\\n;\n2) Use standard markdown (**, __, #) for formatting (Example to follow: ##Bold Text##\n **Point**);\n3)Do not include the property name as a title;\n4) Avoid combining - and # in single lines;\n5) Close all markdown tags properly (For example: ##Bold Text##\n **Point**)."}

def load_json_file(file_path):
    with open(file_path, 'r') as file:
        return json.load(file)

def get_function_schema(discussion_type):
    # Load configuration files
    discussion_configs = load_json_file(discussion_config_path)

    # Get the configuration for this discussion type
    config_key = f"{discussion_type}"

    if config_key not in discussion_configs:
        raise ValueError(f"Configuration for '{config_key}' not found.")
    
    config = discussion_configs[config_key]
    node_type = config['modified_node_type']

    # Get the node info from the data model
    if node_type not in data_model['model']:
        raise ValueError(f"Node type '{node_type}' not found in data model.")
    
    node_info = data_model['model'][node_type]
     # Add formatting instructions to schema description
    formatting_instructions = """
    - The `modified_node` property should always be an object type.
    - The modified_node object should only contain direct node properties (Title, Description, etc).
    - Do not nest new_child_nodes or other structural elements inside modified_node.

    CRITICAL: 
        1) NEVER use escaped backslashes (\\\\n) in ANY field - use actual line breaks instead;
        2) Please ensure that the `type` for each property is followed exactly as specified in the schema;
        3) All properties in `required` must be present in the output;
    """

    # Start building the function schema
    function_schema = {
        "type": "function",
        "function": {
            "name": "capture_discussion_output",
            "description": f"Save all consolidated modifications from the current discussion for {discussion_type} of a {node_type} node. This function is called once at the end of a discussion. The parameters should reflect all the changes made during the discussion.",
            "parameters": {
                "type": "object",
                "properties": {
                    "modified_node": {
                        "type": "object",
                        "description": "Specified fields of the current node, with or without any modifications as required by this discussion",
                        "properties": {},
                        "additionalProperties": False,  # Following 2020-12 schema
                        "required": config.get('modifiable_fields', [])
                    }
                },
                "required": ["modified_node", "reason_for_this_call"],
                "additionalProperties": False  # Following 2020-12 schema
            }
        }
    }

    if 'function' in function_schema and 'description' in function_schema['function']:
        function_schema['function']['description'] = function_schema['function']['description'] + "\n\n" + formatting_instructions
        

    # Populate modified_node schema
    modifiable_fields = config.get('modifiable_fields', [])
    for attr, attr_info in node_info['attributes'].items():
        instruction = data_type_instructions.get(node_info.get('ui_metadata', {}).get(attr, {}).get('display_type', ''), '')
        if not modifiable_fields or attr in modifiable_fields:

            if 'items' in attr_info:
                function_schema["function"]["parameters"]["properties"]["modified_node"]["properties"][attr] = {
                "type": convert_type(attr_info['type']),
                "items": {"type" : convert_type(attr_info['items'].get('type',""))},
                "description": attr_info['description'] + ('\n\n' + f'Format as proper markdown: {instruction}' if instruction else '')
                }
            else:
                function_schema["function"]["parameters"]["properties"]["modified_node"]["properties"][attr] = {
                "type": convert_type(attr_info['type']),
                "description": attr_info['description'] + ('\n\n' + f'Format as proper markdown: {instruction}' if instruction else '')
                }

    # Handle child node schemas based on configuration
    new_children_types = config.get('new_children_type', [])
    if new_children_types:
        is_required = config.get('required_new_children_type',False)
        # Always include new_child_nodes for configurations that allow new children
        function_schema["function"]["parameters"]["properties"]["new_child_nodes"] = {
            "type": "array",
            "description": f"New child nodes created during this discussion. Use this for completely new nodes that don't already exist.",
            "items": {}
        }
        
        if is_required:
            function_schema["function"]["parameters"]["required"].append("new_child_nodes")
        
        if len(new_children_types) == 1:
            child_type = new_children_types[0]
            new_child_schema = create_child_schema(child_type, config, data_model, include_id=False)
            function_schema["function"]["parameters"]["properties"]["new_child_nodes"]["items"] = new_child_schema
        else:
            new_child_schema = {
                "oneOf": [create_child_schema(child_type, config, data_model, include_id=False) for child_type in new_children_types],
                "discriminator": {  # Added for 2020-12 schema
                    "propertyName": "Type"
                }
            }
            function_schema["function"]["parameters"]["properties"]["new_child_nodes"]["items"] = new_child_schema

    # Only include modified_child_nodes if explicitly allowed in the configuration
    if config.get('modified_children', False):
        function_schema["function"]["parameters"]["properties"]["modified_child_nodes"] = {
            "type": "array",
            "description": f"Existing child nodes that were modified during the discussion. Each call MUST include the 'id' of the modified child node in the 'id' field. If no existing children were modified, this array should be empty.",
            "items": {}
        }
        
        if len(new_children_types) == 1:
            child_type = new_children_types[0]
            modified_child_schema = create_child_schema(child_type, config, data_model, include_id=True)
            function_schema["function"]["parameters"]["properties"]["modified_child_nodes"]["items"] = modified_child_schema
        else:
            modified_child_schema = {
                "oneOf": [create_child_schema(child_type, config, data_model, include_id=True) for child_type in new_children_types],
                "discriminator": {  # Added for 2020-12 schema
                    "propertyName": "Type"
                }
            }
            function_schema["function"]["parameters"]["properties"]["modified_child_nodes"]["items"] = modified_child_schema

    # Add llm_metadata if present in the data model
    if 'llm_metadata' in node_info:
        function_schema["function"]["metadata"] = {
            "llm_guidelines": {
                "usage_instruction": "Use these guidelines to inform your decisions, but do not include them in your output."
            }
        }
        function_schema["function"]["metadata"]["llm_guidelines"].update(node_info['llm_metadata'])


    # Add new_relationships schema if applicable
    if config.get('new_relationship_types'):
        new_relationship_schema = dict(
            type="array",
            description="New relationships created from the discussion.",
            items={}
        )

        if len(config['new_relationship_types']) == 1:
            # Only one relationship type
            rel_type = config['new_relationship_types'][0]
            relationship_entity = data_model['model'].get(rel_type, {})
            # Build the schema for this relationship type
            relationship_schema = dict(
                type="object",
                description=relationship_entity.get('description', ''),
                properties={},
                additionalProperties=False,  # Following 2020-12 schema
                required=[]
            )
            for attr, attr_info in relationship_entity.get('attributes', {}).items():
                relationship_schema["properties"][attr] = dict(
                    type=convert_type(attr_info.get('type', 'string')),
                    description=attr_info.get('description', f"Description for {attr}")
                )
                if attr_info.get('required', False):
                    relationship_schema["required"].append(attr)
            new_relationship_schema["items"] = relationship_schema
        else:
            # Multiple relationship types
            relationship_schemas = []
            for rel_type in config['new_relationship_types']:
                relationship_entity = data_model['model'].get(rel_type, {})
                relationship_schema = dict(
                    type="object",
                    description=relationship_entity.get('description', ''),
                    properties={},
                    additionalProperties=False,  # Following 2020-12 schema
                    required=[]
                )
                # Add 'type' property to distinguish relationship types
                relationship_schema['properties']['type'] = dict(
                    type="string",
                    enum=[rel_type],
                    description="Type of the relationship"
                )
                relationship_schema['required'].append('type')
                for attr, attr_info in relationship_entity.get('attributes', {}).items():
                    relationship_schema["properties"][attr] = dict(
                        type=convert_type(attr_info.get('type', 'string')),
                        description=attr_info.get('description', f"Description for {attr}")
                    )
                    if attr_info.get('required', False):
                        relationship_schema["required"].append(attr)
                relationship_schemas.append(relationship_schema)
            new_relationship_schema["items"] = dict(
                oneOf=relationship_schemas,  # Changed from anyOf to oneOf for 2020-12 schema
                discriminator={  # Added for 2020-12 schema
                    "propertyName": "type"
                }
            )

        function_schema["function"]["parameters"]["properties"]["new_relationships"] = new_relationship_schema

    if config.get('modified_siblings'):
        function_schema["function"]["parameters"]["properties"]["modified_sibling_nodes"] = {
            "type": "array",
            "description": "Sibling nodes modified in the discussion.",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "Title": {"type": "string"},
                    "Description": {"type": "string"}
                },
                "additionalProperties": False,  # Following 2020-12 schema
                "required": ["id"]
            }
        }

    if config.get('modified_other_nodes'):
        function_schema["function"]["parameters"]["properties"]["modified_other_nodes"] = {
            "type": "array",
            "description": "Other nodes modified in the discussion.",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "Title": {"type": "string"},
                    "Description": {"type": "string"}
                },
                "additionalProperties": False,  # Following 2020-12 schema
                "required": ["id"]
            }
        }

    if config.get('modified_relationship_types'):
        function_schema["function"]["parameters"]["properties"]["modified_relationships"] = {
            "type": "array",
            "description": "Existing relationships modified in the discussion.",
            "items": {
                "type": "object",
                "properties": {
                    "source": {"type": "string"},
                    "target": {"type": "string"},
                    "type": {"type": "string"},
                },
                "additionalProperties": False,  # Following 2020-12 schema
                "required": ["source", "target", "type"]
            }
        }

    
    function_schema["function"]["parameters"]["properties"]["reason_for_this_call"] = {
        "type": "object",
        "description": "Explain the reasons for making this function call.",
        "properties": {
            "reason": {"type": "string"},
        },
        "additionalProperties": False,  # Following 2020-12 schema
        "required": ["reason"]
    }
    
    if config.get('soft_delete', True) :
        function_schema["function"]["parameters"]["properties"]["delete_node_check"] = {
        "type": "object",
            "description": "Check if the user wants to delete something , if yes the `to_delete` has to have true and also the reason",
                "properties": {
                    "to_delete": {"type": "boolean", "description":"True if something needs to be deleted"},
                    "reason_for_deletion":{"type":"string", "description":"get reason for deletion from user, else keep none"},
                    "node_id":{"type":"array", "items": {"type" : "number"}, "description":"The node Id to be deleted"}
                }
        }

    # Ensure all object schemas in the entire structure have additionalProperties set to false
    # This is a recursive function to ensure compliance with JSON Schema draft 2020-12
    def ensure_schema_compliance(schema_obj):
        if isinstance(schema_obj, dict):
            # If this is an object schema definition, ensure it has additionalProperties=False
            if schema_obj.get('type') == 'object' and 'properties' in schema_obj:
                if 'additionalProperties' not in schema_obj:
                    schema_obj['additionalProperties'] = False
                
                # If required is not set but properties exist, add empty required array
                if 'required' not in schema_obj:
                    schema_obj['required'] = []
            
            # For anyOf/oneOf/allOf schemas, ensure they use oneOf with discriminator where possible
            if any(key in schema_obj for key in ['anyOf', 'allOf']):
                # Convert anyOf to oneOf where possible for better validation
                for key in ['anyOf', 'allOf']:
                    if key in schema_obj:
                        schema_obj['oneOf'] = schema_obj.pop(key)
                        # Try to add discriminator if possible
                        options = schema_obj['oneOf']
                        common_props = set()
                        if options and all(isinstance(opt, dict) and opt.get('type') == 'object' for opt in options):
                            # Find common properties that could serve as discriminators
                            for option in options:
                                props = option.get('properties', {}).keys()
                                if common_props:
                                    common_props &= set(props)
                                else:
                                    common_props = set(props)
                            
                            # If we have common property that could serve as discriminator, add it
                            for prop in common_props:
                                # Check if this property has enum values that could differentiate
                                has_enum = all('properties' in opt and 
                                              prop in opt['properties'] and 
                                              'enum' in opt['properties'][prop] 
                                              for opt in options)
                                if has_enum:
                                    schema_obj['discriminator'] = {
                                        'propertyName': prop
                                    }
                                    break
            
            # Recursively process all properties
            for key, value in schema_obj.items():
                ensure_schema_compliance(value)
        elif isinstance(schema_obj, list):
            for item in schema_obj:
                ensure_schema_compliance(item)
    
    # Apply recursive compliance check to the entire schema
    ensure_schema_compliance(function_schema)
    
    return function_schema

def create_child_schema(child_type, config, data_model, include_id=False):
    child_node_info = data_model['model'][child_type]
    new_child_fields = config.get('new_child_fields', {}).get(child_type, [])
    
    properties = {}
    if include_id:
        properties["id"] = {
            "type": "string",
            "description": "Unique identifier of the existing child node. This field is required for all modified child nodes."
        }
    
    for attr, attr_info in child_node_info['attributes'].items():
        instruction = data_type_instructions.get(child_node_info.get('ui_metadata', {}).get(attr, {}).get('display_type', ''), '')
        if not new_child_fields or attr in new_child_fields:
            
            if 'items' in attr_info:
                properties[attr] = {
                "type": convert_type(attr_info['type']),
                "items": {"type" : convert_type(attr_info['items'].get('type',""))},
                "description": attr_info['description'] + ('\n\n' + f'Format as proper markdown: {instruction}' if instruction else '')
                }
            else:
                properties[attr] = {
                    "type": convert_type(attr_info['type']),
                    "description": attr_info['description'] + ('\n\n' + f'Format as proper markdown: {instruction}' if instruction else '')
                }
    
    schema = {
        "type": "object",
        "properties": properties,
        "additionalProperties": False,  # Following JSON Schema 2020-12
        "required": ["id", "Title"] if include_id else ["Title"]
    }
    
    return schema

def convert_type(attr_type):
    """
    Converts data model types to JSON Schema types.
    Handles both simple types and array types with specific item types.
    """
    # Base type mapping
    type_mapping = {
        "string": "string",
        "boolean": "boolean",
        "date": "string",
        "enum": "string",
        "integer": "integer",
        "float": "number",
        "array": "array"
    }   
    # For simple types, just return the mapped type
    return type_mapping.get(attr_type, "string")

