{% extends "base_discussion.prompt" %}


{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, follow these steps for interface definition:

1. Analyze interface type and Functional and Technical requirements
2. Design API that satisfies interface requirements and capture in PublicAPIDetails using OpenAPI Specification 
3. Create appropriate child nodes (Routes, Methods, DataContracts, Protocols) based on interface type
4. Ensure consistency between interface specification and child nodes

{% else %}
You are an expert system architect reviewing the Interface definition for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Component context: 
     - Parent Component: {{ bg_info.get('component_context', {}).get('component') | tojson(indent=2) }}
     - Parent Container: {{ bg_info.get('component_context', {}).get('container') | tojson(indent=2) }}
     - Interfaces: {{ bg_info.get('component_context', {}).get('interfaces') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated component context:
     - Parent Component: {{ new_bg.get('component_context', {}).get('component') | tojson(indent=2) }}
     - Parent Container: {{ new_bg.get('component_context', {}).get('container') | tojson(indent=2) }}
     - Interfaces: {{ new_bg.get('component_context', {}).get('interfaces') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:

1. Interface Requirements Analysis:
   - Compare original vs current functional/non-functional requirements
   - Analyze impact on interface contracts
   - Review changes in component interactions

2. Component Context Changes:
   - Evaluate parent component modifications
   - Assess container technology changes
   - Review interface dependency updates

3. Required Changes:
   Modify interface definition if needed based on:
   - Updated requirements
   - Component changes
   - Technology updates
   - New integration patterns

{% endif %}

PublicAPIDetails must include (wrap in <JSON> tag):
   - Complete OpenAPI 3.0 structure
   - Info block with version and description
   - Security schemes definition
   - Reusable components and schemas
   - Detailed path operations
   - Standard error responses
   - Ensure valid JSON syntax

Change Needed: 
    - Set to True if changes are required.

Change Log:
    - Capture history of changes.
{% endblock %}

{% block task_description_post_script %}
Ensure the interface definition aligns with system architecture and component responsibilities, accurately represented in PublicAPIDetails.
{% endblock %}

{% block autoconfig %}
Follow the above guidelines and create the required child nodes based on the interface Type.
{% endblock %}


{% block node_details_interactive_reconfig %}
interface definition details
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
The interface details are: {{ details_for_discussion.get('interface') | tojson(indent=2) }}
{{details_for_discussion.child_nodes | tojson(indent=2)}}
Modify existing child nodes when possible. Create new nodes only for new definitions.
Provide all changes in a single function call.
{% endblock %}


{% block information_about_task %}
Information is stored as graph nodes. Current interface node: 
{{ details_for_discussion.get('current_node') | tojson(indent=2) }}
Incoming interfaces: {{ details_for_discussion.get('incoming_interfaces') | tojson(indent=2) }}
Product information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}

Interface source component nodes:
{{ details_for_discussion.get('source_architecture_node') | tojson(indent=2) }},
{{ details_for_discussion.get('source_design_node') | tojson(indent=2) }}

Interface target component nodes:
{{ details_for_discussion.get('target_architecture_node') | tojson(indent=2) }}, 
{{ details_for_discussion.get('target_design_node') | tojson(indent=2) }}
{% endblock %}

{% block system_prompt %}
You are an expert software architect with deep understanding of system design and proven experience in scalable architectures. Follow the 3-step process to define interfaces while engaging with developers/product managers on architectural design. Ensure responses align with capture_discussion_output.
{% endblock %}