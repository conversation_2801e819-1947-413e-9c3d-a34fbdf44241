import os
from app.core.file_watch import FileWatch
from app.connection.tenant_middleware import get_tenant_id
import json
from datetime import datetime
from enum import Enum
import tempfile
from code_generation_core_agent.agents.project_welcome_page import  ContainerType


diffPath = '.diff'
input_arguments = os.environ.get("input_arguments", "{}")
input_arguments = json.loads(input_arguments)

def custom_asdict_factory(data):
    def convert(obj):
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    return {k: convert(v) for k, v in data}

def get_efs_manifest_path(project_id: str):
    if os.environ.get('LOCAL_DEBUG'):
        base_dir = f'/tmp/kavia/{get_tenant_id()}/{project_id}/manifest'  
        
    else:
        base_dir = f'/app/data/{get_tenant_id()}/{project_id}/manifest'
        
    os.makedirs(base_dir, exist_ok=True)
    return base_dir
        

def get_codegeneration_path(agent_name = "CodeGeneration", task_id=input_arguments.get("task_id")):
    if os.environ.get('LOCAL_DEBUG'):
        if(agent_name=="DocumentCreation" or agent_name=="CodeMaintenance"):
            base_dir = f"/tmp/kavia/workspace/{task_id}"
        else:
            base_dir = '/tmp/kavia/workspace/code-generation'
    else :
        if(agent_name=="DocumentCreation" or agent_name=="CodeMaintenance"):
            base_dir = f"/home/<USER>/workspace/{task_id}"
        else:
            base_dir = f'/home/<USER>/workspace/code-generation'
    
    os.makedirs(base_dir, exist_ok=True)
    return base_dir

def get_logs_path(task_id: str):
    if os.environ.get('LOCAL_DEBUG'):
        return f'/tmp/kavia/workspace'
    else :
        return f'/tmp/kavia/logs/{task_id}'
    
def initialize_filewatch_dir():
    if os.listdir(get_codegeneration_path()) == []:
        print("No projects in the code generation path.")
        return
    base_dir = os.path.join(get_codegeneration_path() , os.listdir(get_codegeneration_path())[0])
    print(f"Initializing filewatch for {base_dir}")
    filewatch = FileWatch(base_dir=base_dir, diffs_dir=diffPath)
    return filewatch


def get_container_type(container):
    container_type = container.get('platform', '')
    print("Container -->", container)
    print("Container type --> ", container_type)
    if container.get("framework","").casefold() in ['android', 'kotlin', 'flutter']:
        return ContainerType.MOBILE.value
    if container_type == 'web':
        return ContainerType.FRONTEND.value
    elif container_type == 'backend':
        return ContainerType.BACKEND.value
    elif container_type == 'database':
        return ContainerType.DATABASE.value
    elif container_type == 'mobile':
        return ContainerType.MOBILE.value
    return ContainerType.FRONTEND.value


def convert_manifest_to_yaml_string(project_schema):
    """Convert ProjectSchema to YAML string using existing save method"""
    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            # Use existing save_to_manifest method
            project_schema.save_to_manifest(temp_path)
            
            # Read the YAML content
            with open(temp_path, 'r', encoding='utf-8') as f:
                yaml_content = f.read()
            
            return yaml_content
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except Exception as e:
        print(f"Error converting manifest to YAML: {str(e)}")
        raise
    
def load_project_schema_from_yaml_string(yaml_string):
    """Load ProjectSchema from YAML string (for when you have access to ProjectSchema)"""
    from code_generation_core_agent.agents.project_welcome_page import ProjectSchema
    try:
        # Create a temporary file with the YAML content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as temp_file:
            temp_file.write(yaml_string)
            temp_path = temp_file.name
        
        try:
            # Use existing load_from_file method
            project_schema = ProjectSchema.load_from_file(temp_path)
            return project_schema
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except Exception as e:
        print(f"Error loading ProjectSchema from YAML: {str(e)}")
        raise
    
import yaml

def yaml_to_json(yaml_string: str)-> dict:
    try:
        return yaml.safe_load(yaml_string)
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML: {e}")
    
def json_to_yaml(json_string: str) -> str:
    try:
        return yaml.dump(json.loads(json_string), default_flow_style=False, indent=2)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {e}")