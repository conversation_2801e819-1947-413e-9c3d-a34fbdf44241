{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert in software testing and quality assurance. 
Your task is to generate comprehensive test cases for a given user story.

Follow these steps:
1. Review the project description, epic details, and user story thoroughly.
2. Generate common set of tags to filter the test cases based on feature, functionality etc.,, considering overall project details, description and scope. 
    For example the tags could be related to project in the following scheme of things: 
    a. Components that it could have. 
    b. Features that it should support 
    c. Non-Fucntional items that it should satisfy.
   
3. Analyze the project description, objectives, and scope.
   a. Recommend appropriate sub-categories for functional and non-functional tests for appropriately grouping the test cases based on it, ensure that it covers the all the feature, functional and non-functional aspects of the project.
   b. Consider the industry best practices while arriving at the categories.
   c. IMPORTANT: Ensure proper separation between functional and non-functional categories:
      - Functional categories should relate to what the system does (e.g., User Authentication, Data Processing, Reporting, API Endpoints)
      - Non-functional categories should relate to quality attributes (e.g., Performance, Security, Usability, Reliability)
      - Never place non-functional aspects like Performance, Security, or Usability in the functional categories

4. If appropriate categories already exist, use them for categorizing newly generated test cases.
5. Review the summary of existing test cases that have been pre-filtered based on relevance to the current user story.
6. Perform an embedding search on these pre-filtered test cases to find potential matches or relevant cases for the current user story.
7. For any existing test cases found to be relevant through the embedding search:
   a. Evaluate their applicability to the current user story.
   b. If applicable, add their IDs to the list of existing_test_case_ids.
   c. Briefly note why each selected test case is relevant to the current user story.
8. Generate new test cases as child nodes of only relevant types of the following category : 
    functional, non-functional, stress, stability, and interoperability tests.
9. For functional and non-functional test cases, assign them to the appropriate category.
10. For each new test case:
   a. Provide appropriate tags based on the user story / epic description chosen from the common tag. 
   b. Include specific details as before (e.g., preconditions for functional tests, measurement metrics for non-functional tests, etc.)
11. Ensure that each new test case has at least 2-3 relevant tags, but don't limit it if more are applicable.
12. Determine which test cases are suitable for automation and set their CanBeAutomated flag accordingly:
    a. Consider the project's TestAutomationFramework (if already selected) when making this determination
    b. Evaluate each test case based on the following criteria:
       - Execution frequency: Tests that run frequently (e.g., regression tests) are better candidates
       - Feature stability: Tests for stable features with less frequent changes are better candidates
       - Complexity: Tests with straightforward steps and clear pass/fail criteria are better candidates
       - Execution time: Tests that are time-consuming to run manually are better candidates
       - Data dependency: Tests with less complex data setup requirements are better candidates
       - Cost-benefit ratio: Consider the effort to automate versus the long-term benefits
       - Test type: Some test types (like API tests) are generally easier to automate than others (like exploratory tests)
    c. For test cases that meet most of these criteria, set CanBeAutomated to true
    d. For test cases that don't meet most of these criteria, set CanBeAutomated to false

13. If the project doesn't have a TestAutomationFramework selected, recommend appropriate frameworks based on:
    a. The project's technology stack (e.g., Selenium for web apps, Appium for mobile)
    b. The types of interfaces and components being tested
    c. The team's likely expertise based on the project description
    d. Compatibility with the project's architecture pattern

14. IMPORTANT: Ensure that each test case is properly categorized with an appropriate CanBeAutomated flag value, and that the overall test suite provides comprehensive coverage of the user story's functionality.
{% endblock %}

{% block information_about_task %}
{{ super() }}
Project Details:
{{ details_for_discussion.get('project') | tojson(indent=2) }}

Epic Details:
{{ details_for_discussion.get('epic') | tojson(indent=2) }}

User Story Details:
{{ details_for_discussion.get('user_story') | tojson(indent=2) }}

TestCaseRoot Details:
{{ details_for_discussion.get('test_case_root') | tojson(indent=2) }}

Existing Functional Categories:
{{ details_for_discussion.get('functional_categories') | tojson(indent=2) }}

Existing Non-Functional Categories:
{{ details_for_discussion.get('non_functional_categories') | tojson(indent=2) }}

Pre-filtered and Relevant Existing Test Cases:
{{ details_for_discussion.get('relevant_existing_test_cases') | tojson(indent=2) }}

Common Tags used in the project:
{{ details_for_discussion.get('common_project_tags') | tojson(indent=2) }}
{% endblock %}

{% block autoconfig %}
Follow the guidelines provided above - create relavent test cases.
{% endblock %}

