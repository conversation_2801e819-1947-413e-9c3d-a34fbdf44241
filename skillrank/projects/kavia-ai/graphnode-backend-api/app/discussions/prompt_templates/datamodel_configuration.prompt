{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
As a data modeling expert, analyze the component data requirements and create a comprehensive data model. The data model should capture all required data structures for this container while considering interactions with other components.

Container Context:
{{ details_for_discussion.container }}

Components Analysis:
{{ details_for_discussion.components}}

{% if details_for_discussion.data_contracts %}
Existing Data Contracts:
{{ details_for_discussion.data_contracts}}
{% endif %}

Required Deliverables:

1. Entity Definitions:
   Define all entities including:
   - Entity name and description
   - Attributes with data types
   - Primary and foreign keys
   - Required constraints
   - Appropriate indices
   - Audit fields (created_at, updated_at)

2. Relationship Mappings:
   Define all relationships including:
   - Entity associations
   - Relationship types
   - Cardinality
   - Referential integrity
   - Join conditions

3. Generate ER Diagram:
   Create a Mermaid ER diagram:
   ```
   erDiagram
   [Define entities and relationships here]
   ```
   Include:
   - All entities with attributes
   - Relationship types with cardinality
   - Entity grouping by domain
   - Clear relationship labels

4. Generate Database Schema:
   Create complete SQL schema:
   ```sql
   -- Define schema
   CREATE SCHEMA IF NOT EXISTS [schema_name];
   
   -- Create tables
   CREATE TABLE [schema_name].[table_name] (
     -- Columns, constraints, indices
   );
   
   -- Define relationships
   ALTER TABLE [schema_name].[table_name]
     ADD CONSTRAINT [name] FOREIGN KEY ...
   ```

Key Considerations:

1. Performance Requirements:
   - Design efficient indices
   - Optimize data access patterns
   - Consider query patterns
   - Enable data scaling

2. Data Integrity:
   - Validate data constraints
   - Maintain referential integrity
   - Handle transactions
   - Ensure consistency

3. Storage Strategy:
   - Select appropriate DB engine
   - Define schema organization
   - Plan for data growth
   - Consider data distribution

Format all entity and relationship definitions as structured JSON in the appropriate fields.

Example Entity Definition:
```json
{
  "name": "entity_name",
  "description": "Entity description",
  "attributes": [
    {
      "name": "id",
      "type": "uuid",
      "isPrimary": true,
      "description": "Unique identifier"
    },
    {
      "name": "field_name",
      "type": "data_type",
      "constraints": ["NOT NULL"],
      "description": "Field description"
    }
  ]
}
```

Example Relationship Definition:
```json
{
  "source": "entity_name",
  "target": "related_entity",
  "type": "one-to-many",
  "description": "Relationship description",
  "constraints": ["ON DELETE CASCADE"]
}
```

{% endblock %}

{% block autoconfig %}
Based on the container and component analysis, generate a complete data model that defines all required entities, relationships, and storage structures. Format the output according to the defined structure, including ER diagrams and SQL schema.
{% endblock %}


{% block additional_system_instructions %}
When defining the data model:
1. Consider domain boundaries
2. Enable efficient querying
3. Support future scalability
4. Maintain data integrity
5. Document all decisions
{% endblock %}