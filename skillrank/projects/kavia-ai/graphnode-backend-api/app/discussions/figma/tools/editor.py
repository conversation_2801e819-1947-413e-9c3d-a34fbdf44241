
import os
import unicodedata

def apply_edit_block(content, files, base_path=None):
    """
    Apply the edit block to the content using native SEARCH/REPLACE processing
    
    :param content: The content of the edit block in SEARCH/REPLACE format
    :param files: List of files. If defined the edit will be only performed
                  if files specified in content are also present in this list
    :param base_path: Base path for file operations (optional)
    
    Returns 4 values:
    - A boolean indicating if the changes were applied successfully
    - A message indicating the result of the operation
    - A warning message if any
    - A dictionary containing file summaries with updated content
    """
    
    def decode_mixed_unicode(text):
        """
        Decodes only \\uXXXX and \\UXXXXXXXX escape sequences in a mixed string.
        Leaves real Unicode characters untouched.
        """
        import re
        def replace_match(match):
            escape = match.group(0)
            try:
                return escape.encode().decode('unicode_escape')
            except Exception:
                return escape  # Leave it unchanged if decoding fails

        # Replace all \uXXXX or \UXXXXXXXX sequences
        return re.sub(r'\\u[0-9a-fA-F]{4}|\\U[0-9a-fA-F]{8}', replace_match, text)

    def validate_file_access(file_path, allowed_files):
        """Validate that the file is in the allowed list if specified"""
        if allowed_files is None:
            return True
        
        # Check if the file or its basename is in the allowed list
        file_name = os.path.basename(file_path)
        return file_path in allowed_files or file_name in allowed_files

    try:
        content = decode_mixed_unicode(content)
        lines = content.strip().splitlines()
        index = 0
        file_summary = {}
        warning_message = ""

        while index < len(lines):
            file_path = lines[index].strip()
            index += 1

            # Validate file access if files list is provided
            if not validate_file_access(file_path, files):
                return False, f"File {file_path} is not in the allowed files list", "", {}

            # Handle relative paths with base_path
            if base_path and not os.path.isabs(file_path):
                file_path = os.path.join(base_path, file_path)

            try:
                # Read original file content or create empty content for new files
                if os.path.exists(file_path):
                    with open(file_path, "r", encoding="utf-8") as file:
                        file_data = file.read()
                else:
                    # For new files, initialize with empty content
                    file_data = ""
                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)

                # Normalize file content
                file_data = unicodedata.normalize("NFC", file_data)
                original_file_data = file_data

                # Process all SEARCH/REPLACE blocks for this file
                while index < len(lines) and lines[index].strip() == "<<<<<<< SEARCH":
                    search_index = index
                    
                    # Find the separator and end markers
                    try:
                        replace_index = next(i for i in range(search_index + 1, len(lines)) 
                                           if lines[i].strip() == "=======")
                        end_index = next(i for i in range(replace_index + 1, len(lines)) 
                                       if lines[i].strip() == ">>>>>>> REPLACE")
                    except StopIteration:
                        return False, f"Malformed SEARCH/REPLACE block in {file_path}: missing separator or end marker", "", {}

                    # Extract and decode search/replace blocks
                    search_content = "\n".join(lines[search_index + 1:replace_index])
                    replace_content = "\n".join(lines[replace_index + 1:end_index])

                    search_content = decode_mixed_unicode(search_content)
                    replace_content = decode_mixed_unicode(replace_content)

                    search_content = unicodedata.normalize("NFC", search_content)
                    replace_content = unicodedata.normalize("NFC", replace_content)

                    # For new files with empty SEARCH content, just set the content
                    if not file_data and not search_content:
                        file_data = replace_content
                    elif search_content in file_data:
                        # Apply changes if search content is found
                        file_data = file_data.replace(search_content, replace_content)
                    else:
                        # Provide detailed error information
                        return False, (f"SEARCH content not found in {file_path}. "
                                     f"Expected to find:\n{repr(search_content)}\n"
                                     f"But file contains:\n{repr(file_data[:500])}..."), "", {}

                    index = end_index + 1
                    # Skip empty lines after REPLACE block
                    while index < len(lines) and not lines[index].strip():
                        index += 1

                # Write updated file only if changes were made
                if file_data != original_file_data or not os.path.exists(file_path):
                    with open(file_path, "w", encoding="utf-8") as file:
                        file.write(file_data)

                # Add to file summary
                file_summary[os.path.basename(file_path)] = file_data

            except Exception as e:
                return False, f"Error processing {file_path}: {str(e)}", "", {}

        success_message = "Changes applied successfully"
        if len(file_summary) == 1:
            success_message += f" to {list(file_summary.keys())[0]}"
        elif len(file_summary) > 1:
            success_message += f" to {len(file_summary)} files"

        return True, success_message, warning_message, file_summary

    except Exception as e:
        return False, f"Critical error during edit processing: {str(e)}", "", {}


# Utility function for backward compatibility
def get_file_content(file_path):
    """Helper function to read file content safely"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return ""
    except Exception as e:
        raise Exception(f"Error reading file {file_path}: {str(e)}")


