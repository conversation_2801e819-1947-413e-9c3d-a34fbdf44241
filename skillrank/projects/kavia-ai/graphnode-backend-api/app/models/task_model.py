from pydantic import BaseModel, Field
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from typing import Optional, Dict, Any
from datetime import datetime
from app.core.task_framework import TaskStatus
from app.utils.datetime_utils import generate_timestamp
class TaskModel(BaseModel):
    id: str = Field(alias="_id")
    project_id: str
    status: str = Field(default=TaskStatus.SUBMITTED)
    progress: int = Field(default=0)
    created_at: str = Field(default_factory=lambda: generate_timestamp())
    user_id: str
    error: Optional[str] = None
    file_data: Optional[Dict[str, Any]] = None
    sizes: Optional[Dict[str, Any]] = None
    figma_link: Optional[Dict[str, Any]] = None

    @classmethod
    def get_connection(cls):
        return get_mongo_db(db_name=settings.MONGO_DB_NAME, collection_name="tasks")

    @classmethod
    async def create_task(cls, task_data: dict) -> "TaskModel":
        """Create a new task"""
        collection = cls.get_connection()
        await collection.insert_one(task_data)
        return cls(**task_data)

    @classmethod
    async def get_task(cls, task_id: str) -> Optional["TaskModel"]:
        """Get a task by ID"""
        collection = cls.get_connection()
        task_data = await collection.get_by_id(task_id, collection.db)
        return cls(**task_data) if task_data else None

    @classmethod
    async def update_task_status(cls, task_id: str, status: str, progress: int = None, **kwargs) -> bool:
        """Update task status and optional fields"""
        collection = cls.get_connection()
        update_data = {"status": status}
        if progress is not None:
            update_data["progress"] = progress
        update_data.update(kwargs)
        
        result = await collection.update_one(
            {"_id": task_id},
            {"$set": update_data}
        )
        return result.modified_count > 0

    @classmethod
    async def update_task_error(cls, task_id: str, error: str) -> bool:
        """Update task error status"""
        return await cls.update_task_status(
            task_id,
            status=TaskStatus.FAILED,
            error=error
        )

    @classmethod
    async def update_task_complete(cls, task_id: str, file_data: dict, sizes: dict) -> bool:
        """Update task as complete with results"""
        return await cls.update_task_status(
            task_id,
            status=TaskStatus.COMPLETE,
            progress=100,
            file_data=file_data,
            sizes=sizes
        ) 