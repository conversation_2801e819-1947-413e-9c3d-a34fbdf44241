{"UserStoryConfiguration": {"modified_node_type": "UserStory", "child_node_types": ["Task"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasTask"], "modified_relationship_types": true}, "EpicConfiguration": {"modified_node_type": "Epic", "child_node_types": ["UserStory"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasUserStory"], "modified_relationship_types": true}, "ProjectConfiguration": {"modified_node_type": "Project", "child_node_types": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "RequirementRootConfiguration": {"modified_node_type": "RequirementRoot", "child_node_types": ["Epic"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasEpic"], "modified_relationship_types": true}, "WorkItemConfiguration": {"modified_node_type": "WorkItem", "child_node_types": ["WorkItem"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasSubTask"], "modified_relationship_types": true}, "ArchitectureConfiguration": {"modified_node_type": "Architecture", "child_node_types": ["ArchitecturalElement", "Interfaces"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasComponent", "hasInterface"], "modified_relationship_types": true}, "ArchitectureDesignDetailsConfiguration": {"modified_node_type": "ArchitectureDesignDetails", "child_node_types": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "InterfaceDesignDetailsConfiguration": {"modified_node_type": "InterfaceDesignDetails", "child_node_types": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "DesignSpecificationConfiguration": {"modified_node_type": "DesignSpecification", "child_node_types": [], "modified_children": false, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": [], "modified_relationship_types": false}, "InterfaceDefinitionConfiguration": {"modified_node_type": "InterfaceDefinition", "child_node_types": ["Method", "Route", "DataContract", "Protocol"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["has<PERSON><PERSON><PERSON>", "hasRoute", "hasDataContract", "hasProtocol"], "modified_relationship_types": true}, "DesignBehaviorConfiguration": {"modified_node_type": "DesignBehavior", "child_node_types": ["Algorithm", "StateLogic"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasAlgorithm", "hasStateLogic"], "modified_relationship_types": true}, "DesignComponentInteractionsConfiguration": {"modified_node_type": "DesignComponentInteractions", "child_node_types": ["Sequence", "StateDiagram"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasSequence", "hasStateDiagram"], "modified_relationship_types": true}, "DesignTestCasesConfiguration": {"modified_node_type": "DesignTestCases", "child_node_types": ["UnitTest", "IntegrationTest", "PerformanceTest", "RobustnessTest"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasUnitTest", "hasIntegrationTest", "hasPerformanceTest", "hasRobustnessTest"], "modified_relationship_types": true}, "DesignClassDiagramsConfiguration": {"modified_node_type": "DesignClassDiagrams", "child_node_types": ["ClassDiagram"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasClassDiagram"], "modified_relationship_types": true}, "DesignAPIDocsConfiguration": {"modified_node_type": "DesignAPIDocs", "child_node_types": ["APIDoc"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasAPIDoc"], "modified_relationship_types": true}, "DesignSADDocsConfiguration": {"modified_node_type": "DesignSADDocs", "child_node_types": ["SADDoc"], "modified_children": true, "modified_siblings": false, "modified_other_nodes": false, "new_relationship_types": ["hasSADDoc"], "modified_relationship_types": true}}