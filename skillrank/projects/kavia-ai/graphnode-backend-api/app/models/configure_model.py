from enum import Enum
from typing import Union
from pydantic import BaseModel, Field
from app.utils.node_utils import get_node_type
from app.core.constants import NodeType
import json

class BaseOptions(BaseModel):
    configure: bool = Field(default=False, description="Configure the node")

class TaskOptions(BaseOptions):
    pass

class ProjectOnlyOptions(BaseOptions):
    pass

class RequirementOnlyOptions(BaseOptions):
    pass

class TestcaseOptions(BaseOptions):
    enabled: bool = Field(default=True, description="Enable/disable test case generation")
    component_tests: bool = Field(default=True, description="Enable/disable component test generation")
    user_story_tests: bool = Field(default=True, description="Enable/disable user story test generation")

class UserStoryOptions(BaseOptions):
    task: TaskOptions = Field(default_factory=TaskOptions, description="Task options")
    testcase: TestcaseOptions = Field(default_factory=TestcaseOptions, description="Testcase options")

class EpicOptions(BaseOptions):
    user_story: UserStoryOptions = Field(default_factory=UserStoryOptions, description="User Story options")

class RequirementOptions(BaseOptions):
    requirements: RequirementOnlyOptions = Field(default_factory=RequirementOnlyOptions, description="Requirement options")
    epic: EpicOptions = Field(default_factory=EpicOptions, description="Epic options")



class DesignDetails(BaseOptions):
    pass

class SubComponentOptions(BaseOptions):
    pass

class ArchitecturalRequirementOptions(BaseOptions):
    pass

class InterfaceOptions(BaseOptions):
    pass

class DesignOptions(BaseOptions):
    pass


class ComponentOptions(BaseOptions):
    design: DesignOptions = Field(default_factory=DesignOptions, description="Design options")
    testcase: TestcaseOptions = Field(default_factory=TestcaseOptions, description="Testcase options")
    interface: InterfaceOptions = Field(default_factory=InterfaceOptions, description="Interface options")

class ContainerOptions(BaseOptions):
    components: ComponentOptions = Field(default_factory=ComponentOptions, description="Component options")

class SystemContextOptions(BaseOptions):
    container: ContainerOptions = Field(default_factory=ContainerOptions, description="Container options")

class ArchitectureOptions(BaseOptions):
    architectural_requirements: ArchitecturalRequirementOptions = Field(default_factory=ArchitecturalRequirementOptions, description="Architectural Requirements options")
    system_context: SystemContextOptions = Field(default_factory=SystemContextOptions, description="System Context options")

class DocumentationOptions(BaseOptions):
    configure: bool = Field(default=False, description="Enable/disable all documentation generation")

class WorkItemOptions(BaseOptions):
    pass

class ProjectOptions(BaseOptions):
    project: ProjectOnlyOptions = Field(default_factory=ProjectOnlyOptions, description="Project Only options")
    # work_item: WorkItemOptions = Field(default_factory=WorkItemOptions, description="Work Item options")
    requirements: RequirementOptions = Field(default_factory=RequirementOptions, description="Requirement options")
    architecture: ArchitectureOptions = Field(default_factory=ArchitectureOptions, description="Architecture options")
    documentation: DocumentationOptions = Field(
        default_factory=DocumentationOptions,
        description="Documentation configuration options"
    )

def get_config_options(nodeType):
    nodeType = get_node_type(nodeType)
    if nodeType == NodeType.PROJECT.value:
        options = ProjectOptions()
    elif nodeType == NodeType.ARCHITECTUREROOT.value:
        options = ArchitectureOptions()
    elif nodeType == NodeType.ARCHITECTURE.value:
        options = ArchitectureOptions()
    elif nodeType == NodeType.REQUIREMENT.value or nodeType == NodeType.REQUIREMENTROOT.value:
        options = RequirementOptions()
    elif nodeType == NodeType.EPIC.value:
        options = EpicOptions()
    elif nodeType == NodeType.USERSTORY.value:
        options = UserStoryOptions()
    elif nodeType == NodeType.TASK.value:
        options = TaskOptions()
    elif nodeType == NodeType.WORKITEM.value:
        options = WorkItemOptions()
    elif nodeType == NodeType.ARCHITECTURALREQUIREMENT.value:
        options = ArchitecturalRequirementOptions()
    elif nodeType == NodeType.DESIGN.value:
        options = DesignOptions()
    elif nodeType == NodeType.INTERFACE.value:
        options = InterfaceOptions()
    elif nodeType == NodeType.DOCUMENTATION.value:
        options = DocumentationOptions()
    else:
        raise ValueError("Invalid node type")
    
    return json.loads(options.model_dump_json())