import subprocess
import os
import sys
import time
import json
import asyncio
import argparse
from datetime import datetime
from pathlib import Path
from app.utils.prompts import manifest_prompt
# Add your project path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.knowledge.redis_kg import getRedisKnowledge
from app.utils.kg_inspect.kg_tool import KgTools
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from code_generation_core_agent.agents.utilities import ModelNameSelector


# Import with retry logic and exception handling
def import_with_retry(import_func, max_retries=3, delay=2):
    """
    Import modules with retry logic to handle transient import failures

    Args:
        import_func: Function that performs the import
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds

    Returns:
        Result of import_func or None if all retries failed
    """
    for attempt in range(max_retries):
        try:
            return import_func()
        except Exception as e:
            print(f"Import attempt {attempt + 1}/{max_retries} failed: {str(e)}")
            if attempt < max_retries - 1:
                print(f"Retrying in {delay} seconds...")
                time.sleep(delay)
                delay *= 2  # Exponential backoff
            else:
                print(f"All {max_retries} import attempts failed. Last error: {str(e)}")
                raise e

# Import basic dependencies with better error handling
def import_basic_dependencies():
    """Import basic dependencies with error handling"""
    try:
        from app.connection.establish_db_connection import get_mongo_db
        from app.knowledge.redis_kg import add_redis_support_to_knowledge
        from app.utils.kg_build.knowledge import Knowledge, KnowledgeCodeBase
        from app.utils.kg_build.knowledge_helper import Knowledge_Helper
        from app.core.websocket.client import WebSocketClient
        from app.core.Settings import settings
        from app.knowledge.code_query import KnowledegeBuild
        import logging
        from app.celery_app import user_context

        return {
            'get_mongo_db': get_mongo_db,
            'add_redis_support_to_knowledge': add_redis_support_to_knowledge,
            'Knowledge': Knowledge,
            'KnowledgeCodeBase': KnowledgeCodeBase,
            'Knowledge_Helper': Knowledge_Helper,
            'WebSocketClient': WebSocketClient,
            'settings': settings,
            'KnowledegeBuild': KnowledegeBuild,
            'logging': logging,
            'user_context': user_context
        }
    except Exception as e:
        print(f"❌ Failed to import basic dependencies: {e}")
        raise

# Import dependencies when needed
_dependencies = None

def get_dependencies():
    """Get dependencies, importing them if needed"""
    global _dependencies
    if _dependencies is None:
        _dependencies = import_basic_dependencies()
        print("✅ Basic imports successful")
    return _dependencies

# Import Reporter with retry logic due to complex dependency chain
def import_reporter():
    from app.utils.kg_build.knowledge_reporter import Reporter
    return Reporter

def get_reporter():
    """Get Reporter class, importing with retry if needed"""
    try:
        return import_with_retry(import_reporter, max_retries=3, delay=2)
    except Exception as e:
        print(f"❌ Failed to import Reporter after retries: {e}")
        raise

# Enhanced GitHub API rate limit handling
def check_github_rate_limit(github_token=None):
    """
    Check GitHub API rate limit status

    Args:
        github_token: GitHub token for authentication

    Returns:
        tuple: (can_proceed: bool, remaining_requests: int, reset_time: int)
    """
    import requests

    try:
        headers = {}
        if github_token:
            headers["Authorization"] = f"token {github_token}"

        response = requests.get("https://api.github.com/rate_limit", headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            core_limit = data.get("resources", {}).get("core", {})
            remaining = core_limit.get("remaining", 0)
            reset_time = core_limit.get("reset", 0)

            # Allow operation if we have at least 10 requests remaining
            can_proceed = remaining >= 10

            print(f"GitHub API Rate Limit - Remaining: {remaining}, Reset: {reset_time}")
            return can_proceed, remaining, reset_time
        else:
            print(f"Failed to check rate limit: HTTP {response.status_code}")
            return True, 0, 0  # Assume we can proceed if check fails

    except Exception as e:
        print(f"Error checking GitHub rate limit: {e}")
        return True, 0, 0  # Assume we can proceed if check fails

def wait_for_rate_limit_reset(reset_time, max_wait=300):
    """
    Wait for GitHub rate limit to reset

    Args:
        reset_time: Unix timestamp when rate limit resets
        max_wait: Maximum time to wait in seconds (default: 5 minutes)
    """
    import time

    current_time = int(time.time())
    wait_time = min(reset_time - current_time + 60, max_wait)  # Add 60s buffer

    if wait_time > 0:
        print(f"Rate limit exceeded. Waiting {wait_time} seconds for reset...")
        time.sleep(wait_time)
    else:
        print("Rate limit should be reset now, proceeding...")

def execute_with_github_rate_limit_retry(func, *args, max_retries=3, **kwargs):
    """
    Execute a function with GitHub rate limit retry logic

    Args:
        func: Function to execute
        *args: Function arguments
        max_retries: Maximum number of retries
        **kwargs: Function keyword arguments

    Returns:
        Function result or raises exception after all retries
    """
    github_token = kwargs.get('github_token') or kwargs.get('token')

    for attempt in range(max_retries):
        try:
            # Check rate limit before making API calls
            can_proceed, remaining, reset_time = check_github_rate_limit(github_token)

            if not can_proceed:
                if attempt < max_retries - 1:
                    print(f"Rate limit exceeded on attempt {attempt + 1}/{max_retries}")
                    wait_for_rate_limit_reset(reset_time)
                    continue
                else:
                    raise Exception("GitHub API rate limit exceeded and max retries reached")

            # Execute the function
            result = func(*args, **kwargs)
            print(f"✅ GitHub API call successful on attempt {attempt + 1}")
            return result

        except Exception as e:
            error_msg = str(e).lower()

            # Check if it's a rate limit error
            if any(keyword in error_msg for keyword in ['rate limit', 'forbidden', '403', 'abuse']):
                print(f"Rate limit error on attempt {attempt + 1}/{max_retries}: {e}")

                if attempt < max_retries - 1:
                    # Wait with exponential backoff
                    wait_time = min(2 ** attempt * 60, 300)  # 1min, 2min, 4min (max 5min)
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"All {max_retries} attempts failed due to rate limiting")
                    raise e
            else:
                # Non-rate-limit error, re-raise immediately
                print(f"Non-rate-limit error on attempt {attempt + 1}: {e}")
                raise e

    raise Exception(f"Function failed after {max_retries} attempts")

# Wrapper for get_latest_commit_hash with rate limit handling
def get_latest_commit_hash_with_retry(git_url, branch_name, github_token=None, max_retries=3):
    """
    Get latest commit hash with GitHub rate limit retry logic

    Args:
        git_url: Git repository URL
        branch_name: Branch name
        github_token: GitHub token for authentication
        max_retries: Maximum number of retries

    Returns:
        Commit hash string or None if failed
    """
    try:
        from app.utils.kg_build.import_codebase import get_latest_commit_hash

        return execute_with_github_rate_limit_retry(
            get_latest_commit_hash,
            git_url,
            branch_name,
            github_token=github_token,
            max_retries=max_retries
        )
    except Exception as e:
        print(f"Failed to get latest commit hash after {max_retries} retries: {e}")
        return None

# Monkey patch the original function to use retry logic
def patch_github_functions():
    """
    Monkey patch GitHub-related functions to use retry logic
    """
    try:
        import app.utils.kg_build.import_codebase as import_codebase

        # Store original function
        original_get_latest_commit_hash = import_codebase.get_latest_commit_hash

        # Replace with retry version
        def get_latest_commit_hash_patched(git_url, branch_name, github_token=None):
            return get_latest_commit_hash_with_retry(git_url, branch_name, github_token, max_retries=3)

        import_codebase.get_latest_commit_hash = get_latest_commit_hash_patched
        print("✅ Successfully patched get_latest_commit_hash with retry logic")

    except Exception as e:
        print(f"⚠️ Warning: Could not patch GitHub functions: {e}")

# Apply patches when module is imported
patch_github_functions()

def set_tenant_context(tenant_id):
    """Set tenant ID in environment variables for get_tenant_id() function"""
    try:
        # Get existing input_arguments or create new ones
        existing_args = json.loads(os.environ.get("input_arguments", "{}"))
        
        # Update with tenant_id
        existing_args["tenant_id"] = tenant_id
        
        # Set back to environment
        os.environ["input_arguments"] = json.dumps(existing_args)
        
        print(f"✅ Tenant ID set in environment: {tenant_id}")
        print(f"📄 Updated input_arguments: {os.environ.get('input_arguments')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting tenant context: {e}")
        return False

def set_user_context(user_id):
    """Set user ID in the context variable"""
    from app.connection.tenant_middleware import user_id_context
    user_id_context.set(user_id)
    return True

async def run_knowledge_build_with_retry(data, max_retries=3):
    """
    Run the knowledge building process with GitHub rate limit retry logic

    Args:
        data: Knowledge build data
        max_retries: Maximum number of retries for rate limit issues
    """
    for attempt in range(max_retries):
        try:
            print(f"Knowledge build attempt {attempt + 1}/{max_retries}")

            # Check GitHub rate limit before starting
            try:
                deps = get_dependencies()
                settings = deps['settings']
                # Use token rotation for better rate limit handling
                from app.knowledge.code_query import get_best_github_token
                github_token = data.get('repo', {}).get('github_token') or get_best_github_token()
            except Exception as e:
                print(f"Warning: Could not get settings, using token from data: {e}")
                github_token = data.get('repo', {}).get('github_token')
            can_proceed, remaining, reset_time = check_github_rate_limit(github_token)

            if not can_proceed:
                if attempt < max_retries - 1:
                    print(f"Rate limit exceeded on attempt {attempt + 1}. Waiting for reset...")
                    wait_for_rate_limit_reset(reset_time)
                    continue
                else:
                    raise Exception("GitHub API rate limit exceeded and max retries reached")

            # Execute the actual knowledge build
            await run_knowledge_build(data)
            print(f"✅ Knowledge build completed successfully on attempt {attempt + 1}")
            return

        except Exception as e:
            error_msg = str(e).lower()

            # Check if it's a rate limit error
            if any(keyword in error_msg for keyword in ['rate limit', 'forbidden', '403', 'abuse']):
                print(f"Rate limit error on attempt {attempt + 1}/{max_retries}: {e}")

                if attempt < max_retries - 1:
                    # Wait with exponential backoff
                    wait_time = min(2 ** attempt * 60, 300)  # 1min, 2min, 4min (max 5min)
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"All {max_retries} attempts failed due to rate limiting")
                    raise e
            else:
                # Non-rate-limit error, re-raise immediately
                print(f"Non-rate-limit error on attempt {attempt + 1}: {e}")
                raise e

    raise Exception(f"Knowledge build failed after {max_retries} attempts")

async def run_knowledge_build(data):
    """Run the knowledge building process with provided data"""

    # Get dependencies
    deps = get_dependencies()
    get_mongo_db = deps['get_mongo_db']
    add_redis_support_to_knowledge = deps['add_redis_support_to_knowledge']
    Knowledge = deps['Knowledge']
    KnowledgeCodeBase = deps['KnowledgeCodeBase']
    Knowledge_Helper = deps['Knowledge_Helper']
    WebSocketClient = deps['WebSocketClient']
    settings = deps['settings']
    KnowledegeBuild = deps['KnowledegeBuild']
    logging = deps['logging']
    user_context = deps['user_context']

    session_id = data['session_id'] 
    user_id = data['user_id']
    project_id = int(data['project_id'])
    repo = data['repo']
    build_ids = data['build_ids']
    base_path = data['base_path']


    # Reconstruct codebases
    codebases = []
    for cb_data in data['codebases']:
        codebase = KnowledgeCodeBase(
            cb_data['base_path'],
            cb_data['name'],
            cb_data.get('service', 'github')
        )
        codebases.append(codebase)

    print(f"Starting knowledge build for session: {session_id}")
    print(f"Base path: {base_path}")
    print(f"Project ID: {project_id}")
    print(f"Build IDs: {build_ids}")
    print(f"run_knowledge_build -> user_id {user_id}")

    # Initialize session handler
    session_handler = get_mongo_db(
        db_name=data['settings']['mongo_db_name'],
        collection_name='kg_sessions',
        user_id=user_id
    )

    # Initialize WebSocket client and reporter
    ws_client = WebSocketClient(session_id, data['settings']['websocket_uri'])
    Reporter = get_reporter()
    reporter = Reporter(ws_client)
    from app.knowledge.code_query import KnowledegeBuild
    # Initialize KnowledgeBuild for status updates
    kg = KnowledegeBuild()

    try:
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Starting Build Process',
            'status': 'Success',
            'buildId': build_ids[0]
        })
        
        # Update session status to progress
        await session_handler.update_one(
            filter={"session_id": session_id},
            element={
                "session_status": "Progress",
                "updated_at": datetime.utcnow()
            },
            db=session_handler.db
        )
        
        # Initialize Knowledge_Helper
        knowledge_helper = Knowledge_Helper(
            session_id, 
            reporter, 
            os.getcwd(), 
            codebases, 
            user_id, 
            project_id, 
            service=repo.get('service', 'github')
        )

        # Get knowledge instance
        knowledge = Knowledge.getKnowledge(id=session_id)
        add_redis_support_to_knowledge(knowledge)
        
        # Get total files count
        total_files = knowledge.get_file_count(base_path)
        print(f"Total files to process: {total_files}")
        
        # Start knowledge processing
        knowledge.start()
        
        # Update status
        await kg.update_kg_status_by_id(
            1, project_id, build_ids[0], session_id, False, 
            repo.get('service', 'github'), user_id
        )
        
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Fetching Progress Data',
            'status': 'Success',
            'buildId': build_ids[0]
        })
        
        # Main processing loop
        while True:
            await kg.update_build_times(
                project_id, [knowledge.build_id], "last_updated", 
                False, repo.get('service', 'github'), user_id
            )
            
            if knowledge._state == 2:  # Completed
                # Save to Redis

                project_manifest = None
                knowledge.save_to_redis()
                
                try:
                    # Setup LLM interface
                    llm = LLMInterface(
                        str(get_path()),
                        'knowledge',
                        user_id,
                        int(project_id),
                        'manifest_generation'
                    )
                
                    knowledge_session = getRedisKnowledge(id=build_ids[0], verbose=True)
                    
                    if not knowledge_session:
                        print("Failed to get Redis knowledge session")
                        break
                    
                    general_registry = ToolRegistry()
                    general_registry.register_tool("KgTools", KgTools)
                    general_factory = DynamicToolFactory(general_registry)
                    
                    general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])

                    # Initialize KG tool with session
                    kg_tool = general_exec_agent(
                        base_path='/tmp',
                        logger=None,
                        user_id=session_id
                    )
                    model_selector = ModelNameSelector("gpt-4.1")
                    selected_model = model_selector.get_selected_model()

                

                    # Prepare messages for LLM
                    messages = [
                        {
                            "role": "system",
                            "content": manifest_prompt
                        },
                        {
                            "role": "user",
                            "content": f"Please analyze the codebase for project ID {project_id} and generate a comprehensive project manifest in YAML format."
                        }
                    ]

                    response = await llm.llm_interaction_wrapper(
                        messages=messages,
                        user_prompt=None,
                        system_prompt=None,
                        model=selected_model,
                        stream=False,
                        response_format={"type": "text"},
                        function_schemas=kg_tool.function_schemas,
                        function_executor=kg_tool.function_executor
                    )
                    from app.utils.yaml_extractor import process_manifest_response
                    # Use the utility function to extract and validate YAML
                    final_response = process_manifest_response(
                        manifest_content=response,
                        project_id=project_id,
                        validate_structure=True
                    )
                    if final_response['status'] == 'complete':
                        print("Manifest generated successfully")
                        print("Project Manifest: ", final_response['manifest']['content'])
                        yaml_content = final_response['manifest']['content']
                        project_manifest = yaml_content
                        await kg.update_kg_status_by_id(
                        2, project_id, build_ids[0], None, False, 
                        repo.get('service', 'github'), user_id, project_manifest=project_manifest
                    )
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    print(f"Error generating manifest: {str(e)}")
                
                
                # Update end time
                await kg.update_build_times(
                    project_id, build_ids, "end_time", 
                    False, repo.get('service', 'github'), user_id
                )
                
                # Handle different service types
                if repo.get('service') == 'github' and repo.get('repo_type') == "private":
                    for branch in repo.get('branches', []):
                        if branch['builds']['build_id'] in build_ids:
                            build_path = branch['builds']['path']
                            if build_path:
                                # Try direct push
                                can_push = await kg.try_to_commit(build_path, branch['name'])
                                if can_push:
                                    # Update commit hash
                                    os.chdir(build_path)
                                    _hash = os.popen('git rev-parse HEAD').read().strip()
                                    await kg.update_commit_hash(_hash, project_id, branch['builds']['build_id'])
                                    logging.info(f"Successfully pushed changes to {repo.get('repository_name', 'repo')}")
                                    await kg.update_kg_status(2, project_id, build_ids, user_id=user_id, project_manifest=project_manifest)
                                    reporter.send_message("code_ingestion", knowledge.get_kg_progress(total_files))
                                else:
                                    logging.info("Error while pushing the code")
                                    await kg.update_kg_status(-1, project_id, build_ids, user_id=user_id, project_manifest=project_manifest)
                else:
                    await kg.update_kg_status_by_id(
                        2, project_id, build_ids[0], None, False, 
                        repo.get('service', 'github'), user_id, project_manifest=project_manifest
                    )
                
                # Update session status to completed
                await session_handler.update_one(
                    filter={"session_id": session_id},
                    element={
                        "session_status": "Completed",
                        "updated_at": datetime.utcnow()
                    },
                    db=session_handler.db
                )

                reporter.send_message("code_ingestion", {'info': 'Repo_Data_Update'})
                print("Knowledge ingestion completed. Exiting...")
                break
            
            await asyncio.sleep(1)
        
        # Cleanup after completion
        Knowledge_Helper.cleanup(str(session_id))
        
        print("Knowledge graph generation completed successfully")
        return True
        
    except Exception as e:
        # Update session status to failed
        try:
            await session_handler.update_one(
                filter={"session_id": session_id},
                element={
                    "session_status": "Failed",
                    "error_message": str(e),
                    "updated_at": datetime.utcnow()
                },
                db=session_handler.db
            )
        except Exception as update_error:
            print(f"Failed to update session status: {update_error}")
        
        print(f"Error in knowledge build: {str(e)}")
        raise

def main():
    parser = argparse.ArgumentParser(description='Knowledge Graph Creation Background Job')
    parser.add_argument('--input_args', required=True, help='JSON string containing input arguments')
    parser.add_argument('--stage', required=True, help='Processing stage')
    
    args = parser.parse_args()
    
    print(f"Starting knowledge creation job")
    print(f"Stage: {args.stage}")
    print(f"Input args: {args.input_args}")
    
    try:
        # Parse input arguments
        input_data = json.loads(args.input_args)
        
        base_path = input_data['base_path']
        json_data_file = input_data['json_data_file']
        session_id = input_data['session_id']
        project_id = input_data['project_id']
        user_id = input_data['user_id']
        tenant_id = input_data['tenant_id']
        set_tenant_context(tenant_id)
        set_user_context(user_id)

        # Set user context
        try:
            deps = get_dependencies()
            user_context = deps['user_context']
            user_context.set(user_id)
        except Exception as e:
            print(f"Warning: Could not set user context: {e}")
        
        print(f"Parsed parameters:")
        print(f"  Base path: {base_path}")
        print(f"  JSON data file: {json_data_file}")
        print(f"  Session ID: {session_id}")
        print(f"  Project ID: {project_id}")
        print(f"  User ID: {user_id}")
        print(f"  Tenant ID: {tenant_id}")
        
        # Load JSON data
        with open(json_data_file, 'r') as f:
            data = json.load(f)
        
        print(f"Loaded data from {json_data_file}")
        print(f"Data keys: {list(data.keys())}")
        
        # Run the knowledge build process based on stage
        if args.stage == "knowledge_creation":
            try:
                # Use retry wrapper to handle GitHub rate limit issues
                asyncio.run(run_knowledge_build_with_retry(data, max_retries=3))
                print("✅ Knowledge build completed successfully")
                sys.exit(0)
            except Exception as e:
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['rate limit', 'forbidden', '403', 'abuse']):
                    print(f"❌ Knowledge build failed due to GitHub rate limiting: {e}")
                    print("💡 Consider waiting for rate limit reset or using a different GitHub token")
                else:
                    print(f"❌ Knowledge build failed: {e}")
                sys.exit(1)
        else:
            print(f"Unknown stage: {args.stage}")
            sys.exit(1)
            
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input arguments: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: JSON data file not found: {json_data_file}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
    finally:
        # Clean up the JSON data file
        try:
            if 'json_data_file' in locals():
                if os.path.exists(json_data_file):
                    os.unlink(json_data_file)
                    print(f"Cleaned up JSON data file: {json_data_file}")
        except Exception as cleanup_error:
            print(f"Warning: Could not clean up JSON data file: {cleanup_error}")

if __name__ == "__main__":
    main()
