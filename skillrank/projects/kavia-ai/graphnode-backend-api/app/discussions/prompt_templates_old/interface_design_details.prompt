{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function. Now, for your specific task:

Your task is to first create technical and functional requirements for an interface defintion between two components and then to create the 
for that interface. The current node displayed below represents the interface between a source component and a target component. 
The interface definition should include the full desciption of the interface and all the details about the interface.
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node that represents 
the interface: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
This interface is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The interface is between the source component: 
{{ details_for_discussion.get('source_node') | tojson(indent=2) }}
and the target component:
{{ details_for_discussion.get('target_node') | tojson(indent=2) }}
The interface details are: {{ details_for_discussion.get('interface') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
Here are some functional requirements for the whole system to take into account while you are designing the architecture for this module: 
{{ details_for_discussion.get('functional_requirements')  }}
Here are some architectural requirements for the whole system to take into account while your are designing the architecture for this module: 
{{ details_for_discussion.get('architectural_requirements') }}

In addition here is a list of other architectural components already available in the system outside of the component that you are designing. 
This information could be used to have a good understanding of the whole system and to avoid duplication: 
Top levels of architecture: {{ details_for_discussion.get('top_levels') | tojson(indent=2) }}
Relationship between other architectural components: {{ details_for_discussion.get('relationships') | tojson(indent=2) }}
{% endblock %}

{% block output_format %}
Use the function capture_discussion_output to save the output.
{% endblock %}

{%block system_prompt %}
You are an expert software architect who has a deep understanding of system design and architecture and has a proven track record of designing 
scalable and maintainable architectures. You are engaging in a conversation with a developer or a product manager regarding architectural design 
for a new feature or a new product. Make sure that your architectural design addresses all the key functional and non-functional requirements 
of the system. Output any comments to  the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}