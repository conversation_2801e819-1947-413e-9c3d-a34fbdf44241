import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional

from .figma_parser import parse_figma_file
from .models import FigmaDocument, Node, Component, Paint, PaintType, Color


class FigmaUtils:
    """Utility functions for working with parsed Figma documents."""

    @staticmethod
    def find_node_by_id(document: FigmaDocument, node_id: str) -> Optional[Node]:
        """Find a node by its ID in the document."""

        def search_node(node: Node) -> Optional[Node]:
            if node.id == node_id:
                return node
            for child in node.children:
                result = search_node(child)
                if result:
                    return result
            return None

        for component in document.components:
            result = search_node(component.hierarchy)
            if result:
                return result
        return None

    @staticmethod
    def find_nodes_by_name(document: FigmaDocument, name: str) -> List[Node]:
        """Find all nodes with a given name in the document."""
        nodes: List[Node] = []

        def collect_nodes(node: Node) -> None:
            if node.name == name:
                nodes.append(node)
            for child in node.children:
                collect_nodes(child)

        for component in document.components:
            collect_nodes(component.hierarchy)
        return nodes

    @staticmethod
    def extract_colors(document: FigmaDocument) -> Dict[str, Color]:
        """Extract all unique colors used in the document."""
        colors: Dict[str, Color] = {}

        def collect_colors(node: Node) -> None:
            for paint in node.fills + node.strokes:
                if paint.type == PaintType.SOLID and paint.color:
                    color_hex = paint.color.to_hex()
                    if color_hex not in colors:
                        colors[color_hex] = paint.color

            for child in node.children:
                collect_colors(child)

        for component in document.components:
            collect_colors(component.hierarchy)
        return colors

    @staticmethod
    def extract_text_content(document: FigmaDocument) -> List[Node]:
        """Extract all text nodes from the document."""
        text_nodes: List[Node] = []

        def collect_text(node: Node) -> None:
            if node.characters and node.text_style:
                text_nodes.append(node)
            for child in node.children:
                collect_text(child)

        for component in document.components:
            collect_text(component.hierarchy)
        return text_nodes

    @staticmethod
    def get_component_tree(document: FigmaDocument) -> Dict[str, Any]:
        """Get a hierarchical tree representation of components."""

        def build_tree(node: Node) -> Dict[str, Any]:
            return {
                'id': node.id,
                'name': node.name,
                'type': node.type,
                'children': [build_tree(child) for child in node.children]
            }

        return {
            component.id: build_tree(component.hierarchy)
            for component in document.components
        }

    @staticmethod
    def get_interactive_elements(document: FigmaDocument) -> List[Dict[str, Any]]:
        """Get all interactive elements in the document."""
        interactive_elements: List[Dict[str, Any]] = []

        def collect_interactive(node: Node) -> None:
            if node.interactions:
                interactive_elements.append({
                    'id': node.id,
                    'name': node.name,
                    'type': node.type,
                    'interactions': node.interactions
                })
            for child in node.children:
                collect_interactive(child)

        for component in document.components:
            collect_interactive(component.hierarchy)
        return interactive_elements

    @staticmethod
    def validate_document(document: FigmaDocument) -> List[str]:
        """Validate the Figma document structure and return any issues."""
        issues: List[str] = []

        if not document.components:
            issues.append("Document contains no components")

        def validate_node(node: Node, path: str) -> None:
            if not node.id:
                issues.append(f"Node at {path} has no ID")
            if not node.name:
                issues.append(f"Node at {path} has no name")
            if node.characters and not node.text_style:
                issues.append(f"Text node {node.name} at {path} has no text style")
            for i, child in enumerate(node.children):
                validate_node(child, f"{path}/{node.name}[{i}]")

        for component in document.components:
            validate_node(component.hierarchy, component.id)

        return issues


def _text_style_to_dict(text_style) -> Dict[str, Any]:
    """Convert TextStyle object to a dictionary."""
    if not text_style:
        return {}

    return {
        'font_family': text_style.font_family,
        'font_size': text_style.font_size,
        'font_weight': text_style.font_weight,
        'letter_spacing': text_style.letter_spacing,
        'line_height_px': text_style.line_height_px,
        'text_align_horizontal': text_style.text_align_horizontal,
        'text_align_vertical': text_style.text_align_vertical,
        'text_decoration': text_style.text_decoration,
        'text_transform': text_style.text_transform,
        'paragraph_spacing': text_style.paragraph_spacing,
        'paragraph_indent': text_style.paragraph_indent
    }


def _prepare_template_data(document: FigmaDocument) -> Dict[str, Any]:
    """Prepare data structure for the template."""
    utils = FigmaUtils()
    colors = utils.extract_colors(document)

    return {
        'metadata': document.metadata,
        'statistics': {
            'Components': len(document.components),
            'Colors': len(colors),
            'Text Elements': len(utils.extract_text_content(document)),
            'Interactive Elements': len(utils.get_interactive_elements(document))
        },
        'colors': [
            {
                'hex': hex_color,
                'rgb': {
                    'r': int(color.r * 255),
                    'g': int(color.g * 255),
                    'b': int(color.b * 255)
                }
            }
            for hex_color, color in colors.items()
        ],
        'text_content': [
            {
                'name': node.name,
                'content': node.characters,
                'style': _text_style_to_dict(node.text_style)
            }
            for node in utils.extract_text_content(document)
        ],
        'interactive_elements': [
            {
                'name': element['name'],
                'type': element['type'],
                'interactions': [
                    {
                        'trigger': interaction.trigger,
                        'actions': [
                            {
                                'type': action.type,
                                'destination': action.destination_id
                            }
                            for action in interaction.actions
                        ]
                    }
                    for interaction in element['interactions']
                ]
            }
            for element in utils.get_interactive_elements(document)
        ],
        'component_tree': utils.get_component_tree(document),
        'validation_issues': utils.validate_document(document)
    }


def generate_report_data(json_data: str) -> Dict[str, Any]:
    """Generate report data from Figma JSON data."""
    # Parse the document
    document = parse_figma_file(json_data)

    # Return prepared data
    return _prepare_template_data(document)


def load_json_file(file_path: str) -> str:
    """
    Load JSON from file with proper path resolution.
    Args:
        file_path: Path to the JSON file (can be relative or absolute)
    Returns:
        str: JSON content as string
    Raises:
        FileNotFoundError: If the file cannot be found
        json.JSONDecodeError: If the file contains invalid JSON
    """
    # Convert to Path object for better path handling
    path = Path(file_path)

    # If path is relative and file doesn't exist, try from script location
    if not path.is_absolute() and not path.exists():
        script_dir = Path(__file__).parent.absolute()
        path = script_dir / path

    # If still doesn't exist, try from current working directory
    if not path.exists():
        path = Path.cwd() / file_path

    if not path.exists():
        raise FileNotFoundError(f"Could not find file: {file_path}")

    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


# Add to_hex method to Color class if it doesn't exist in the models.py
def color_to_hex(color: Color) -> str:
    """Convert a Color object to hex string."""
    r = int(color.r * 255)
    g = int(color.g * 255)
    b = int(color.b * 255)
    return f"#{r:02x}{g:02x}{b:02x}"

# Add the to_hex method to Color class if it doesn't exist
if not hasattr(Color, 'to_hex'):
    Color.to_hex = color_to_hex
