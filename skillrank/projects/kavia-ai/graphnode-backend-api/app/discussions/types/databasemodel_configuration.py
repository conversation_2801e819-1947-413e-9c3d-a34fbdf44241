from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.telemetry.logger_config import get_logger
import json

class DatabaseModelDiscussion(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="DataModel")
        self.template_name = "datamodel_configuration.prompt"
        self.function_schema_type = "DatabaseModelConfiguration"
        self.update_logger = get_logger(__name__)

    async def retrieve_info(self):
        """Gather comprehensive information about the system's data structures across all containers"""
        await super().retrieve_info()
        
        try:
            # Get system context to traverse entire architecture
            current_container = await self.db.get_parent_node(self.node_id)
            system_context = await self.get_system_context(current_container['id'])
            
            if not system_context:
                self.update_logger.error("Could not find system context")
                return self.retrieved_info

            # Collect all containers in the system
            containers = await self.db.get_child_nodes(system_context['id'], "Container")
            self.retrieved_info['containers'] = containers

            # Initialize collections for system-wide data structures
            data_contracts = []
            interfaces = []
            component_relationships = []
            container_relationships = []
            database_components = []
            
            # Get relationships between containers
            for container in containers:
                # Get relationships
                relationships = await self.db.get_relationships_involving_node(container['id'], "INTERFACES_WITH")
                container_relationships.extend(relationships)
                
                # Get components within each container
                components = await self.db.get_child_nodes(container['id'], "Component")
                
                for component in components:
                    # Check if this is a database component
                    tech_stack = component['properties'].get('Technology', '').lower()
                    if any(tech in tech_stack for tech in {'postgresql', 'mysql', 'mongodb', 'redis', 'cassandra', 'elasticsearch'}):
                        database_components.append(component)
                    
                    # Get interfaces and data contracts
                    interface_nodes = await self.db.get_child_nodes(component['id'], "Interface")
                    for interface in interface_nodes:
                        contracts = await self.db.get_child_nodes(interface['id'], "DataContract")
                        for contract in contracts:
                            data_contracts.append({
                                'contract': contract,
                                'interface': interface,
                                'component': component,
                                'container': container
                            })
                        
                        interfaces.append({
                            'interface': interface,
                            'component': component,
                            'container': container
                        })
                    
                    # Get component relationships
                    comp_rels = await self.db.get_relationships_involving_node(component['id'], "INTERFACES_WITH")
                    for rel in comp_rels:
                        component_relationships.append({
                            'relationship': rel,
                            'container': container
                        })

            # Store all collected information
            self.retrieved_info.update({
                'system_context': system_context,
                'data_contracts': data_contracts,
                'interfaces': interfaces,
                'component_relationships': component_relationships,
                'container_relationships': container_relationships,
                'database_components': database_components
            })

            self.update_logger.info(f"""
            Collected system-wide data structures:
            - Containers: {len(containers)}
            - Data Contracts: {len(data_contracts)}
            - Interfaces: {len(interfaces)}
            - Component Relationships: {len(component_relationships)}
            - Container Relationships: {len(container_relationships)}
            - Database Components: {len(database_components)}
            """)

        except Exception as e:
            self.update_logger.error(f"Error retrieving system data structures: {str(e)}")
            raise

        return self.retrieved_info

    async def get_system_context(self, start_node_id):
        """Traverse up the hierarchy to find the SystemContext node"""
        current_node = await self.db.get_node_by_id(start_node_id)
        while current_node:
            if "SystemContext" in current_node['labels']:
                return current_node
            current_node = await self.db.get_parent_node(current_node['id'])
        return None

    

# Register with factory
DiscussionFactory.register('Configuration', DatabaseModelDiscussion, "DatabaseModel")