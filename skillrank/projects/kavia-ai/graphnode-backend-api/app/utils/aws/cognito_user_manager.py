from typing import List, Dict, Optional, Union
from botocore.exceptions import ClientError
import boto3
from app.core.Settings import settings
from app.classes.SESHandler import ses_handler



class CognitoUserManager:
    """
    A plug-and-play AWS Cognito User Management class
    that handles user operations with flexible configurations
    """
    def __init__(self,
                 user_pool_id: Optional[str] = None,
                 client_id: Optional[str] = None):
        """
        Initialize Cognito Manager with minimal required credentials
        
        Args:
            aws_access_key_id: AWS access key
            aws_secret_access_key: AWS secret key
            region_name: AWS region
            user_pool_id: Optional - Cognito User Pool ID
            client_id: Optional - Cognito Client ID
        """
        self.cognito = boto3.client(
            'cognito-idp',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self._user_pool_id = user_pool_id
        self._client_id = client_id

    @property
    def user_pool_id(self) -> str:
        """Get user pool ID, fetch from AWS if not provided"""
        if not self._user_pool_id:
            response = self.cognito.list_user_pools(MaxResults=60)
            if response['UserPools']:
                self._user_pool_id = response['UserPools'][0]['Id']
            else:
                raise ValueError("No user pools found in the account")
        return self._user_pool_id

    @property
    def client_id(self) -> str:
        """Get client ID, fetch from AWS if not provided"""
        if not self._client_id:
            response = self.cognito.list_user_pool_clients(
                UserPoolId=self.user_pool_id,
                MaxResults=60
            )
            if response['UserPoolClients']:
                self._client_id = response['UserPoolClients'][0]['ClientId']
            else:
                raise ValueError("No clients found in the user pool")
        return self._client_id

    def get_user_by_identifier(self, identifier: str) -> Dict:
        """
        Get user by either username or email
        
        Args:
            identifier: Either username or email
            
        Returns:
            Dict containing user information
        """
        try:
            # Try as username first
            return self.get_user(identifier)
        except Exception as e:
            if 'UserNotFoundException' in str(e):
                # If not found, try to find by email
                users = self.list_users(filter_key='email', filter_value=identifier)
                if users:
                    return users[0]
                raise ValueError(f"No user found with identifier: {identifier}")
            raise e

    def create_user(self, 
                username: Optional[str] = None,
                email: Optional[str] = None,
                temporary_password: Optional[str] = None,
                custom_attributes: Optional[Dict] = None,
                send_welcome_email: bool = True,
                organization_name: Optional[str] = "Kavia",
                set_password_url: Optional[str] = "http://kavia.local:3000/users/set_password?tenant_id=T0000&email={username}",
                login_url: Optional[str] = "http://kavia.local:3000/login?tenant_id=T0000&email={username}",
                user_fullname: str = None,
                use_ses: bool = True,
                ses_source_arn: Optional[str] = settings.SES_SOURCE_ARN,
                ses_from_email: Optional[str] = settings.SES_FROM_EMAIL,
                ses_reply_to: Optional[str] = settings.SES_REPLY_TO,
               ) -> Dict:
        """
        Create a new user with flexible parameters
        """
        try:
            if not user_fullname:
                if 'Name' in custom_attributes:
                    user_fullname = str(custom_attributes['Name']).split(' ')[0]
                elif email:
                    user_fullname = email.split('@')[0]
                else:
                    user_fullname = username

            if not username and not email:
                raise ValueError("Either username or email must be provided")
                
            final_username = username or email
            
            if not temporary_password:
                import secrets
                chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + \
                        'abcdefghijklmnopqrstuvwxyz' + \
                        '0123456789' + \
                        '!@#$%^&*'
                temporary_password = ''.join(secrets.choice(chars) for _ in range(12))
            
            attributes = []
            if email:
                attributes.extend([
                    {'Name': 'email', 'Value': email},
                    {'Name': 'email_verified', 'Value': 'true'}
                ])
                    
            if custom_attributes:
                attributes.extend([
                    {
                        'Name': f"custom:{key}" if not key.startswith('custom:') else key, 
                        'Value': str(value)
                    }
                    for key, value in custom_attributes.items()
                ])

            params = {
                'UserPoolId': self.user_pool_id,
                'Username': final_username,
                'UserAttributes': attributes,
                'TemporaryPassword': temporary_password,
                'DesiredDeliveryMediums': ['EMAIL'],
                'ForceAliasCreation': True,
                'MessageAction': 'SUPPRESS'  # Always suppress Cognito's email since we'll send our own
            }

            response = self.cognito.admin_create_user(**params)

            # Set permanent password so user doesn't need to reset upon first login
            self.cognito.admin_set_user_password(
                UserPoolId=self.user_pool_id,
                Username=final_username,
                Password=temporary_password,
                Permanent=True
            )
            
            # Send welcome email using SESHandler if requested
            if send_welcome_email and email:
                try:
                    self._send_welcome_email(
                        email=email,
                        username=final_username,
                        temporary_password=temporary_password,
                        user_fullname=user_fullname,
                        organization_name=organization_name,
                        set_password_url=set_password_url.format(username=final_username),
                        login_url=login_url.format(username=final_username),
                        ses_from_email=ses_from_email,
                        ses_reply_to=ses_reply_to
                    )
                except Exception as email_error:
                    print(f"Warning: User created but email failed to send: {str(email_error)}")
            
            return response['User']

        except ClientError as e:
            error = e.response['Error']
            if error['Code'] == 'UsernameExistsException':
                raise ValueError(f"User exists: {final_username}")
            elif error['Code'] == 'InvalidParameterException':
                raise ValueError(f"Invalid parameters: {error['Message']}")
            elif error['Code'] == 'TooManyRequestsException':
                raise Exception("Rate limit exceeded")
            raise Exception(f"Failed to create user: {error['Message']}")

    def _send_welcome_email(self,
                           email: str,
                           username: str,
                           temporary_password: str,
                           user_fullname: str,
                           organization_name: str,
                           set_password_url: str,
                           login_url: str,
                           ses_from_email: Optional[str] = None,
                           ses_reply_to: Optional[str] = None) -> None:
        """
        Send welcome email using SESHandler
        """
        subject = "🎉 Welcome to KAVIA AI "
        
        # HTML email content
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to KAVIA AI</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f0f0f0;
        }}
        
        .email-container {{
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }}
        
        .header-section {{
            position: relative;
            text-align: center;
            padding: 30px 20px;
            margin: -40px -40px 30px -40px;
            border-radius: 8px 8px 0 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%);
        }}
        
        .logo {{
            width: 54px;
            height: 62px;
            display: block;
            margin: 0 auto 20px;
        }}
        
        .header-title {{
            font-size: 24px;
            font-weight: 600;
            color: #333333;
            text-align: center;
            margin-bottom: 0;
        }}
        
        .greeting {{
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            margin-bottom: 10px;
        }}
        
        .intro-text {{
            font-size: 16px;
            color: #333333;
            margin-bottom: 40px;
            line-height: 1.5;
        }}
        
        .credentials-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }}
        
        .credentials-title {{
            font-size: 16px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 15px;
        }}
        
        .credential-item {{
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
        }}
        
        .credential-label {{
            font-weight: bold;
            color: #333333;
            width: 90px;
            flex-shrink: 0;
        }}
        
        .credential-value {{
            color: #333333;
        }}
        
        .url-section {{
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #FF6B35;
            border-radius: 4px;
        }}
        
        .url-label {{
            font-weight: 600;
            color: #333333;
            margin-bottom: 5px;
            font-size: 14px;
        }}
        
        .url-link {{
            color: #4A90E2;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            background-color: #ffffff;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }}
        
        .section-title {{
            font-size: 18px;
            font-weight: 600;
            color: #333333;
            margin: 30px 0 15px 0;
        }}
        
        .about-text {{
            font-size: 16px;
            color: #333333;
            margin-bottom: 30px;
            line-height: 1.5;
        }}
        
        .features-title {{
            font-size: 18px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 15px;
        }}
        
        .features-list {{
            list-style: none;
            margin-bottom: 30px;
        }}
        
        .features-list li {{
            font-size: 16px;
            color: #333333;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }}
        
        .features-list li::before {{
            content: counter(item) ".";
            counter-increment: item;
            position: absolute;
            left: 0;
            font-weight: 600;
        }}
        
        .features-list {{
            counter-reset: item;
        }}
        
        .doc-intro-text {{
            font-size: 16px;
            color: #333333;
            margin-bottom: 10px;
            line-height: 1.5;
        }}
        
        .documentation-link {{
            color: #4A90E2;
            text-decoration: underline;
            font-size: 16px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 30px;
        }}
        
        .documentation-link:hover {{
            color: #357ABD;
        }}
        
        .footer-text {{
            font-size: 16px;
            color: #333333;
            margin-bottom: 5px;
        }}
        
        .footer-links a {{
            color: #4A90E2;
            text-decoration: underline;
        }}
        
        .footer-links a:hover {{
            color: #357ABD;
        }}
        
        .warning-note {{
            background-color: #FFF3CD;
            border-left: 4px solid #FFA500;
            padding: 15px;
            margin: 25px 0;
            border-radius: 4px;
            color: #856404;
            font-size: 14px;
        }}
        
        .warning-note strong {{
            color: #B8860B;
        }}
        
        @media (max-width: 600px) {{
            .email-container {{
                margin: 10px;
                padding: 30px 20px;
            }}
            
            .url-section {{
                margin: 15px 0;
            }}
        }}
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header-section">
            <svg class="logo" width="54" height="62" viewBox="0 0 54 62" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Layer_9">
                    <path id="Vector" d="M50.9138 39.6648C45.0723 37.3149 39.3834 34.6209 33.8149 31.6991C33.387 31.4712 32.9613 31.2411 32.5356 31.0068C38.4804 27.756 44.6315 24.8879 50.9138 22.3358C53.7024 21.2909 54.7021 17.623 52.7951 15.3182C51.2385 13.3725 48.4005 13.0543 46.4547 14.6109C41.499 18.4959 36.3218 22.0757 31.007 25.4361C30.5963 25.6941 30.1835 25.9457 29.7686 26.1972C29.9277 19.4226 30.5189 12.663 31.4477 5.94636C31.9379 3.00946 29.259 0.30906 26.3092 0.80786C23.9313 1.16906 22.2694 3.32551 22.4951 5.69051C24.1786 13.1596 25.1461 20.8631 25.3009 28.7385C25.3073 29.061 25.3138 29.3835 25.3181 29.706C25.3224 30.0737 25.3245 30.4392 25.3267 30.8068C25.3267 30.8713 25.3267 30.9358 25.3267 31.0003C25.3267 31.0605 25.3267 31.1186 25.3267 31.1788C25.3267 31.1831 25.3267 31.1874 25.3267 31.1917C25.3267 31.5851 25.3224 31.9786 25.3181 32.372C25.3181 32.4279 25.3181 32.486 25.3159 32.5419C25.3159 32.6859 25.3116 32.8278 25.3095 32.9719C25.1719 40.9484 24.2022 48.7486 22.4973 56.3101C22.2737 58.6751 23.9335 60.8316 26.3114 61.1927C29.2612 61.6915 31.9401 58.9933 31.4499 56.0543C30.5211 49.342 29.9298 42.5867 29.7707 35.8163C35.5542 39.338 41.1098 43.2295 46.4569 47.3898C48.7552 49.2839 52.4317 48.3121 53.4745 45.5085C54.3818 43.1887 53.238 40.57 50.916 39.6627L50.9138 39.6648Z" fill="#F26A1B"/>
                    <path id="Vector_2" d="M12.4967 35.5345C11.7206 35.8978 10.9423 36.2547 10.1597 36.6052C7.80756 37.6608 5.45331 38.71 3.05606 39.6625C0.278257 40.7117 -0.717194 44.3645 1.17911 46.6672C2.72926 48.6172 5.56726 48.9397 7.51731 47.3874C8.19241 46.8499 8.87611 46.3296 9.56411 45.8093C10.2543 45.2955 10.9401 44.7795 11.6346 44.2764C13.0192 43.2637 14.4081 42.2554 15.8142 41.275C19.0736 38.8756 16.2377 33.9048 12.4967 35.5302V35.5345Z" fill="#F26A1B"/>
                    <path id="Vector_3" d="M15.8122 20.7219C15.1092 20.2317 14.4104 19.735 13.716 19.2341C11.6262 17.7248 9.5407 16.2112 7.51539 14.6116C5.21919 12.7282 1.55559 13.6957 0.510694 16.4885C-0.403056 18.8062 0.736444 21.4249 3.05414 22.3387C3.85609 22.6547 4.65159 22.988 5.44279 23.3212C6.23184 23.6609 7.02305 23.9985 7.80565 24.3489C9.37515 25.0412 10.9425 25.74 12.4948 26.4688C16.2014 28.0921 19.091 23.1514 15.8122 20.724V20.7219Z" fill="#F26A1B"/>
                </g>
            </svg>
            <h1 class="header-title">🎉 Welcome to KAVIA AI</h1>
        </div>
        
        <div class="greeting">Hello {user_fullname},</div>
        
        <div class="intro-text">
            We're excited to have you join Kavia AI, your platform for effortless, intelligent software development.
        </div>
        
        <div class="url-section">
            <div class="url-label">Set Password URL:</div>
            <div class="url-link">{set_password_url}</div>
        </div>
        
        <div class="url-section">
            <div class="url-label">Login URL (after setting password):</div>
            <div class="url-link">{login_url}</div>
        </div>
        
        <div class="section-title">About Kavia AI</div>
        <div class="about-text">
            Kavia AI helps you build, manage, and deploy software projects faster with AI-powered tools and real-time collaboration.
        </div>
        
        <div class="features-title">What can you do with KAVIA AI?</div>
        <ol class="features-list">
            <li>Create Web and Mobile Applications</li>
            <li>Integrate and Query your code base</li>
            <li>Modify your Code with Natural Language prompts</li>
            <li>Deploy your applications and share!</li>
        </ol>
        
        <div class="features-title">Start with KAVIA AI - Documentation and Guide</div>
        <div class="doc-intro-text">To get started and explore all the features, please click the link below to view our comprehensive documentation:</div>
        <a href="https://kavia-website-artifacts.s3.us-east-1.amazonaws.com/Docs/welcome-guide/Start+with+KAVIA+AI.pdf" class="documentation-link">
            https://kavia-website-artifacts.s3.us-east-1.amazonaws.com/Docs/welcome-guide/Start+with+KAVIA+AI.pdf
        </a>
        
        <div class="footer-text">
            Share your feedback at <a href="mailto:<EMAIL>"><EMAIL></a> or <a href="https://forms.gle/jyjby6wAsiYsnAs17">Feedback form</a>
        </div>
        <div class="footer-text">
            Share the issues faced in <a href="https://forms.gle/T14KragEV8yB1d4L8">Issue Tracker</a>
        </div>
    </div>
</body>
</html>"""

        # Plain text content
        text_content = f"""
Hello {user_fullname},

Welcome to {organization_name}! We're excited to have you on board.

Your Login Credentials:
Username: {username}
Password: {temporary_password}

Set Password URL: {set_password_url}

Login URL (after setting password): {login_url}

Note: Please keep this information secure and delete after use.

Best regards,
{organization_name} Team
"""

        # Send email using SESHandler
        reply_to_list = [ses_reply_to] if ses_reply_to else None
        
        ses_handler.send_email_api(
            to_addresses=[email],
            subject=subject,
            body_text=text_content,
            body_html=html_content,
            sender=ses_from_email,
            reply_to=reply_to_list
        )

    def update_user_attributes(self, 
                             identifier: str,
                             attributes: Dict,
                             is_custom: bool = True) -> Dict:
        """
        Update user attributes with simplified dictionary input
        
        Args:
            identifier: Username or email
            attributes: Dictionary of attributes to update
            is_custom: Whether these are custom attributes
            
        Returns:
            Updated user information
        """
        user = self.get_user_by_identifier(identifier)
        username = user['Username']
        
        formatted_attributes = []
        for key, value in attributes.items():
            if key =="picture" and value=="":
                formatted_attributes.append({
                    'Name': 'picture',
                    'Value': ''
                })
                continue
            if not value:
                continue
            if is_custom and not key.startswith('custom:'):
                key = f'custom:{key}'
            formatted_attributes.append({
                'Name': key,
                'Value': str(value)
            })
            
        try:
            self.cognito.admin_update_user_attributes(
                UserPoolId=self.user_pool_id,
                Username=username,
                UserAttributes=formatted_attributes
            )
            return self.get_user(username)
        except ClientError as e:
            raise Exception(f"Failed to update attributes: {str(e)}")

    def list_users(self, 
                  filter_key: Optional[str] = None,
                  filter_value: Optional[str] = None,
                  limit: int = 60) -> List[Dict]:
        """
        List users with optional filtering
        
        Args:
            filter_key: Optional attribute to filter on
            filter_value: Optional value to filter by
            limit: Maximum number of users to return
            
        Returns:
            List of user information dictionaries
        """
        try:
            params = {
                'UserPoolId': self.user_pool_id,
                'Limit': limit
            }
            
            if filter_key and filter_value:
                params['Filter'] = f"{filter_key} = \"{filter_value}\""
                
            response = self.cognito.list_users(**params)
            return response['Users']
        except ClientError as e:
            raise Exception(f"Failed to list users: {str(e)}")

    def get_user(self, username: str) -> Dict:
        """Get user details by username"""
        try:
            return self.cognito.admin_get_user(
                UserPoolId=self.user_pool_id,
                Username=username
            )
        except ClientError as e:
            raise Exception(f"Failed to get user: {str(e)}")

    def delete_user(self, identifier: str) -> None:
        """Delete user by username or email"""
        try:
            user = self.get_user_by_identifier(identifier)
            self.cognito.admin_delete_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
        except ClientError as e:
            raise Exception(f"Failed to delete user: {str(e)}")

    def enable_user(self, identifier: str) -> Dict:
        """
        Enable a user by username or email
        
        Args:
            identifier: Username or email of the user to enable
            
        Returns:
            Dict: Response from Cognito
            
        Raises:
            Exception: If enabling user fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            response = self.cognito.admin_enable_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to enable user: {str(e)}")
    
    def disable_user(self, identifier: str) -> Dict:
        """
        Disable a user by username or email
        
        Args:
            identifier: Username or email of the user to disable
            
        Returns:
            Dict: Response from Cognito
            
        Raises:
            Exception: If disabling user fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            response = self.cognito.admin_disable_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to disable user: {str(e)}")

    def get_user_attributes(self, identifier: str, custom_only: bool = False) -> Dict:
        """
        Get user attributes in a simplified dictionary format
        
        Args:
            identifier: Username or email
            custom_only: Whether to return only custom attributes
            
        Returns:
            Dictionary of user attributes
        """
        user = self.get_user_by_identifier(identifier)
        attributes = {}
        
        for attr in user.get('UserAttributes', []):
            name = attr['Name']
            if custom_only and not name.startswith('custom:'):
                continue
            attributes[name] = attr['Value']
            
        return attributes
    
    def configure_email_templates(self,
                                email_subject: str = "Welcome to Our Application!",
                                email_message: str = None,
                                use_ses: bool = True,
                                ses_source_arn: Optional[str] = None,
                                ses_from_email: Optional[str] = None,
                                ses_reply_to: Optional[str] = None) -> Dict:
        """
        Configure User Pool email templates for welcome messages
        
        Args:
            email_subject: Subject line for welcome emails
            email_message: Custom message template. Available variables:
                {username} - the username
                {####} - the temporary password
            use_ses: Whether to use Amazon SES for sending emails
            ses_source_arn: ARN of the SES identity to use (required if use_ses is True)
            ses_from_email: Email address to use as the sender (required if use_ses is True)
            ses_reply_to: Email address to use for replies (optional)
                
        Returns:
            Dict containing the API response
        """
        if email_message is None:
            email_message = """
Hello {username},

Welcome to our application! We're excited to have you on board.

Your temporary login credentials are:
Username: {username}
Temporary Password: {####}

Please log in and change your password at your earliest convenience.

Best regards,
Your App Team"""

        try:
            email_config = {}
            
            if use_ses:
                if not ses_source_arn or not ses_from_email:
                    raise ValueError("ses_source_arn and ses_from_email are required when use_ses is True")
                
                email_config = {
                    'EmailSendingAccount': 'DEVELOPER',
                    'SourceArn': ses_source_arn,
                    'From': ses_from_email
                }
                
                if ses_reply_to:
                    email_config['ReplyToEmailAddress'] = ses_reply_to
            else:
                email_config = {
                    'EmailSendingAccount': 'COGNITO_DEFAULT'
                }

            response = self.cognito.update_user_pool(
                UserPoolId=self.user_pool_id,
                EmailConfiguration=email_config,
                AdminCreateUserConfig={
                    'AllowAdminCreateUserOnly': True,
                    'InviteMessageTemplate': {
                        'EmailMessage': email_message,
                        'EmailSubject': email_subject
                    }
                }
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to configure email templates: {str(e)}")

    def check_if_attribute_exists(self, attribute_name):
        # Describe the user pool to get the schema
        response = self.cognito.describe_user_pool(UserPoolId=self.user_pool_id)
        
        # Get the list of schema attributes
        schema_attributes = response['UserPool']['SchemaAttributes']
        print("SCHEMA ATTRIBUTES: ", schema_attributes)
        
        # Check if the attribute exists in the schema
        for attribute in schema_attributes:
            if attribute['Name'] == attribute_name:
                return True  # Attribute exists
        return False  # Attribute does not exist

    def add_free_tier_to_pool(self):
        # Describe the user pool to get the current schema
        if self.check_if_attribute_exists('custom:free_user'):
            print('Free tier attribute already exist!')
            return

        response = self.cognito.add_custom_attributes(
            UserPoolId=self.user_pool_id,
            CustomAttributes=[
                {
                    'Name': 'free_user',
                    'AttributeDataType': 'Boolean',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False
                }
            ]
        )
        print("Updated schema:", response)