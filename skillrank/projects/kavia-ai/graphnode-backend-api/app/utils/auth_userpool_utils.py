# import jwt
# from functools import lru_cache
# import time
# import boto3
# import requests
# from app.core.Settings import settings
# import json
# import base64
# from typing import Dict
# from fastapi import HTTPException, status, Request
# from cryptography.hazmat.primitives import serialization
# from cryptography.hazmat.backends import default_backend
# from cryptography.hazmat.primitives.asymmetric import rsa

# region = "us-east-1"

# class CognitoAuthManager:
#     def __init__(self):
#         self.region = region
#         self.cognito_client = boto3.client(
#             "cognito-idp",
#             region_name=self.region,
#             aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
#         )

#     @lru_cache(maxsize=None)
#     def get_jwks(self, pool_id: str):
#         keys_url = f"https://cognito-idp.{self.region}.amazonaws.com/{pool_id}/.well-known/jwks.json"
#         response = requests.get(keys_url)
#         return json.loads(response.text)["keys"]

#     def verify_token_and_get_user_details(self, token: str, pool_id: str) -> dict:
#         try:
#             # print("Starting token verification process")
            
#             if token.startswith("Bearer "):
#                 token = token[7:]
#                 # print("Removed 'Bearer ' prefix from token")

#             # Check if this is a Google SSO token first
#             try:
#                 # Decode without verification to check the issuer
#                 unverified_claims = jwt.decode(token, options={"verify_signature": False})
                
#                 # If it's a Google SSO token, verify with our custom secret
#                 if unverified_claims.get('iss') == 'kavia-google-sso' or unverified_claims.get('auth_provider') == 'google_sso':
#                     # Verify Google SSO token with our secret
#                     claims = jwt.decode(
#                         token, 
#                         "kavia-google-sso-secret", 
#                         algorithms=["HS256"],
#                         options={"verify_exp": True}
#                     )
#                     return claims
                    
#             except jwt.ExpiredSignatureError:
#                 raise HTTPException(status_code=401, detail="Google SSO token has expired")
#             except jwt.InvalidTokenError:
#                 # Not a Google SSO token, continue with regular Cognito verification
#                 pass

#             # Regular Cognito token verification
#             headers = jwt.get_unverified_header(token)
#             kid = headers["kid"]

#             # Decode token without verification to get audience
#             unverified_claims = jwt.decode(token, options={"verify_signature": False})
#             audience = unverified_claims.get('aud')

#             key = next((k for k in self.get_jwks(pool_id) if k["kid"] == kid), None)
#             if not key:
#                 print("No matching key found in JWKS")
#                 raise HTTPException(status_code=401, detail="Invalid token")

#             public_key = rsa.RSAPublicNumbers(
#                 n=int.from_bytes(base64.urlsafe_b64decode(key["n"] + "=="), byteorder="big"),
#                 e=int.from_bytes(base64.urlsafe_b64decode(key["e"] + "=="), byteorder="big"),
#             ).public_key(backend=default_backend())

#             public_key_pem = public_key.public_bytes(
#                 encoding=serialization.Encoding.PEM,
#                 format=serialization.PublicFormat.SubjectPublicKeyInfo
#             )
#             claims = jwt.decode(
#                 token,
#                 public_key_pem,
#                 algorithms=["RS256"],
#                 audience=audience,  # Use extracted audience
#                 options={"verify_exp": True}
#             )
#             return claims

#         except Exception as e:
#             print(f"Error during token verification: {str(e)}")
#             raise HTTPException(status_code=401, detail=str(e))

# cognito_manager = CognitoAuthManager()
import jwt
from functools import lru_cache
import time
import boto3
import requests
from app.core.Settings import settings
import json
import base64
from typing import Dict, Optional
from fastapi import HTTPException, status, Request
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import rsa
import logging

logger = logging.getLogger(__name__)
region = "us-east-1"

class CognitoAuthManager:
    def __init__(self):
        self.region = region
        self.cognito_client = boto3.client(
            "cognito-idp",
            region_name=self.region,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

    @lru_cache(maxsize=None)
    def get_jwks(self, pool_id: str) -> list:
        """Get JWKS keys for a Cognito User Pool with caching"""
        try:
            keys_url = f"https://cognito-idp.{self.region}.amazonaws.com/{pool_id}/.well-known/jwks.json"
            response = requests.get(keys_url)
            response.raise_for_status()
            return response.json()["keys"]
        except Exception as e:
            logger.error(f"Failed to fetch JWKS for pool {pool_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch token verification keys")

    def _is_google_sso_token(self, token: str) -> bool:
        """Check if token is a Google SSO token without full verification"""
        try:
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            issuer = unverified_claims.get('iss', '')
            auth_provider = unverified_claims.get('auth_provider', '')
            
            return (issuer == 'kavia-google-sso' or 
                   auth_provider in ['google_sso', 'google'])
        except Exception:
            return False

    def _verify_google_sso_token(self, token: str) -> dict:
        """Verify Google SSO token with custom secret"""
        try:
            # First, decode without verification to check the claims
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            logger.debug(f"Google SSO token claims: {unverified_claims}")
            
            # Verify with relaxed options for Google SSO tokens
            claims = jwt.decode(
                token, 
                "kavia-google-sso-secret", 
                algorithms=["HS256"],
                options={
                    "verify_exp": True,
                    "verify_aud": False,  # Disable audience verification for Google SSO
                    "verify_iss": False,  # Disable issuer verification
                    "verify_signature": True
                }
            )
            
            logger.info(f"Successfully verified Google SSO token for user: {claims.get('email', 'unknown')}")
            return claims
            
        except jwt.ExpiredSignatureError:
            logger.warning("Google SSO token has expired")
            raise HTTPException(status_code=401, detail="Google SSO token has expired")
        except jwt.InvalidSignatureError:
            logger.error("Invalid Google SSO token signature")
            raise HTTPException(status_code=401, detail="Invalid Google SSO token signature")
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid Google SSO token: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Invalid Google SSO token: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error verifying Google SSO token: {str(e)}")
            raise HTTPException(status_code=401, detail="Google SSO token verification failed")

    def _verify_cognito_token(self, token: str, pool_id: str) -> dict:
        """Verify regular Cognito JWT token"""
        try:
            # Get token headers
            headers = jwt.get_unverified_header(token)
            kid = headers.get("kid")
            
            if not kid:
                raise HTTPException(status_code=401, detail="Token missing key ID")

            # Get unverified claims for audience
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            audience = unverified_claims.get('aud')

            # Find matching key in JWKS
            jwks_keys = self.get_jwks(pool_id)
            key = next((k for k in jwks_keys if k["kid"] == kid), None)
            
            if not key:
                logger.error(f"No matching key found for kid: {kid}")
                raise HTTPException(status_code=401, detail="Invalid token key")

            # Construct public key
            public_key = rsa.RSAPublicNumbers(
                n=int.from_bytes(base64.urlsafe_b64decode(key["n"] + "=="), byteorder="big"),
                e=int.from_bytes(base64.urlsafe_b64decode(key["e"] + "=="), byteorder="big"),
            ).public_key(backend=default_backend())

            public_key_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )

            # Verify token
            claims = jwt.decode(
                token,
                public_key_pem,
                algorithms=["RS256"],
                audience=audience,
                options={"verify_exp": True}
            )
            
            logger.info(f"Successfully verified Cognito token for user: {claims.get('cognito:username', 'unknown')}")
            return claims

        except jwt.ExpiredSignatureError:
            logger.warning("Cognito token has expired")
            raise HTTPException(status_code=401, detail="Token has expired")
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid Cognito token: {str(e)}")
            raise HTTPException(status_code=401, detail="Invalid token")
        except Exception as e:
            logger.error(f"Error verifying Cognito token: {str(e)}")
            raise HTTPException(status_code=401, detail="Token verification failed")

    def verify_token_and_get_user_details(self, token: str, pool_id: str) -> dict:
        """
        Verify JWT token and return user details.
        Supports both Cognito tokens and Google SSO tokens.
        """
        try:
            # Clean token
            if token.startswith("Bearer "):
                token = token[7:]

            # Log token type for debugging
            logger.debug(f"Verifying token starting with: {token[:20]}...")

            # Check if it's a Google SSO token first
            if self._is_google_sso_token(token):
                logger.info("Detected Google SSO token")
                return self._verify_google_sso_token(token)
            else:
                logger.info("Detected Cognito token")
                return self._verify_cognito_token(token, pool_id)

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error during token verification: {str(e)}")
            raise HTTPException(
                status_code=401, 
                detail=f"Token verification failed: {str(e)}"
            )

    def get_user_info_from_token(self, token: str, pool_id: str) -> dict:
        """
        Extract user information from verified token claims.
        Returns standardized user info regardless of token type.
        """
        claims = self.verify_token_and_get_user_details(token, pool_id)
        
        # Standardize user info format
        user_info = {
            "user_id": claims.get("sub"),
            "email": claims.get("email"),
            "username": claims.get("cognito:username") or claims.get("email"),
            "is_admin": claims.get("custom:is_admin") == "true",
            "is_free_user": claims.get("custom:free_user") == "true",
            "tenant_id": claims.get("custom:tenant_id"),
            "auth_provider": claims.get("auth_provider", "cognito"),
            "token_type": claims.get("token_use", "access"),
            "expires_at": claims.get("exp"),
            "issued_at": claims.get("iat")
        }
        
        return user_info

# Create singleton instance
cognito_manager = CognitoAuthManager()