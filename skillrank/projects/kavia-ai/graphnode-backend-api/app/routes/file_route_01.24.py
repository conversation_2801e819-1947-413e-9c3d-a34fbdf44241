import uuid
import mimetypes
import json
import yaml
import os
import time
import asyncio
from fastapi import APIRouter, UploadFile, File, HTTPException, Form,Depends,BackgroundTasks
from fastapi.responses import JSONResponse
from app.utils.aws_utils import extract_text_with_textract, upload_file_to_s3, get_s3_file_url
from app.utils.file_utils.pdf_utils import extract_text_from_pdf
from app.utils.file_utils.excel_utils import extract_text_from_excel
from app.utils.file_utils.text_utils import extract_text_from_text_file
from app.utils.file_utils.image_utils import extract_text_from_image, generate_preview, generate_thumbnail, get_primary_color, image_to_bytes, image_to_base64url
from app.utils.file_utils.upload_utils import upload_and_process, create_thumbnail
from PIL import Image
from io import BytesIO, StringIO
import io
import logging
from app.connection.tenant_middleware import get_tenant_id
from app.utils.prodefn.projdefn import Proj<PERSON><PERSON><PERSON>, ProjDefnReporter, ProjDefnDocSpecifier
from app.utils.prodefn.projdefn_helper import Reporter, Helpers, ProjDefn_Helper
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.auth_utils import get_current_user
from app.utils.file_utils.upload_utils import upload_and_process, s3_client, get_tenant_bucket
from app.utils.file_utils.upload_utils import generate_document_presigned_url
from app.connection.establish_db_connection import get_mongo_db
from datetime import datetime
from app.core.Settings import Settings

_SHOW_NAME = "file"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

settings = Settings()


# Set up logging
def setup_logger(name, base_path, log_level=logging.INFO):
    """Set up a logger with a file handler"""
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Prevent the logger from propagating messages to the root logger
    logger.propagate = False

    # Create logs directory if it doesn't exist
    log_dir = os.path.join(base_path, "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Create file handler
    file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
    file_handler.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # Add file handler to logger
    logger.addHandler(file_handler)
    logger.propagate = False

    return logger

def get_file_type(filename):
    return mimetypes.guess_type(filename)[0] or 'application/octet-stream'

def initialize_projdefn(base_path, current_user, session_id=None,tenant_id=None,project_id=None):
    """Initialize the project definition handler"""
    reporter = Reporter()
    configuration = {
        "base_path": base_path,
        "model": "gpt-4o-mini",
        "timeout": 60,
        "chunk_size": 64*1024,
        "cost_tracer": None,
        "reporter": reporter,
        "helpers": Helpers(base_path)
    }
    
    # Get or create ProjDefn instance with session
    projdefn = ProjDefn.getInstance(configuration, "default", session_id,tenant_id,project_id)
    projdefn.start()
    return projdefn

def wait_for_processing_complete(reporter, timeout=300):
    """Wait for document processing to complete"""
    start_time = time.time()
    while not reporter.is_ready() and (time.time() - start_time) < timeout:
        time.sleep(1)
    return reporter.is_ready()

@router.post("/upload")
async def upload(file: UploadFile = File(...), discussion_id: str = Form(...)):
    tenant_id = get_tenant_id()
    content = await file.read()
    
    # Check file type using mimetypes
    file_type = get_file_type(file.filename)
    
    if not file_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="Only image files are allowed")

    try:
        # Generate a UUID for the file
        file_uuid = str(uuid.uuid4())
        
        # Process the image
        with Image.open(BytesIO(content)) as img:
            width, height = img.size
            
            # Create thumbnail
            thumbnail = create_thumbnail(img.copy())
            thumbnail_io = BytesIO()
            thumbnail.save(thumbnail_io, format=img.format)
            thumbnail_io.seek(0)
            
            # Create preview (full-size image)
            preview_io = BytesIO()
            img.save(preview_io, format=img.format)
            preview_io.seek(0)
        
        # Upload original file
        original_data = upload_and_process(file_uuid, content, f"original_{file.filename}", file_type, tenant_id=tenant_id)

        # Generate base64 URLs
        original_url = image_to_base64url(content, file_type)
        thumbnail_url = image_to_base64url(thumbnail_io.getvalue(), file_type)
        preview_url = image_to_base64url(preview_io.getvalue(), file_type)
        
        # Construct the response
        response = {
            "file_kind": "image",
            "file_uuid": file_uuid,
            "file_name": file.filename,
            "file_type": file_type,
            "original_url": original_url,
            "thumbnail_url": thumbnail_url,
            "preview_url": preview_url,
            "thumbnail_asset": {
                "url": thumbnail_url,
                "file_variant": "thumbnail",
                "primary_color": "ffffff",
                "image_width": min(400, width),
                "image_height": int(min(400, width) * height / width)
            },
            "preview_asset": {
                "url": preview_url,
                "file_variant": "preview",
                "primary_color": "ffffff",
                "image_width": width,
                "image_height": height
            },
            "discussion_id": discussion_id,
            "s3_location": original_data['s3_location']
        }
        
        return JSONResponse(content=response, status_code=200)
    
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
async def get_document_handler():
    """Get MongoDB handler for ingested_documents collection"""
    return get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='ingested_documents'
    )


async def update_document_status(file_uuid: str, status_update: dict):
    """Update the document status in the ingested_documents collection"""
    # Get MongoDB handler
    doc_handler = await get_document_handler()
    
    # Create the processing_status update
    processing_status = {
        "status": status_update.get("status", "processing"),
        "updated_at": datetime.now()
    }
    
    # Add additional fields if they exist
    if "error" in status_update:
        processing_status["error"] = status_update["error"]
    if "error_details" in status_update:
        processing_status["error_details"] = status_update["error_details"]
    if "progress" in status_update:
        processing_status["progress"] = status_update["progress"]
    if "s3_location" in status_update:
        processing_status["s3_location"] = status_update["s3_location"]
    if "end_time" in status_update:
        processing_status["end_time"] = status_update["end_time"]
    if "start_time" in status_update:
        processing_status["start_time"] = status_update["start_time"]

    # First try to find the document
    existing_doc = await doc_handler.get_one(
        {"document_record.file_uuid": file_uuid},
        db=doc_handler.db
    )

    if existing_doc:
        print(f"Updating existing document for file_uuid: {file_uuid}")
        # Update only processing status for existing document
        update_data = {"processing_status": processing_status}
        result = await doc_handler.update_one(
            {"document_record.file_uuid": file_uuid},
            update_data,
            db=doc_handler.db
        )
    else:
        print(f"Creating new document for file_uuid: {file_uuid}")
        # Create complete document for new entry
        document_record = {
            "project_id": status_update.get("project_id"),
            "file_name": status_update.get("filename", "Unknown"),
            "file_uuid": file_uuid,
            "file_type": status_update.get("file_type", "application/pdf"),
            "file_extension": status_update.get("file_extension", ""),
            "file_size": status_update.get("file_size", 0),
            "uploaded_by": {
                "user_id": status_update.get("user_id"),
                "user_name": status_update.get("user_name", "Unknown")
            },
            "tenant_id": status_update.get("tenant_id"),
            "session_id": status_update.get("session_id"),
            "created_at": datetime.now()
        }

        new_document = {
            "document_record": document_record,
            "processing_status": processing_status
        }

        # Insert new document
        result = await doc_handler.insert(new_document, db=doc_handler.db)
        print(f"Created new document with ID: {result}")


async def process_document_background(
    file_path: str, 
    file_uuid: str, 
    session_id: str, 
    filename: str, 
    project_id: int,
    tenant_id: str,
    user_id: str
):
    """Background task to process the uploaded document"""
    try:
        # Create initial document record first
        await update_document_status(file_uuid, {
            "status": "processing",
            "start_time": datetime.now(),
            "filename": filename,
            "project_id": project_id,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "session_id": session_id,
            "file_type": "application/pdf",  # or appropriate mime type
            "file_extension": filename.split(".")[-1],
            "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            "progress": {
                "elapsed_time": 0,
                "is_active": True
            }
        })

        base_path = "/tmp/doc_ingestion/dir"
        projdefn_dir = os.path.join(base_path, '.projdefn')
        os.makedirs(projdefn_dir, exist_ok=True)

        reporter = Reporter()
        
        # Initialize project definition handler
        projdefn = initialize_projdefn(base_path, "default_user", session_id, tenant_id, project_id)
        
        doc_spec = ProjDefnDocSpecifier(file_path, file_uuid)
        projdefn.addToIngestQueue(filename, file_path, isS3=True)
        
        # Wait for processing to complete with timeout
        process_timeout = 200
        start_time = time.time()
        
        while not reporter.is_ready() and (time.time() - start_time) < process_timeout:
            await asyncio.sleep(1)
            # Update only progress information
            elapsed_time = time.time() - start_time
            await update_document_status(file_uuid, {
                "status": "processing",
                "progress": {
                    "elapsed_time": round(elapsed_time, 2),
                    "is_active": True
                }
            })
        
        is_complete = reporter.is_ready()
        is_complete = True  # temporary override for testing

        if is_complete:
            # Check if digested file exists in S3
            bucket_name = get_tenant_bucket(tenant_id)
            prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
            
            try:
                s3_client.head_object(Bucket=bucket_name, Key=prefix)
                await update_document_status(file_uuid, {
                    "status": "completed",
                    "end_time": datetime.now(),
                    "s3_location": f"{bucket_name}/{prefix}"
                })
            except Exception as s3_error:
                await update_document_status(file_uuid, {
                    "status": "failed",
                    "end_time": datetime.now(),
                    "error": "Failed to verify digested file",
                    "error_details": str(s3_error)
                })
        else:
            await update_document_status(file_uuid, {
                "status": "failed",
                "end_time": datetime.now(),
                "error": "Processing timed out"
            })

    except Exception as e:
        await update_document_status(file_uuid, {
            "status": "failed",
            "end_time": datetime.now(),
            "error": str(e),
            "error_details": str(e)
        })
    finally:
        if 'projdefn' in locals():
            ProjDefn.releaseInstance("default")

        

@router.post("/extract_text")
async def extract_text(background_tasks: BackgroundTasks,project_id, file: UploadFile = File(...), session_id: str = None,current_user: dict = Depends(get_current_user)):
    content = await file.read()
    file_uuid = str(uuid.uuid4())
    file_type = get_file_type(file.filename)
    file_extension = file.filename.split('.')[-1].lower()
    project_id = int(project_id)
    tenant_id = get_tenant_id()
    print("Tenant_id",tenant_id )

    # Set up base path and logging
    # base_path = "./app/utils/prodefn/dir"
    base_path = "/tmp/doc_ingestion/dir"

    os.makedirs(base_path, exist_ok=True)

    # Create a temporary file path using full absolute path
    safe_filename = file.filename.replace(" ", "_")  # Replace spaces with underscores
    temp_file_path = os.path.abspath(os.path.join(base_path, safe_filename))
    print(f"Writing file to: {temp_file_path}")
    
    try:
        # Create the file
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(content)
            
        print(f"File created successfully at {temp_file_path}")
        print(f"File exists: {os.path.exists(temp_file_path)}")

        # Extract text based on file type
        if file_extension == 'pdf':
            text = extract_text_from_pdf(content)
            if text is None:
                raise HTTPException(status_code=500, detail="Failed to extract text from PDF")
        elif file_extension in ['xlsx', 'xls']:
            text = extract_text_from_excel(content)
        elif file_type.startswith('text/') or file_extension in ['txt', 'csv', 'log', 'py', 'js']:
            text = extract_text_from_text_file(content)
        elif file_extension in ['json']:
            try:
                json_data = json.loads(content.decode('utf-8'))
                text = json.dumps(json_data, indent=2)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON file")
        elif file_extension in ['yml', 'yaml']:
            try:
                yaml_data = yaml.safe_load(content)
                text = yaml.dump(yaml_data, default_flow_style=False)
            except yaml.YAMLError:
                raise HTTPException(status_code=400, detail="Invalid YAML file")
        elif file_extension in ['xml']:
            text = content.decode('utf-8')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        base_path = f"extracted-docs-{tenant_id}"
        project_folder = f"project-{project_id}"
        digested_folder = "digested-file-content"
        original_file_path = f"{base_path}/{project_folder}"

        result = upload_and_process(
            identifier=project_folder,
            file_content=content,
            file_name=file.filename,
            content_type=file_type,
            tenant_id=tenant_id,
            folder_name=base_path
        )

        user_id = current_user.get("cognito:username")
        # Create ingest session
        session_info= await get_or_create_session(user_id, project_id)
        session_id = session_info["session_id"]

        # Add background task
        background_tasks.add_task(
            process_document_background,
            temp_file_path,
            file_uuid,
            session_id,
            file.filename,
            project_id,
            tenant_id,
            user_id
        )

        print("background taks",background_tasks)

     

        print("result",result)


        return JSONResponse(content={
            "message": "Document processing started",
            "file_name": file.filename,
            "file_size": len(content)
        })

    except Exception as e:
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        raise HTTPException(status_code=500, detail=str(e))
    

    


@router.get("/list-project-files/{project_id}")
async def list_project_files(project_id: int, current_user: dict = Depends(get_current_user)):
    try:
        tenant_id = get_tenant_id()
        bucket_name = get_tenant_bucket(tenant_id)
        
        # Construct the prefix for the project folder
        prefix = f"extracted-docs-{tenant_id}/project-{project_id}/"
        
        # List objects in the S3 bucket with the specified prefix
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix
        )
        
        files = []
        if 'Contents' in response:
            for obj in response['Contents']:
                if obj['Key'].lower().endswith('.pdf'):
                    filename = os.path.basename(obj['Key'])
                    
                    # Generate URLs for viewing and downloading
                    view_url = generate_document_presigned_url(
                        bucket=bucket_name,
                        key=obj['Key'],
                        disposition='inline'
                    )
                    
                    download_url = generate_document_presigned_url(
                        bucket=bucket_name,
                        key=obj['Key'],
                        disposition=f'attachment; filename="{filename}"'
                    )
                    
                    files.append({
                        "filename": filename,
                        "size": obj['Size'],
                        "last_modified": obj['LastModified'].isoformat(),
                        "download_url": download_url,
                        "view_url": view_url,
                        "s3_location": f"{bucket_name}/{obj['Key']}"
                    })
        
        return JSONResponse(content={
            "project_id": project_id,
            "file_count": len(files),
            "files": files
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
