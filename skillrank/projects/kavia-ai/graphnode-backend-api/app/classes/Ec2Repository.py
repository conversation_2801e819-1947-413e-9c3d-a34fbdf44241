import boto3
import boto3.session
from app.core import constants
import time,asyncio

class Ec2Repository(object):
    def __init__(self, creds:dict,ami_id):
        self.client: str = boto3.client('ec2',**creds)
        self.cloudwatch: str = boto3.client('cloudwatch', **creds)
        self.creds = creds
        self.ami_id = ami_id

    def check_cloudwatch_alarm_for_instance(self, instance_id):
        """
        Checks if there are any active CloudWatch alarms for a given instance.
        Returns True if any alarms are found, otherwise returns False.
        """
        response = self.cloudwatch.describe_alarms_for_metric(
            MetricName='CPUUtilization',
            Namespace='AWS/EC2',
            Dimensions=[
                {
                    'Name': 'InstanceId',
                    'Value': instance_id
                },
            ]
        )

        for alarm in response['MetricAlarms']:
            if alarm['StateValue'] in ['ALARM', 'INSUFFICIENT_DATA']:
                return True

        return False

    def create_cpu_alarm(self,instance_id, threshold=1, period=300, evaluation_periods=6, region='us-east-1'):
        alarm_name = f"checking-activity-{instance_id}"
        response = self.cloudwatch.put_metric_alarm(
            AlarmName=alarm_name,
            AlarmDescription='Alarm for checking inactivity based on CPUUtilization',
            ActionsEnabled=True,
            AlarmActions=[
                f'arn:aws:automate:{region}:ec2:stop'
            ],
            MetricName='CPUUtilization',
            Namespace='AWS/EC2',
            Statistic='Average',
            Dimensions=[
                {
                    'Name': 'InstanceId',
                    'Value': instance_id
                },
            ],
            Period=period,
            EvaluationPeriods=evaluation_periods,
            Threshold=threshold,
            ComparisonOperator='LessThanOrEqualToThreshold',
            TreatMissingData='missing'
        )
        return response


    def describe_instance_status(self, instance_id):
        response = self.client.describe_instance_status(
            InstanceIds=[
                instance_id,
            ]
        )
        return response

    def wait_until_instance_available(self,instance_id):
        instances = self.describe_instance_status(instance_id)
        for _ in range(60):
            instances = self.describe_instance_status(instance_id)
            print(instances["InstanceStatuses"][0]["InstanceStatus"]["Status"])
            if instances["InstanceStatuses"][0]["InstanceStatus"]["Status"] == "ok":
                print("Instance Available")
                return instances["InstanceStatuses"][0]
            print("Sleeping 5 sec....")
            time.sleep(5)

    def wait_until_instance_created(self,instance_id,old_instance=False):
        instances = self.describe_instances_by_tag([instance_id])
        for _ in range(60):
            instances = self.describe_instances_by_tag([instance_id])
            print(instances["Reservations"][0]["Instances"][0]["State"]["Name"])
            if instances["Reservations"][0]["Instances"][0]["State"]["Name"] == "running":
                print("Instance Created")
                print("Wait until instance available")
                # self.wait_until_instance_available(instance_id)
                if old_instance: time.sleep(15)
                else: time.sleep(20)
                return instances["Reservations"][0]["Instances"][0]
            print("Sleeping 5 sec....")
            time.sleep(5)

    async def wait_until_instance_created_stream(self,instance_id):
        instances = self.describe_instances_by_tag([instance_id])
        for _ in range(60):
            instances = self.describe_instances_by_tag([instance_id])
            # print(instances["Reservations"][0]["Instances"][0]["State"]["Name"])
            if instances["Reservations"][0]["Instances"][0]["State"]["Name"] == "running":
                # print("Instance Created")
                # print("Wait until instance available")
                yield {"message":"Building Environment...","error":False,"end":False}
                # self.wait_until_instance_available(instance_id)
                # time.sleep(20)
                for i in range(10):
                    await asyncio.sleep(2)
                    yield {"message":f"Workspace will available in {20 - i*2} seconds.","error":False,"end":False}
                yield {
                    'response':instances["Reservations"][0]["Instances"][0],
                    'end':True
                    }
                break
            yield {"message":"Building Workspace...","error":False,"end":False}
            time.sleep(2)


    def describe_instances_by_tag(self,instances_id=None,tag=None,filters=None):
        query = {}
        if not instances_id:
            query["Filters"] = [ { 'Name': tag, 'Values': filters } ]
        else:
            query["InstanceIds"] = instances_id
        response = self.client.describe_instances(**query)

        return response


    def create_instances(self,name,project_obj):
        response = self.client.run_instances(
            ImageId=self.ami_id,
            InstanceType='t3.micro',
            MaxCount=1,
            MinCount=1,
            SecurityGroupIds=['sg-09c88ecd8dbc64411'],
            SubnetId='subnet-0d9e5a0ef8e431281',
            UserData=constants.user_data_script.format(
                aws_access_key_id=project_obj['aws_access_key_id'],
                aws_secret_access_key=project_obj['aws_secret_access_key'],
                region_name=project_obj['region_name'],
                user_name=project_obj['user_name'],
                email=project_obj['email'],
                repo_url=project_obj['repo_url']
                ),
            TagSpecifications=[
                {
                    'ResourceType': 'instance',
                    'Tags': [
                        {
                            'Key': 'Name',
                            'Value': name
                        }
                    ]
                }
            ]
        )
        print(response)
        return response

    def start_instances(self,instance_id):
        response = self.client.start_instances(
            InstanceIds=[
                instance_id,
            ]
        )
        return response

    def stop_instances(self,instance_id):
        response = self.client.stop_instances(
            InstanceIds=[
                instance_id,
            ]
        )
        return response
    
    def terminate_instance(self, instance_id):
        """
        Terminates an EC2 instance using the provided instance ID.
        """
        response = self.client.terminate_instances(
            InstanceIds=[instance_id]
        )
        return response