# --------------------------------------------------------------------------------
# Company Name: Kavia AI
# Author: <PERSON><PERSON><PERSON>
# Creation Date: Year (2024)
#
# Confidential Information of Kavia AI
# NOTICE: All information contained herein is, and remains the property of Kavia AI.
# The intellectual and technical concepts contained herein are proprietary to Kavia AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Kavia AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Kavia AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------
from pymongo import MongoClient

import os
import configparser
from app.connection.llm_init import get_llm_interface
from app.connection.tenant_middleware import get_tenant_based_db_name

import asyncio

llm_interface = get_llm_interface()
class VectorDbHandler():
    def __init__(self,mongo_connection_uri,db_name):
        db_name = get_tenant_based_db_name(db_name)
        self.client = MongoClient(mongo_connection_uri)
        print("VectorDbHandler initialized with db_name:", db_name)
        self.db_name = db_name
        self.db = self.client[db_name]
        self.vector_collection = self.db.graphIndex
        
    async def add_node_to_vector_db(self, node_id, properties, root_id , node_type=None):
        # Assuming `generate_embedding` can be awaited or wrapped in an async call

        embedding = await asyncio.to_thread(llm_interface.generate_embedding, properties)
        
        
        filter_query = { 'node_id': node_id }  
        insert_query = { 'node_id': node_id,'root_id': root_id ,'embedding': embedding }
        
        if node_type:
            insert_query['node_type'] = node_type
        # Insert the embedding into the MongoDB collection asynchronously
        await asyncio.to_thread(self.vector_collection.update_one, filter_query, {'$set': insert_query}, upsert=True)

    async def update_node_in_vector_db(self, node_id, properties, root_id=None ,node_type=None):
        # Regenerate the embedding with updated properties asynchronously
        embedding = await asyncio.to_thread(llm_interface.generate_embedding, properties)
        update_query = {'embedding': embedding}
        if node_type:
            update_query['node_type'] = node_type
        # Update the embedding in the MongoDB collection asynchronously
        await asyncio.to_thread(self.vector_collection.update_one, {'node_id': node_id}, {'$set': update_query}, upsert=True)

    async def update_nodes_in_vector_db(self, node_ids, properties):
        # Regenerate the embeddings with updated properties asynchronously
        embeddings = await asyncio.gather(*(asyncio.to_thread(llm_interface.generate_embedding, properties) for _ in node_ids))
        # Update the embeddings in the MongoDB collection asynchronously
        updates = [{'node_id': node_id, 'embedding': embedding} for node_id, embedding in zip(node_ids, embeddings)]
        for update in updates:
            await asyncio.to_thread(self.vector_collection.update_one, {'node_id': update['node_id']}, {'$set': {'embedding': update['embedding']}}, upsert=True)

    async def delete_node_from_vector_db(self, node_id):
        # Delete the node from the MongoDB collection asynchronously
        await asyncio.to_thread(self.vector_collection.delete_one, {'node_id': node_id})

    async def find_similar_nodes(self, node_id ,properties, root_id ,max_results=10):
        return [] # temporary disabled
        # Generate an embedding for the query properties asynchronously
        return [] #temporaly disabled
        embedding = []
     
        node = self.vector_collection.find_one({'node_id': node_id, 'root_id': root_id})
        if not node:
            return []
        embedding = node.get('embedding')       
        if not embedding: 
            embedding = await asyncio.to_thread(llm_interface.generate_embedding, properties)
        # Query the MongoDB collection for similar embeddings asynchronously. This part is pseudo-code and needs a custom implementation
        # for calculating similarity in MongoDB
            # Perform Atlas Search query (assuming MongoDB Atlas and vector index)

        query_results = await asyncio.to_thread(
            self.vector_collection.aggregate, [
       
                    {
                        "$vectorSearch": {
                        "index": self.db_name,
                        "path": "embedding",
                        "queryVector": embedding,
                        "numCandidates": 150,
                        "limit": max_results,
                        }
                    },
                    {
                    "$match": {"root_id": root_id}
                    },
            ]
        )
        query_results = list(query_results)
        # Extract node IDs
        if query_results:
            similar_node_ids = [result.get('node_id') for result in query_results]
            return similar_node_ids
        else:
            return []

    async def get_node_embedding(self, node_id):
        embedding = None
        query_results = await asyncio.to_thread(
            self.vector_collection.aggregate, [
                    {
                    "$match": {"node_id": node_id}
                    },
            ]
        )
        query_results = list(query_results)
        if query_results:
            embeddings = [result.get('embedding') for result in query_results]
            if embeddings:
                embedding = embeddings[0]
        return embedding
