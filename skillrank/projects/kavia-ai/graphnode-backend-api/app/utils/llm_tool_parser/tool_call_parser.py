# import re
# import json

# def parse_tool_info_streaming(value, current_operations=None):
#     """
#     Parse Tool_Info value for streaming updates in real-time
#     with simplified raw content extraction
    
#     Args:
#         value (dict): The Tool_Info value containing function details
#         current_operations (dict, optional): Dictionary to track operations in progress
    
#     Returns:
#         tuple: (parsed_data, updated, current_operations)
#             - parsed_data: Current state of the operation
#             - updated: <PERSON><PERSON><PERSON> indicating if content was updated (for streaming)
#             - current_operations: Updated tracking dictionary
#     """
#     # Initialize tracking dictionary if needed
#     if current_operations is None:
#         current_operations = {}
        
#     function_name = value.get("function_name")
#     tool_call_id = value.get("tool_call_id", "")
#     args_str = value.get("function_args", "")
#     is_complete = value.get("is_complete", False)
    
#     # Initialize or continue tracking this operation
#     if tool_call_id not in current_operations:
#         current_operations[tool_call_id] = {
#             "operation": function_name,
#             "tool_call_id": tool_call_id,
#             "file_path": "",
#             "content": ""
#         }
    
#     # Track if content was updated
#     prev_content = current_operations[tool_call_id]["content"]
#     content_updated = False
    
#     # Handle different operation types
#     if function_name == "create_file":
#         # Extract file path if not already set
#         if not current_operations[tool_call_id]["file_path"]:
#             path_match = re.search(r'"file_path":\s*"([^"]*)"', args_str)
#             if path_match:
#                 current_operations[tool_call_id]["file_path"] = path_match.group(1)
        
#         # ULTRA SIMPLE APPROACH: Take everything after "content":
#         content_tag = '"content":'
#         content_start_pos = args_str.find(content_tag)
#         if content_start_pos >= 0:
#             # Just take everything after the "content": tag
#             # This will include the quotes, JSON syntax, etc. but we won't try to parse it
#             content_raw = args_str[content_start_pos + len(content_tag):]
            
#             # Store it without any processing
#             current_operations[tool_call_id]["content"] = content_raw
    
#     elif function_name == "edit_block":
#         # Extract file path if not already set
#         if not current_operations[tool_call_id]["file_path"]:
#             match = re.search(r'"changes":\s*"([^"\\]*?)\\n', args_str)
#             if match:
#                 current_operations[tool_call_id]["file_path"] = match.group(1)
        
#         # Just store the raw args_str as content without any processing
#         current_operations[tool_call_id]["content"] = args_str
    
#     # Check if content actually changed
#     content_updated = prev_content != current_operations[tool_call_id]["content"]
    
#     return current_operations[tool_call_id], content_updated, current_operations

import re
import json

def parse_tool_info_streaming(value, current_operations=None):
    """
    Parse Tool_Info value for streaming updates in real-time
    with escape sequence handling
    
    Args:
        value (dict): The Tool_Info value containing function details
        current_operations (dict, optional): Dictionary to track operations in progress
    
    Returns:
        tuple: (parsed_data, updated, current_operations)
            - parsed_data: Current state of the operation
            - updated: Boolean indicating if content was updated (for streaming)
            - current_operations: Updated tracking dictionary
    """
    # Initialize tracking dictionary if needed
    if current_operations is None:
        current_operations = {}
        
    function_name = value.get("function_name")
    tool_call_id = value.get("tool_call_id", "")
    args_str = value.get("function_args", "")
    is_complete = value.get("is_complete", False)
    
    # Initialize or continue tracking this operation
    if tool_call_id not in current_operations:
        current_operations[tool_call_id] = {
            "operation": function_name,
            "tool_call_id": tool_call_id,
            "file_path": "",
            "content": ""
        }
    
    # Track if content was updated
    prev_content = current_operations[tool_call_id]["content"]
    content_updated = False
    
    # Handle different operation types
    if function_name == "create_file":
        # Extract file path if not already set
        if not current_operations[tool_call_id]["file_path"]:
            path_match = re.search(r'"file_path":\s*"([^"]*)"', args_str)
            if path_match:
                current_operations[tool_call_id]["file_path"] = path_match.group(1)
        
        # Extract content properly from the JSON args
        try:
            # Try parsing the function_args as JSON first
            args_dict = json.loads(args_str)
            if "content" in args_dict:
                # Extract clean content directly from the parsed JSON
                clean_content = args_dict["content"]
                current_operations[tool_call_id]["content"] = clean_content
        except json.JSONDecodeError:
            # Fallback to regex extraction if JSON parsing fails
            content_tag = '"content":'
            content_start_pos = args_str.find(content_tag)
            if content_start_pos >= 0:
                # Get the raw content after the "content": tag
                content_raw = args_str[content_start_pos + len(content_tag):]
                
                # Find where the content value ends (before the closing brace)
                # Look for the last quote followed by } or ,
                match = re.search(r'(.*[^\\])"(?=\s*[,}])', content_raw)
                if match:
                    # Extract just the content without the closing bracket
                    extracted_content = match.group(1) + '"'
                    
                    # Try to parse the extracted content as a JSON string
                    try:
                        # This will handle all the escaping properly
                        clean_content = json.loads(extracted_content)
                        current_operations[tool_call_id]["content"] = clean_content
                    except json.JSONDecodeError:
                        # If that fails, use the raw handling
                        processed_content = handle_escape_sequences(extracted_content)
                        # Remove surrounding quotes if present
                        processed_content = remove_surrounding_quotes(processed_content)
                        current_operations[tool_call_id]["content"] = processed_content
                else:
                    # If we can't find a clean end, just use the full string with escape handling
                    processed_content = handle_escape_sequences(content_raw)
                    # Remove surrounding quotes if present
                    processed_content = remove_surrounding_quotes(processed_content)
                    current_operations[tool_call_id]["content"] = processed_content
    
    elif function_name == "edit_block":
        # Extract file path if not already set
        if not current_operations[tool_call_id]["file_path"]:
            match = re.search(r'"changes":\s*"([^"\\]*?)\\n', args_str)
            if match:
                current_operations[tool_call_id]["file_path"] = match.group(1)
        
        # Process the content with escape sequences handled
        processed_content = handle_escape_sequences(args_str)
        processed_content = remove_surrounding_quotes(processed_content)
        current_operations[tool_call_id]["content"] = processed_content
    
    # Check if content actually changed
    content_updated = prev_content != current_operations[tool_call_id]["content"]
    
    return current_operations[tool_call_id], content_updated, current_operations

def handle_escape_sequences(content):
    """
    Handle escape sequences in content while preserving structure
    
    Args:
        content (str): Raw content string that may contain escaped sequences
        
    Returns:
        str: Content with properly processed escape sequences
    """
    if not content:
        return content
    
    try:
        # Simple replacement of common escape sequences
        replacements = {
            '\\n': '\n',   # newlines
            '\\t': '\t',   # tabs
            '\\"': '"',    # quotes
            '\\r': '\r'    # carriage returns
        }
        
        # Process the content with replacements
        processed = content
        for escaped, unescaped in replacements.items():
            processed = processed.replace(escaped, unescaped)
            
        return processed
    except Exception as e:
        # If any error occurs, return the original content
        print(f"Error handling escape sequences: {str(e)}")
        return content

def remove_surrounding_quotes(text):
    """
    Remove surrounding quotes from text
    
    Args:
        text (str): Text that may have surrounding quotes
        
    Returns:
        str: Text without surrounding quotes
    """
    # First remove leading whitespace and first quote if present
    if text.startswith(' "') or text.startswith(' \''):
        text = text[2:]
    elif text.startswith('"') or text.startswith('\''):
        text = text[1:]
        
    # Then remove trailing quote if present
    if text.endswith('"}') or text.endswith('\'}'):
        text = text[:-2]
    elif text.endswith('"') or text.endswith('\''):
        text = text[:-1]
        
    return text

