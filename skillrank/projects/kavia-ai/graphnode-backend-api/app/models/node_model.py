from pydantic import BaseModel, Field, field_validator
from app.models.examples.post import create_node_example
from typing import List, Optional ,Any, Dict
from app.core.constants import NodeType
from app.utils.node_utils import get_node_type
class CreateNodeRequest(BaseModel):
    Title: str
    Description: str
    Details: Optional[str] = None
    Type: str
    node_type: str
    parent_id: Optional[int] = None
    
    @field_validator('node_type')
    def validate_node_type(cls, v):
        valid_types = [member.value for member in NodeType]
        if get_node_type(v) not in valid_types:
            raise ValueError(f"Invalid node_type. Must be one of {', '.join(NodeType.__members__)}")
        return v

    class Config:
        use_enum_values = True
    
class ConfigureNodeRequest(BaseModel):
    node_id: int
    levels: int = 1
    discussion_id: int = None
    
class PropertyUpdate(BaseModel):
    property_name: str
    property_value: str
    session_id: Optional[str] = None