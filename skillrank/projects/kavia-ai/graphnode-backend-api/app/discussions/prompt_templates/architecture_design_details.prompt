{% extends "base_discussion.prompt" %}

{%block system_prompt %}
You are an expert software architect who has a deep understanding of system design and architecture and has a proven track record of designing 
scalable and maintainable architectures. You are engaging in a conversation with a developer or a product manager regarding architectural design 
for a new feature or a new product. Make sure that your architectural design addresses all the key functional and non-functional requirements 
of the system. 
{% endblock %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, review the Project Details, parent Container, Functional and Non-Fucntional requirements and configure the given Architecture node:

{% else %}
You are an expert system architect reviewing the Architecture for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Components/Containers: {{ bg_info.get('system_context', {}).get('component_details') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Components/Containers: {{ new_bg.get('system_context', {}).get('component_details') | tojson(indent=2) }}
   

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Container Configuration:
   Existing Containers: {{ details_for_discussion.get('component_details') | tojson(indent=2) }}
   Existing Interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}

7. Current Component Configuration:
   Existing Containers: {{ details_for_discussion.get('component_details') | tojson(indent=2) }}
   Existing Interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}

Analysis Instructions:

1. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries
   - Assess need for new containers or modifications
   - Analyze interface adequacy for new requirements

2. Component Architecture Analysis:
   - Review existing components against new requirements
   - Evaluate component responsibilities and boundaries
   - Assess need for new components or modifications
   - Analyze interface adequacy for new requirements

3. Required Changes:
   Conduct the architecture configuration based on the new requirements
   - IMPORTANT: Always set the "changes_needed" flag to true whenever you make any modifications, including adding new child nodes, modifying existing nodes, or updating any fields.

{% endif %}


{% set architecture_pattern = details_for_discussion.get('architecture_pattern', '').lower() %}

{% if architecture_pattern in ['monolithic', 'multi-container-single-component'] %}
1. IMPORTANT ARCHITECTURAL CONSTRAINT: 
   This is a {{ architecture_pattern }} design - the component contains all functionality for the container.
   - Ensure design aligns with single-component constraint
   - Focus on comprehensive, self-contained design
   - Document how component fulfills all container responsibilities
{% else %}
1. Design component according to its specific responsibilities within the container
{% endif %}

2. Create high level detailed design for the component based on provided requirements.
3. Capture the component's core functions, capabilities, and services it provides in the Functionality Field.

Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.
{% endblock %}
  


{% block autoconfig %}
Create all the required details as catpured in the guidelines above.
{% endblock %}

{% block node_details_interactive_reconfig %}
Review the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them
{% endblock %}

{% block auto_reconfig %}
Create or update the funtional and / or non-functional requirements following the above guidelines. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
design details for the current components
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Here are all the interfaces that are defined for the current component: {{ details_for_discussion.get('direct_interfaces') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }}
    Container Details: {{ details_for_discussion.get('container') }}
    
    Functional Requirements: {{ details_for_discussion.get('functional_requirements') }}
    Non-Functional Requirements: {{ details_for_discussion.get('architectural_requirements') }}
{% endblock %}