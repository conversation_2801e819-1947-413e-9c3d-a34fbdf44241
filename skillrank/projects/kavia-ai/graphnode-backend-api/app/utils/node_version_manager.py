from datetime import datetime
from typing import Dict, List,Optional,Union
from app.connection.establish_db_connection import get_node_db, get_mongo_db
from app.telemetry.logger_config import get_logger
from app.core.data_model_helper import data_model
from app.utils.background_task_reconfig_handler import BackgroundTaskManager
import asyncio

class NodeVersionManager:
    def __init__(self):
        self.db = get_node_db()
        self.mongo_db = get_mongo_db()
        self.collection_prefix = 'node_versions'
        self.version_collection = f"{self.collection_prefix}_context"
        self.changes_collection = f"{self.collection_prefix}_changes"
        self.logger = get_logger(__name__)
        self.cdc_enabled = None
        self.reconfig_tracker_collection = self.mongo_db.db["reconfig_tracker.tasks"]
        self.version_tracker_collection = f"{self.collection_prefix}_tracker"
        self.background_manager = BackgroundTaskManager()
        self.start_time = datetime.now() 

    async def _get_element_id(self, node_id: int) -> str:
        """Get element ID for a node using Neo4j elementId() function"""
        try:
            query = """
            MATCH (n) 
            WHERE ID(n) = $node_id 
            RETURN elementId(n) as element_id
            """
            result = await self.db.async_run(query, node_id=node_id)
            data = result.data()
            if not data:
                raise ValueError(f"Node not found: {node_id}")
            return data[0]['element_id']
        except Exception as e:
            self.logger.error(f"Error getting element ID: {str(e)}")
            raise
        
    async def get_node_properties_versions(self, project_id, node_id: str,node_type:str) -> dict:
    # Get reconfig info
        reconfig_doc = await self.mongo_db.get_one(
            {"project_id": project_id},
            self.mongo_db.db["reconfig_tracker"]
        )
        
        # Get base version using last successful reconfig
        base_version = await self.get_node_info_by_version(
            node_id,
            node_type,
            reconfig_doc["last_successful"]  # This becomes version_number
        )
        
        # Get current version
        current_version = await self.get_node_info_by_version(
            node_id,
            node_type,
            reconfig_doc["current_reconfig"]  # This becomes version_number
        )
        
        if current_version is None:
            current_version = base_version
        print("current_version", current_version)
        
        return {
            "node_id": node_id,
            "base_version": base_version,
            "reconfigured_version": current_version
        }

    async def get_changes_since_timestamp(self, element_id: str) -> List[dict]:
        """Get all changes for an element from CDC"""
        try:
            # Get earliest CDC ID
            cdc_id = await self._get_earliest_cdc_id()
            
            query = """
            CALL db.cdc.query($previous_change_id, [{
                select: "n",
                elementId: $element_id
            }])
            """
            
            result = await self.db.async_run(
                query,
                previous_change_id=cdc_id,
                element_id=element_id
            )
            
            changes = []
            for record in result.data():
                change_dict = {
                    'id': record.get('id'),
                    'metadata': record.get('metadata', {}),
                    'data': record.get('event', {})
                }
                changes.append(change_dict)
            
            if changes:
                changes.pop()
                
            return changes

        except Exception as e:
            self.logger.error(f"Error retrieving changes: {str(e)}")
            raise
        
    async def _update_version_tracker(self, project_id: int, node_id: int, node_type: str, version: int, cdc_event_id: str) -> None:
        """Update the version tracker collection with the new version and CDC ID"""
        try:
            # Check if an entry already exists
            tracker_doc = await self.mongo_db.get_one(
                {
                    "project_id": project_id,
                    "node_id": node_id
                },
                self.mongo_db.db[self.version_tracker_collection]
            )
            
            # Create new document or update existing
            if tracker_doc:
                # Update existing document with new version
                # Get existing versions and add the new one
                versions = tracker_doc.get("versions", {})
                versions[str(version)] = cdc_event_id
                
                # Prepare the update document
                update_doc = {
                    "versions": versions,
                    "updated_at": datetime.now().isoformat()
                }
                
                # Update the document
                await self.mongo_db.update_one(
                    {
                        "project_id": project_id,
                        "node_id": node_id
                    },
                    update_doc,  # No $set, your update_one handles that
                    db=self.mongo_db.db[self.version_tracker_collection]
                )
            else:
                # Create new document
                new_tracker = {
                    "project_id": project_id,
                    "node_id": node_id,
                    "node_type": node_type,
                    "versions": {
                        str(version): cdc_event_id
                    },
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
                
                await self.mongo_db.insert(
                    new_tracker,
                    self.mongo_db.db[self.version_tracker_collection]
                )
                
            print(f"Updated version tracker for node {node_id}, version {version}, CDC ID: {cdc_event_id}")
            self.logger.info(f"Updated version tracker for node {node_id}, version {version}")
            
        except Exception as e:
            print(f"Error updating version tracker: {str(e)}")
            self.logger.error(f"Error updating version tracker: {str(e)}")
            
    
    async def update_section_reconfig_flags(self, project_id):
        """
        Update section-specific reconfiguration flags based on affected nodes.
        These flags will be used to enable/disable the auto-configure buttons in UI.
        Architecture nodes should be excluded from affecting these flags.
        """
        try:
            # Get the reconfig tracker document
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.reconfig_tracker_collection
            )
            
            if not reconfig_doc or "affected_nodes" not in reconfig_doc:
                return

            # Initialize section flags
            project_reconfig = False
            requirement_reconfig = False
            architecture_reconfig = False
            
            # Define node types for each section
            project_section_types = ["Project","RequirementRoot", "Requirement", "Epic", 
                                        "UserStory", "Task","ArchitectureRoot", "ArchitecturalRequirement",
                                        "SystemContext", "Container", "Component", "Design", 
                                        "Interface", "ClassDiagram", "Algorithm", "Sequence", 
                                        "StateDiagram", "UnitTest", "IntegrationTest", "FunctionalRequirement",
                                        "NonFunctionalRequirement"]
                                    
            requirement_section_types = ["RequirementRoot", "Requirement", "Epic", 
                                        "UserStory", "Task"]
                                        
            architecture_section_types = ["ArchitectureRoot", "ArchitecturalRequirement",
                                        "SystemContext", "Container", "Component", "Design", 
                                        "Interface", "ClassDiagram", "Algorithm", "Sequence", 
                                        "StateDiagram", "UnitTest", "IntegrationTest", "FunctionalRequirement",
                                        "NonFunctionalRequirement"]
            
            # Check each affected node
            for node in reconfig_doc["affected_nodes"]:
                if node.get("reconfig_needed", False) is True:
                    node_type = node.get("node_type", "")
                    
                    # Skip nodes with node_type "Architecture"
                    if node_type == "Architecture":
                        continue
                        
                    # Update section flags
                    if node_type in project_section_types:
                        project_reconfig = True
                        
                    if node_type in requirement_section_types:
                        requirement_reconfig = True
                        
                    if node_type in architecture_section_types:
                        architecture_reconfig = True
            
            # Update the document with the section flags
            reconfig_doc["section_reconfig_flags"] = {
                "project_reconfig": project_reconfig,
                "requirement_reconfig": requirement_reconfig,
                "architecture_reconfig": architecture_reconfig
            }
            
            # Remove the propagation_path if requested
            if "propagation_path" in reconfig_doc:
                del reconfig_doc["propagation_path"]
            
            # Update MongoDB
            await self.mongo_db.update_one(
                {"project_id": project_id},
                reconfig_doc,
                upsert=True,
                db=self.reconfig_tracker_collection
            )
            
            return {
                "project_reconfig": project_reconfig,
                "requirement_reconfig": requirement_reconfig,
                "architecture_reconfig": architecture_reconfig
            }
            
        except Exception as e:
            self.logger.error(f"Error updating section reconfig flags: {str(e)}")
            raise
          
          
    # async def update_section_reconfig_flags(self, project_id):
    #     """
    #     Update section-specific reconfiguration flags based on affected nodes.
    #     These flags will be used to enable/disable the auto-configure buttons in UI.
    #     """
    #     try:
    #         # Get the reconfig tracker document
    #         reconfig_doc = await self.mongo_db.get_one(
    #             {"project_id": project_id},
    #             self.reconfig_tracker_collection
    #         )
            
    #         if not reconfig_doc or "affected_nodes" not in reconfig_doc:
    #             return

    #         # Initialize section flags
    #         project_reconfig = False
    #         requirement_reconfig = False
    #         architecture_reconfig = False
            
    #         # Define node types for each section
    #         project_section_types = ["Project", "WorkItem", "WorkItemRoot", "Architecture", 
    #                                 "ArchitectureRoot", "SystemContext", "Container", 
    #                                 "Component", "Design", "Interface"]
                                    
    #         requirement_section_types = ["RequirementRoot", "Requirement", "Epic", 
    #                                     "UserStory", "Task"]
                                        
    #         architecture_section_types = ["Architecture", "ArchitectureRoot", "ArchitecturalRequirement",
    #                                     "SystemContext", "Container", "Component", "Design", 
    #                                     "Interface", "ClassDiagram", "Algorithm", "Sequence", 
    #                                     "StateDiagram", "UnitTest", "IntegrationTest", "FunctionalRequirement",
    #                                     "NonFunctionalRequirement"]
            
    #         # Check each affected node
    #         for node in reconfig_doc["affected_nodes"]:
    #             if node.get("reconfig_needed", False) is True:
    #                 node_type = node.get("node_type", "")
                    
    #                 # Update section flags
    #                 if node_type in project_section_types:
    #                     project_reconfig = True
                        
    #                 if node_type in requirement_section_types:
    #                     requirement_reconfig = True
                        
    #                 if node_type in architecture_section_types:
    #                     architecture_reconfig = True
            
    #         # Update the document with the section flags
    #         reconfig_doc["section_reconfig_flags"] = {
    #             "project_reconfig": project_reconfig,
    #             "requirement_reconfig": requirement_reconfig,
    #             "architecture_reconfig": architecture_reconfig
    #         }
            
    #         # Remove the propagation_path if requested
    #         if "propagation_path" in reconfig_doc:
    #             del reconfig_doc["propagation_path"]
            
    #         # Update MongoDB
    #         await self.mongo_db.update_one(
    #             {"project_id": project_id},
    #             reconfig_doc,
    #             upsert=True,
    #             db=self.reconfig_tracker_collection
    #         )
            
    #         return {
    #             "project_reconfig": project_reconfig,
    #             "requirement_reconfig": requirement_reconfig,
    #             "architecture_reconfig": architecture_reconfig
    #         }
            
    #     except Exception as e:
    #         self.logger.error(f"Error updating section reconfig flags: {str(e)}")
    #         raise
  

    # 
    async def save_node_info(self, node_data: Dict) -> None:
        """
        Non-blocking version of save_node_info that returns immediately
        after essential operations and handles the rest in background.
        
        Args:
            node_data: Node data to save
        """
        try:
            node_id = node_data['node_id']
            print(f"Saving node info for node_id: {node_id},{node_data}")
            
            # First check if this node is a Discussion type - if so, skip version tracking
            node_info = await self.db.get_node_by_id(node_id)
            if node_info and 'Discussion' in node_info['labels']:
                return  # Skip version tracking for Discussion nodes
            
            # Determine node type for later use
            node_type = None
            if node_info:
                if 'properties' in node_info and 'Type' in node_info['properties']:
                    node_type = node_info['properties']['Type']
                elif 'labels' in node_info:
                    for label in node_info['labels']:
                        if label != 'Discussion':
                            node_type = label
                            break
                
                if not node_type:
                    node_type = "Unknown"
            
            # This is the essential operation we want to complete immediately
            # so the user doesn't have to wait - update the node properties
            await self.db.update_node_by_id(
                node_id=node_id,
                properties=node_data['properties']
            )
            
            # Launch the rest of the processing as a background task
            self.background_manager.create_task(
                self._save_node_info_bg(node_data, node_id, node_type, node_info),
                f"save_node_info_{node_id}"
            )
            
        except Exception as e:
            self.logger.error(f"Error in save_node_info: {str(e)}")
            raise

    async def _save_node_info_bg(self, node_data: Dict, node_id: int, node_type: str, node_info: Dict) -> None:
        """
        Background task to complete the node info saving process.
        This happens after the main save_node_info method returns.
        """
        try:
            # Determine root_node_id based on node type
            if node_info and 'Project' in node_info['labels']:
                # This is already a Project node
                root_node_id = node_id
            else:
                # Not a Project, find the root Project node
                result = await self.db.get_absolute_root_node(node_id)
                root_node_id = result['id']
            
            # Get or create reconfig doc
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": root_node_id},
                self.reconfig_tracker_collection
            )
            
            if not reconfig_doc:
                # Create initial reconfig tracker
                reconfig_doc = {
                    "project_id": root_node_id,
                    "current_reconfig": 0,
                    "last_successful": 0,
                    "status": "in_progress",
                    "started_at": datetime.now().isoformat(),
                    "affected_nodes": []
                }
                await self.mongo_db.insert(reconfig_doc, self.reconfig_tracker_collection)
            
            # Check if this node needs reconfiguration
            is_needing_reconfig = False
            affected_node_index = None
            
            if "affected_nodes" in reconfig_doc:
                for i, affected_node in enumerate(reconfig_doc["affected_nodes"]):
                    if affected_node["node_id"] == node_id:
                        affected_node_index = i
                        if affected_node.get("reconfig_needed", True) is True:
                            is_needing_reconfig = True
                        break
            
            # Check if this is a reconfiguration
            is_reconfiguration = False
            is_initial_config = False
            if 'user_interaction' in node_data:
                action = node_data['user_interaction'].get('action', '')
                if action == 'reconfig' or action == 'reconfig_from_parent':
                    is_reconfiguration = True
                elif action == 'initial_config' or action == 'initial_reconfig':
                    is_initial_config = True
            
            # Get proper element ID from Neo4j
            element_id = await self._get_element_id(node_id)
            
            # Get CDC changes after update
            changes = await self.get_changes_since_timestamp(element_id)
            latest_change = changes[-1] if changes else None
            latest_cdc_id = latest_change['id'] if latest_change else None
            
            # Create version document
            version_doc = {
                "node_id": node_id,
                "element_id": element_id,
                "version_number": reconfig_doc["current_reconfig"],
                "cdc_event_id": latest_cdc_id,
                "timestamp": datetime.now().isoformat(),
                "background_info": node_data.get('background_info', {}),
                "user_interaction": node_data.get('user_interaction', {}),
                "metadata": {
                    "user": node_data.get('user'),
                    "change_reason": node_data.get('change_reason', '')
                }
            }

            await self._log_change_context(node_id, version_doc)
            
            # Handle initial configuration
            if is_initial_config:
                if affected_node_index is None:
                    # Add with initial version info
                    affected_node = {
                        "node_id": node_id,
                        "node_type": node_type,
                        "parent_id": await self.get_parent_node_id(node_id),
                        "reconfig_needed": False,
                        "current_version": 1,
                        "last_successful": 1,
                        "current_version_cdc_id": latest_cdc_id,
                        "last_successful_cdc_id": latest_cdc_id,
                        "created_at": datetime.now().isoformat()
                    }
                    
                    if "affected_nodes" not in reconfig_doc:
                        reconfig_doc["affected_nodes"] = []
                        
                    reconfig_doc["affected_nodes"].append(affected_node)
                    
                    # Update MongoDB
                    filter_query = {"project_id": root_node_id}
                    await self.mongo_db.update_one(
                        filter_query,
                        reconfig_doc,
                        upsert=True,
                        db=self.reconfig_tracker_collection
                    )
                    
                    # Update version tracker collection
                    await self._update_version_tracker(
                        root_node_id, 
                        node_id, 
                        node_type,
                        1,
                        latest_cdc_id
                    )
            
            # Handle reconfiguration
            elif affected_node_index is not None and is_reconfiguration:
                # Get the existing versions
                current_version = reconfig_doc["affected_nodes"][affected_node_index].get("current_version", 0)
                last_successful = reconfig_doc["affected_nodes"][affected_node_index].get("last_successful", 0)
                
                # Increment current_version
                new_current_version = current_version + 1
                reconfig_doc["affected_nodes"][affected_node_index]["current_version"] = new_current_version
                reconfig_doc["affected_nodes"][affected_node_index]["current_version_cdc_id"] = latest_cdc_id
                
                # Update last_successful ONLY if it was 0 (initial config)
                if last_successful == 0:
                    reconfig_doc["affected_nodes"][affected_node_index]["last_successful"] = new_current_version
                    reconfig_doc["affected_nodes"][affected_node_index]["last_successful_cdc_id"] = latest_cdc_id
                

                reconfig_doc["affected_nodes"][affected_node_index]["reconfig_needed"] = False
                reconfig_doc["affected_nodes"][affected_node_index]["reconfigured_at"] = datetime.now().isoformat()
                    
                # Update MongoDB document
                filter_query = {"project_id": root_node_id}
                await self.mongo_db.update_one(
                    filter_query,
                    reconfig_doc,
                    upsert=True,
                    db=self.reconfig_tracker_collection
                )
                
                # Update version tracker collection
                await self._update_version_tracker(
                    root_node_id, 
                    node_id, 
                    node_type,
                    new_current_version,
                    latest_cdc_id
                )
            
            # Clear reconfig flag if needed
            elif is_reconfiguration and is_needing_reconfig and affected_node_index is None:
                await self.clear_reconfig_flag(node_id, latest_cdc_id)
            
            # Flag downstream nodes when a node is updated except during initial config
            if (is_reconfiguration or reconfig_doc["last_successful"] > 0) and not is_initial_config:
                reason = node_data['user_interaction'].get('reason', 'Parent node was modified') 
                await self.flag_downstream_nodes(node_id, reason)

        except Exception as e:
            self.logger.error(f"Error in background save_node_info for node {node_id}: {str(e)}")
    async def get_affected_nodes_details(self, project_id: int) -> Dict[str, List[Dict[str, Union[str, bool, int]]]]:
        try:
            # Retrieve the reconfig document for the given project ID
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.reconfig_tracker_collection
            )

            
            # If no document found, return an empty result
            if not reconfig_doc or 'affected_nodes' not in reconfig_doc:
                return {}
            
            # Organize nodes by their node type
            nodes_by_type = {}
            
            for node in reconfig_doc.get('affected_nodes', []):
                # Skip nodes that are explicitly marked as deleted
                is_deleted = node.get('is_deleted')
                
                # Convert is_deleted to boolean
                if isinstance(is_deleted, str):
                    is_deleted = is_deleted.lower() == 'true'
                else:
                    is_deleted = bool(is_deleted) if is_deleted is not None else False
                
                # Skip if node is deleted
                if is_deleted:
                    continue
                
                node_type = node.get('node_type', 'Unknown')
                reconfig_needed = node.get('reconfig_needed')
               
                # Explicit conversion and checking for reconfig_needed
                if isinstance(reconfig_needed, str):
                    reconfig_needed = reconfig_needed.lower() == 'true'
                elif reconfig_needed is None:
                    # Default to False if not specified
                    reconfig_needed = False
                
                # Explicit boolean conversion
                reconfig_needed = bool(reconfig_needed)
                
                # Initialize node type list if not exists
                if node_type not in nodes_by_type:
                    nodes_by_type[node_type] = []
                
                # Collect node details
                node_details = {
                    "node_id": node.get('node_id'),
                    "node_type": node_type,
                    "reconfig_needed": reconfig_needed,
                    "current_version": node.get('current_version', 0),
                    "last_successful": node.get('last_successful', 0),
                    "is_deleted": is_deleted  # Explicitly include is_deleted
                }
                
                # Optional: Add logging for nodes not marked for reconfig
                if not reconfig_needed:
                    print(f"Node {node_details['node_id']} not marked for reconfig")
                
                nodes_by_type[node_type].append(node_details)
            
            return nodes_by_type
        
        except Exception as e:
            # Error handling (optional, depending on your error handling strategy)
            print(f"Error in get_affected_nodes_details: {e}")
            return {}

    async def get_section_reconfig_flags(self, project_id: int) -> Optional[Dict[str, bool]]:
       
        try:
            # Retrieve the reconfig document for the given project ID
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.reconfig_tracker_collection
            )
            
            # If no document found, return None
            if not reconfig_doc or 'section_reconfig_flags' not in reconfig_doc:
                self.logger.warning(f"No reconfig flags found for project {project_id}")
                return None
            
            # Extract section reconfig flags
            section_flags = reconfig_doc.get('section_reconfig_flags', {})
            
            return {
                'project_reconfig': section_flags.get('project_reconfig', False),
                'requirement_reconfig': section_flags.get('requirement_reconfig', False),
                'architecture_reconfig': section_flags.get('architecture_reconfig', False)
            }
        
        except Exception as e:
            self.logger.error(f"Error retrieving section reconfig flags for project {project_id}: {str(e)}")
            raise
    
    # async def save_node_info(self, node_data: Dict) -> None:
    #     """Save node info and track CDC events with updated versioning logic"""
    #     try:
    #         node_id = node_data['node_id']
    #         print(f"Saving node info for node_id: {node_id}")
            
    #         # First check if this node is a Project type
    #         node_info = await self.db.get_node_by_id(node_id)
            
    #         # Determine root_node_id based on node type
    #         if node_info and 'Project' in node_info['labels']:
    #             # This is already a Project node
    #             root_node_id = node_id
    #         else:
    #             # Not a Project, find the root Project node
    #             result = await self.db.get_absolute_root_node(node_id)
    #             root_node_id = result['id']
            
    #         # Get or create reconfig doc
    #         reconfig_doc = await self.mongo_db.get_one(
    #             {"project_id": root_node_id},
    #             self.reconfig_tracker_collection
    #         )
            
    #         if not reconfig_doc:
    #             # Create initial reconfig tracker with version 0 (changed from 1)
    #             reconfig_doc = {
    #                 "project_id": root_node_id,
    #                 "current_reconfig": 0,  # Start with 0 instead of 1
    #                 "last_successful": 0,
    #                 "status": "in_progress",
    #                 "started_at": datetime.now().isoformat(),
    #                 "affected_nodes": []
    #             }
    #             await self.mongo_db.insert(reconfig_doc, self.reconfig_tracker_collection)
            
    #         # Check if this node needs reconfiguration
    #         is_needing_reconfig = False
    #         affected_node_index = None
            
    #         if "affected_nodes" in reconfig_doc:
    #             for i, affected_node in enumerate(reconfig_doc["affected_nodes"]):
    #                 if affected_node["node_id"] == node_id:
    #                     affected_node_index = i
    #                     if affected_node.get("reconfig_needed", True) is True:
    #                         is_needing_reconfig = True
    #                         print(f"Node {node_id} found in affected_nodes with reconfig_needed=True")
    #                     break
            
    #         # Check if this is an initial configuration or reconfiguration
    #         is_reconfiguration = False
    #         if 'user_interaction' in node_data:
    #             action = node_data['user_interaction'].get('action', '')
    #             if action == 'reconfig' or action == 'reconfig_from_parent':
    #                 is_reconfiguration = True
    #                 print(f"Node {node_id} is being reconfigured (action: {action})")
            
    #         # Get proper element ID from Neo4j
    #         element_id = await self._get_element_id(node_id)
            
    #         # Update node state
    #         await self.db.update_node_by_id(
    #             node_id=node_id,
    #             properties=node_data['properties']
    #         )

    #         # Get CDC changes after update
    #         changes = await self.get_changes_since_timestamp(element_id)
    #         latest_change = changes[-1] if changes else None
    #         latest_cdc_id = latest_change['id'] if latest_change else None
            
    #         # Create version document
    #         version_doc = {
    #             "node_id": node_id,
    #             "element_id": element_id,
    #             "version_number": reconfig_doc["current_reconfig"],
    #             "cdc_event_id": latest_cdc_id,
    #             "timestamp": datetime.now().isoformat(),
    #             "background_info": node_data.get('background_info', {}),
    #             "user_interaction": node_data.get('user_interaction', {}),
    #             "metadata": {
    #                 "user": node_data.get('user'),
    #                 "change_reason": node_data.get('change_reason', '')
    #             }
    #         }

    #         await self._log_change_context(node_id, version_doc)
            
    #         # If the node is in affected_nodes, update its version information
    #         if affected_node_index is not None and is_reconfiguration:
    #             # Get the existing versions
    #             current_version = reconfig_doc["affected_nodes"][affected_node_index].get("current_version", 0)
    #             last_successful = reconfig_doc["affected_nodes"][affected_node_index].get("last_successful", 0)
                
    #             # Increment current_version
    #             new_current_version = current_version + 1
    #             reconfig_doc["affected_nodes"][affected_node_index]["current_version"] = new_current_version
    #             reconfig_doc["affected_nodes"][affected_node_index]["current_version_cdc_id"] = latest_cdc_id
                
    #             # Update last_successful if this is the first save or a successful reconfiguration
    #             if last_successful == 0:
    #                 # First save - set last_successful to match current_version (should be 1)
    #                 reconfig_doc["affected_nodes"][affected_node_index]["last_successful"] = new_current_version
    #                 reconfig_doc["affected_nodes"][affected_node_index]["last_successful_cdc_id"] = latest_cdc_id
    #             else:
    #                 # Not first save - keep last_successful as is
    #                 # We've already set current_version above
    #                 pass
                    
    #             # Also mark reconfig_needed as false
    #             reconfig_doc["affected_nodes"][affected_node_index]["reconfig_needed"] = False
    #             reconfig_doc["affected_nodes"][affected_node_index]["reconfigured_at"] = datetime.now().isoformat()
                    
    #             # Update MongoDB document with version changes
    #             filter_query = {"project_id": root_node_id}
    #             await self.mongo_db.update_one(
    #                 filter_query,
    #                 reconfig_doc,
    #                 upsert=True,
    #                 db=self.reconfig_tracker_collection
    #             )
    #             print(f"Updated versions for node {node_id}: current_version={new_current_version}, last_successful={reconfig_doc['affected_nodes'][affected_node_index]['last_successful']}")
                
    #             # Update version tracker collection
    #             await self._update_version_tracker(
    #                 root_node_id, 
    #                 node_id, 
    #                 node_info['labels'][0] if node_info and 'labels' in node_info else "Unknown",
    #                 new_current_version,
    #                 latest_cdc_id
    #             )
            
    #         # If the node is not in affected_nodes but needs reconfiguration, call clear_reconfig_flag
    #         elif is_reconfiguration and is_needing_reconfig and affected_node_index is None:
    #             print(f"Clearing reconfig flag for node {node_id}")
    #             await self.clear_reconfig_flag(node_id, latest_cdc_id)
            
    #         # Flag downstream nodes when a node is updated except during initial config
    #         if is_reconfiguration or reconfig_doc["last_successful"] > 0:
    #             reason = node_data['user_interaction'].get('reason', 'Parent node was modified') 
    #             print(f"Flagging downstream nodes for node {node_id} due to reconfiguration")
    #             await self.flag_downstream_nodes(node_id, reason)
    #             print(f"Completed flagging downstream nodes for {node_id}")

    #     except Exception as e:
    #         self.logger.error(f"Error saving node info: {str(e)}")
    #         raise
  
    async def get_node_info_by_version(self, node_id: str, node_type: str, version: int = None) -> Optional[Dict]:
        try:
            # Get element ID first
            element_id = await self._get_element_id(node_id)
            
            # If no version specified, get current reconfig version
            if version is None:
                reconfig_doc = await self.mongo_db.get_one(
                    {"project_id": self.project_id},
                    self.mongo_db.db["reconfig_tracker"]
                )
                version = reconfig_doc["current_reconfig"]

            # Get version document
            version_doc = await self.mongo_db.get_one(
                {
                    "node_id": node_id,
                    "version_number": version
                },
                self.mongo_db.db[self.version_collection]
            )
            
            if not version_doc:
                return None

            # Get CDC state if we have event ID
            if version_doc.get('cdc_event_id'):
                node_state = await self._get_node_state_from_cdc(
                    element_id, 
                    version_doc['cdc_event_id']
                )
                if node_type and node_type in data_model["model"]:
                    node_state['ui_metadata'] = data_model["model"][node_type]["ui_metadata"]
            
                return node_state

            return None

        except Exception as e:
            self.logger.error(f"Error getting version info: {str(e)}")
            raise

    
    
    async def get_node_version_history(self, node_id: int) -> Dict:
        """Get the complete version history for a node"""
        try:
            # Get the project ID
            project_node = await self.db.get_absolute_root_node(node_id)
            project_id = project_node['id'] if project_node else None
            
            if not project_id:
                return {"error": "Could not determine project ID for node"}
            
            # Get version tracker document
            tracker_doc = await self.mongo_db.get_one(
                {
                    "project_id": project_id,
                    "node_id": node_id
                },
                self.mongo_db.db[self.version_tracker_collection]
            )
            
            if not tracker_doc:
                return {
                    "node_id": node_id,
                    "versions": {},
                    "current_version": 0,
                    "last_successful": 0
                }
            
            # Get current status from reconfig tracker
            node_status = await self.get_node_reconfig_status(node_id)
            
            return {
                "node_id": node_id,
                "node_type": tracker_doc.get("node_type", "Unknown"),
                "versions": tracker_doc.get("versions", {}),
                "current_version": node_status.get("current_version", 0) if node_status else 0,
                "last_successful": node_status.get("last_successful", 0) if node_status else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting node version history: {str(e)}")
            return {"error": str(e)}
        
    async def _get_earliest_cdc_id(self) -> Optional[str]:
        """Get earliest CDC event ID"""
        try:
            result = await self.db.async_run("CALL db.cdc.earliest()")
            records = result.data()
            
            if not records:
                return None
            
            return records[0].get('id')

        except Exception as e:
            self.logger.error(f"Error getting earliest CDC ID: {str(e)}")
            raise
        

    async def _get_node_state_from_cdc(self, element_id: str, cdc_event_id: str) -> Dict:
        """Get node state from CDC event"""
        try:
            query = """
            CALL db.cdc.query($cdc_event_id, [{
            select: "n",  
            elementId: $element_id
            }])
            """
            
            result = await self.db.async_run(
                query,
                element_id=element_id,
                cdc_event_id=cdc_event_id
            )
            
            # Get the first record from the result
            record = result.data()[0]
            
            # Extract the "after" state properties from the state
            if 'state' in record['event'] :
                # Return the properties from the "after" state
                
                return record['event']['state']['after']
            else:
                # If structure is not as expected, log warning and return empty dict
                self.logger.warning(f"Unexpected CDC event structure for event {cdc_event_id}")
                return {}
                
        except Exception as e:
            self.logger.error(f"Error getting node state from CDC: {str(e)}")
            raise
        
    async def _get_next_version_number(self, node_id: str) -> int:
        """Get next sequential version number"""
        latest_version = await self.mongo_db.get_one(
            {"node_id": node_id},
            self.mongo_db.db[self.version_collection],
            sort=[('version_number', -1)]
        )
        return (latest_version['version_number'] + 1) if latest_version else 1

    async def _log_change_context(self, node_id: str, version_doc: Dict) -> None:
        """Store version information in MongoDB"""
        try:
            await self.mongo_db.insert(
                version_doc,
                self.mongo_db.db[self.version_collection]
            )
            
            change_record = {
                "node_id": node_id,
                "timestamp": datetime.utcnow(),
                "version_number": version_doc['version_number'],
                "cdc_event_id": version_doc['cdc_event_id'],
                "background_info": version_doc['background_info'],
                "user_interaction": version_doc['user_interaction']
            }
            
            await self.mongo_db.insert(
                change_record,
                self.mongo_db.db[self.changes_collection]
            )

        except Exception as e:
            self.logger.error(f"Error logging context: {str(e)}")
            raise


    async def _get_context(self, node_id: str) -> Dict:
        """
        Retrieve latest context information for a node.
        Used by Discussion classes to get background info and user interactions.
        """
        try:
            # Get element ID for reference
            element_id = await self._get_element_id(node_id)
            
            # Get latest version document which contains context info
            version_doc = await self.mongo_db.get_one(
                {"node_id": node_id},
                self.mongo_db.db[self.version_collection],
                sort=[('version_number', -1)]
            )

            if not version_doc:
                return {
                    "background_info": {},
                    "user_interaction": {}
                }

            return {
                "background_info": version_doc.get('background_info', {}),
                "user_interaction": version_doc.get('user_interaction', {}),
                "element_id": element_id,  # Include element ID for CDC operations
                "version_number": version_doc.get('version_number'),
                "timestamp": version_doc.get('timestamp')
            }

        except Exception as e:
            self.logger.error(f"Error getting context for node {node_id}: {str(e)}")
            # Return empty context rather than raising exception
            return {
                "background_info": {},
                "user_interaction": {}
            }
    
    async def get_parent_node_id(self, node_id: int) -> Optional[int]:
        """Get the immediate parent node ID for a given node."""
        try:
            query = """
            MATCH (parent)-[:HAS_CHILD]->(child)
            WHERE ID(child) = $node_id
            RETURN ID(parent) AS parent_id
            """
            result = await self.db.async_run(query, node_id=node_id)
            data = result.data()
            
            if data and len(data) > 0:
                return data[0]['parent_id']
            return None
        except Exception as e:
            self.logger.error(f"Error getting parent node ID: {str(e)}")
            return None
        
    
    async def flag_downstream_nodes(self, node_id: int, reason: str) -> List[int]:
        """
        Flags only the immediate next level of nodes in the hierarchy based on the project structure.
        Implements dependency checks before flagging next level nodes.
        Strictly follows the hierarchy mapping.
        
        Args:
            node_id: ID of the modified node
            reason: Reason for reconfiguration
                
        Returns:
            List of affected node IDs
        """
        start_time = datetime.now()
        
        # Get the node details to determine its type
        current_node = await self.db.get_node_by_id(node_id)
        if not current_node or 'labels' not in current_node:
            self.logger.error(f"Node {node_id} not found or invalid")
            return []
        
        # Get the node type from labels
        node_labels = current_node['labels']
        node_type = None
        for label in node_labels:
            if label != 'Discussion':
                node_type = label
                break
        
        if not node_type:
            self.logger.warning(f"Could not determine type for node {node_id}")
            return []
        
        # Get the root project_id
        project_node = await self.db.get_absolute_root_node(node_id)
        project_id = project_node['id'] if project_node and project_node.get('id') is not None else node_id
        print(f"🚩 Flagging specific downstream nodes for project_id: {project_id}, node_id: {node_id}, type: {node_type}")
        
        # Define the hierarchy mapping to know what to flag next - STRICTLY FOLLOW THIS
        hierarchy_mapping = {
            "Project": ["RequirementRoot"],
            "RequirementRoot": ["Epic"],
            "Epic": ["ArchitecturalRequirement"],  # Special case that considers dependencies
            "UserStory": [],  # End of requirements branch
            "Requirement":["SystemContext"],
            "ArchitecturalRequirement": ["SystemContext"],
            "SystemContext": ["Container"],
            "Container": ["Component"],  # Special case that considers dependencies
            "Component": ["Design"],  # Special case that considers dependencies
            "Design": ["Interface"],  # Special case that considers dependencies
            "Interface": []  # End of architecture branch
        }
        
        # Determine which node types to flag based on the current node type
        target_types = []
        
        if node_type in hierarchy_mapping:
            target_types = hierarchy_mapping[node_type]
        
        if not target_types:
            print(f"No downstream nodes to flag for node type: {node_type}")
            await self._update_node_version(node_id, project_id, reason)
            return []
        
        print(f"Looking for child node types: {target_types}")
        
        # Find direct child nodes of the target types
        affected_nodes = []
        for target_type in target_types:
            # Special case for Epic -> ArchitecturalRequirement
            if node_type == "Epic" and target_type == "ArchitecturalRequirement":
                # Find ArchitecturalRequirement under ArchitectureRoot, not under Epic
                project_query = """
                MATCH (p:Project)-[:HAS_CHILD]->(arr:ArchitectureRoot)-[:HAS_CHILD]->(ar:ArchitecturalRequirement)
                WHERE ID(p) = $project_id
                RETURN ID(ar) AS id, LABELS(ar) AS labels, properties(ar) AS properties
                """
                result = await self.db.async_run(project_query, project_id=project_id)
                affected_nodes.extend(result.data())

            elif (node_type == "ArchitecturalRequirement" or node_type == "Requirement") and target_type == "SystemContext":
                # Find SystemContext nodes under ArchitectureRoot
                system_context_query = """
                MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
                WHERE ID(p) = $project_id
                RETURN ID(sc) AS id, LABELS(sc) AS labels, properties(sc) AS properties
                """
                result = await self.db.async_run(system_context_query, project_id=project_id)
                affected_nodes.extend(result.data())

            elif (node_type == "SystemContext") and target_type == "Container":
                # Find SystemContext nodes under ArchitectureRoot
                system_context_query = """
                MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD]->(c:Container)
                WHERE ID(p) = $project_id
                RETURN ID(c) AS id, LABELS(c) AS labels, properties(c) AS properties
                """
                result = await self.db.async_run(system_context_query, project_id=project_id)
                affected_nodes.extend(result.data())

            elif (node_type == "Container") and target_type == "Component":
                # Find SystemContext nodes under ArchitectureRoot
                system_context_query = """
                MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)-[:HAS_CHILD]->(c:Container)-[:HAS_CHILD]->(cr:Component)
                WHERE ID(p) = $project_id
                RETURN ID(cr) AS id, LABELS(cr) AS labels, properties(cr) AS properties
                """
                result = await self.db.async_run(system_context_query, project_id=project_id)
                affected_nodes.extend(result.data())
            
            else:
                # Normal case - find direct children of the specified type
                try:
                    child_nodes = await self.db.get_child_nodes(node_id, target_type)
                    for child in child_nodes:
                        if 'id' in child and child['id'] != node_id:  # Skip self
                            affected_nodes.append(child)
                except Exception as e:
                    print(f"Error getting child nodes of type {target_type}: {str(e)}")
        
        # Extract node IDs for return
        affected_node_ids = [node['id'] for node in affected_nodes]
        
        # Create background task for MongoDB operations
        self.background_manager.create_task(
            self._process_downstream_nodes_bg(affected_nodes, node_id, project_id, reason),
            f"flag_downstream_{node_id}"
        )
        print(f"⚡ Starting background task to process {len(affected_node_ids)} affected nodes")
        
        # Return immediately while background task completes
        return affected_node_ids

    async def _process_downstream_nodes_bg(self, downstream_nodes: List[Dict], parent_node_id: int, project_id: int, reason: str):
        """
        Background task to process only specific downstream nodes and update MongoDB.
        This version strictly respects the hierarchical structure of the project.
        """
        try:
            start_time = datetime.now()
            print(f"🔄 Background task: Processing {len(downstream_nodes)} specific downstream nodes")
            
            # Get current node info for context
            current_node = await self.db.get_node_by_id(parent_node_id)
            
            # Get node type from labels
            current_node_type = None
            if current_node:
                if 'properties' in current_node and 'Type' in current_node['properties']:
                    current_node_type = current_node['properties']['Type']
                elif 'labels' in current_node:
                    for label in current_node['labels']:
                        if label != 'Discussion':
                            current_node_type = label
                            break
            
            if not current_node_type:
                current_node_type = "Unknown"
                
            current_node_title = current_node['properties'].get('Title', 'Unknown') if current_node else "Unknown"
            print(f"📋 Modified node: {current_node_type} '{current_node_title}'")
            
            # Get the latest reconfig tracker document
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.reconfig_tracker_collection
            )
            
            # Initialize reconfig_doc if it doesn't exist
            if not reconfig_doc:
                print(f"🆕 Creating new reconfig tracker document for project {project_id}")
                reconfig_doc = {
                    "project_id": project_id,
                    "current_reconfig": 0,
                    "last_successful": 0,
                    "status": "in_progress",
                    "started_at": datetime.now().isoformat(),
                    "affected_nodes": []
                }
            
            # Ensure affected_nodes field exists
            if "affected_nodes" not in reconfig_doc:
                reconfig_doc["affected_nodes"] = []
            
            # Create dictionary of existing nodes for quick lookup
            existing_node_map = {node["node_id"]: node for node in reconfig_doc["affected_nodes"]}
            print(f"🗂️ Found {len(existing_node_map)} existing nodes in MongoDB")
            
            # Process all downstream nodes in memory first - no DB operations yet
            current_time = datetime.now().isoformat()
            
            # Get all element IDs in a single batch for nodes we need to add
            nodes_needing_element_id = [
                node['id'] for node in downstream_nodes 
                if node['id'] not in existing_node_map
            ]
            print(f"🆔 Getting element IDs for {len(nodes_needing_element_id)} new nodes")
            
            element_id_map = {}
            if nodes_needing_element_id:
                # Create a batched query to get multiple element IDs at once
                element_id_query = """
                UNWIND $nodeIds AS nodeId
                MATCH (n) 
                WHERE ID(n) = nodeId 
                RETURN ID(n) as node_id, elementId(n) as element_id
                """
                
                element_id_results = await self.db.async_run(
                    element_id_query, 
                    nodeIds=nodes_needing_element_id
                )
                
                for record in element_id_results.data():
                    element_id_map[record['node_id']] = record['element_id']
                    print(f"✅ Retrieved {len(element_id_map)} element IDs from Neo4j")
            
            # Collect CDC IDs for nodes with element IDs
            print(f"📊 Getting CDC IDs for nodes with element IDs")
            cdc_id_map = {}
            for node_id, element_id in element_id_map.items():
                changes = await self.get_changes_since_timestamp(element_id)
                latest_change = changes[-1] if changes else None
                if latest_change:
                    cdc_id_map[node_id] = latest_change['id']
            
            # Now process all downstream nodes with the element IDs we have
            print(f"✅ Retrieved {len(cdc_id_map)} CDC IDs")
            for node in downstream_nodes:
                downstream_id = node['id']
                
                # Skip Discussion nodes
                if 'labels' in node and 'Discussion' in node['labels']:
                    continue
                
                # Determine downstream node type
                downstream_type = None
                if 'properties' in node and 'Type' in node['properties']:
                    downstream_type = node['properties']['Type']
                elif 'node_type' in node:
                    downstream_type = node['node_type']
                elif 'labels' in node:
                    for label in node['labels']:
                        if label != 'Discussion':
                            downstream_type = label
                            break
                
                if not downstream_type:
                    downstream_type = "Unknown"
                
                # Get parent ID
                parent_node = await self.db.get_parent_node(downstream_id)
                parent_id = parent_node['id'] if parent_node else None
                
                # Mark the node in Neo4j as needing reconfiguration
                await self.db.update_node_by_id(
                    node_id=downstream_id,
                    properties={"reconfig_needed": True}
                )
                
                # For nodes not yet in MongoDB, initialize with proper values
                if downstream_id not in existing_node_map:
                    # Get CDC ID if available
                    cdc_id = cdc_id_map.get(downstream_id)
                    
                    # Create new node entry - initialize with reconfig_needed=True
                    new_node = {
                        "node_id": downstream_id,
                        "node_type": downstream_type,
                        "parent_id": parent_id,
                        "reconfig_needed": True,
                        "reconfig_reason": f"{current_node_type} '{current_node_title}' was modified: {reason}",
                        "flagged_at": current_time,
                        "current_version": 1,  # All nodes start with version 1
                        "last_successful": 1,  # All nodes start with last_successful=1
                        "created_at": current_time,
                        "current_version_cdc_id": cdc_id,
                        "last_successful_cdc_id": cdc_id
                    }
                    
                    # Add to our map
                    existing_node_map[downstream_id] = new_node
                else:
                    # Node already exists in MongoDB - update it to need reconfiguration
                    existing_node_map[downstream_id]["reconfig_needed"] = True
                    existing_node_map[downstream_id]["reconfig_reason"] = f"{current_node_type} '{current_node_title}' was modified: {reason}"
                    existing_node_map[downstream_id]["flagged_at"] = current_time
                    
            print(f"📊 Node processing summary:")
            
            # Now reconstruct the affected_nodes array from our map
            updated_affected_nodes = list(existing_node_map.values())
            
            # Update reconfig_doc with new affected_nodes
            reconfig_doc["affected_nodes"] = updated_affected_nodes
            print(f"💾 Preparing to update MongoDB with {len(updated_affected_nodes)} total nodes")
            
            # Update stats
            reconfig_doc["reconfiguration_progress"] = {
                "total_affected": len(updated_affected_nodes),
                "completed": sum(1 for node in updated_affected_nodes if node.get("reconfig_needed", True) is False),
                "remaining": sum(1 for node in updated_affected_nodes if node.get("reconfig_needed", True) is True)
            }
            
            # Now do the MongoDB update - this could take longer but happens in background
            mongo_update_start = datetime.now()
            
            # Perform single update operation
            await self.mongo_db.update_one(
                {"project_id": project_id},
                reconfig_doc,
                upsert=True,
                db=self.reconfig_tracker_collection
            )
            
            # Process version tracker updates for new nodes
            version_tracker_tasks = []
            for node_id, node in existing_node_map.items():
                # Only process nodes that aren't marked for reconfig
                if not node.get("reconfig_needed", True) and node.get("current_version", 0) > 0:
                    cdc_id = node.get("current_version_cdc_id")
                    if cdc_id:
                        version_tracker_tasks.append(
                            self._update_version_tracker(
                                project_id,
                                node_id,
                                node.get("node_type", "Unknown"),
                                node.get("current_version", 1),
                                cdc_id
                            )
                        )
            print(f"📝 Updating version tracker for {len(version_tracker_tasks)} nodes")
            # Execute version tracker updates in parallel
            if version_tracker_tasks:
                await asyncio.gather(*version_tracker_tasks)
            
            # Update section flags
            await self.update_section_reconfig_flags(project_id)
            
            mongo_update_end = datetime.now()
            
            total_time = mongo_update_end - start_time
            print(f"Background task: Updated reconfiguration tracker for project {project_id}")
            print(f"Background task: Final affected node count: {len(updated_affected_nodes)}")
            print(f"Background task: MongoDB update took {(mongo_update_end - mongo_update_start).total_seconds()}s")
            print(f"Background task: Total execution time: {total_time.total_seconds()}s")
            
        except Exception as e:
            print(f"Error in background task to process downstream nodes: {str(e)}")
            self.logger.error(f"Error in background task to process downstream nodes: {str(e)}")




    async def _update_node_version(self, node_id: int, project_id: int, reason: str):
        """
        Helper method to update a node's version and CDC info even when not flagging downstream nodes.
        Also ensures proper propagation of reconfig flags based on the hierarchy mapping.
        """
        try:
            # Get reconfig doc
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.mongo_db.db["reconfig_tracker"]
            )
            
            if not reconfig_doc:
                print(f"No reconfig doc found for project {project_id}")
                return
                
            # Find node in affected_nodes
            node_index = None
            current_node_type = None
            for i, node in enumerate(reconfig_doc.get("affected_nodes", [])):
                if node["node_id"] == node_id:
                    node_index = i
                    current_node_type = node.get("node_type")
                    break
                    
            if node_index is None:
                print(f"Node {node_id} not found in affected_nodes")
                return
                
            # Get element ID and CDC info
            element_id = await self._get_element_id(node_id)
            changes = await self.get_changes_since_timestamp(element_id)
            latest_change = changes[-1] if changes else None
            cdc_id = latest_change['id'] if latest_change else None
            
            if not cdc_id:
                print(f"No CDC ID found for node {node_id}")
                return
                
            # Update node version info
            current_version = reconfig_doc["affected_nodes"][node_index].get("current_version", 1) + 1
            
            reconfig_doc["affected_nodes"][node_index]["current_version"] = current_version
            reconfig_doc["affected_nodes"][node_index]["current_version_cdc_id"] = cdc_id
            
            # Update last_successful if it was 0
            last_successful = reconfig_doc["affected_nodes"][node_index].get("last_successful", 0)
            if last_successful == 0:
                reconfig_doc["affected_nodes"][node_index]["last_successful"] = current_version
                reconfig_doc["affected_nodes"][node_index]["last_successful_cdc_id"] = cdc_id
                
            # Define the hierarchy mapping as specified
            hierarchy_mapping = {
                "Project": ["RequirementRoot"],
                "RequirementRoot": ["Epic"],
                "Epic": ["ArchitecturalRequirement"],  # Special case that considers dependencies
                "UserStory": [],  # End of requirements branch
                "Requirement":["SystemContext"],
                "ArchitecturalRequirement": ["SystemContext"],
                "SystemContext": ["Container"],
                "Container": ["Component"],  # Special case that considers dependencies
                "Component": ["Design"],  # Special case that considers dependencies
                "Design": ["Interface"],  # Special case that considers dependencies
                "Interface": []  # End of architecture branch
            }
            
            # Initialize node type mapping for all affected nodes
            node_type_mapping = {
                "Project": [],
                "RequirementRoot": [],
                "Epic": [],
                "UserStory": [],
                "ArchitecturalRequirement": [],
                "SystemContext": [],
                "Container": [],
                "Component": [],
                "Design": [],
                "Interface": []
            }
            
            # Populate the node_type_mapping with actual nodes and their indices
            for i, node in enumerate(reconfig_doc.get("affected_nodes", [])):
                node_type = node.get("node_type")
                if node_type in node_type_mapping:
                    node_type_mapping[node_type].append({
                        "index": i,
                        "node_id": node["node_id"],
                        "reconfig_needed": node.get("reconfig_needed", False)
                    })
            
            # Record current changes to apply
            changes_to_apply = {}  # Format: {node_index: new_reconfig_needed_value}
            
            # Set the current node's reconfig_needed to False
            changes_to_apply[node_index] = False
            
            # CRITICAL PART: Find and flag next level nodes based on hierarchy
            if current_node_type in hierarchy_mapping:
                next_types = hierarchy_mapping[current_node_type]
                
                for next_type in next_types:
                    # Find nodes of the next type that should be flagged
                    for node_info in node_type_mapping.get(next_type, []):
                        # Set reconfig_needed to True for next level nodes
                        changes_to_apply[node_info["index"]] = True
                        print(f"Setting reconfig_needed=True for {next_type} node (ID: {node_info['node_id']})")
            
            # Apply all changes
            for index, new_value in changes_to_apply.items():
                # Ensure index is within range
                if index < len(reconfig_doc["affected_nodes"]):
                    node_id_changing = reconfig_doc["affected_nodes"][index]["node_id"]
                    node_type_changing = reconfig_doc["affected_nodes"][index]["node_type"]
                    old_value = reconfig_doc["affected_nodes"][index].get("reconfig_needed", False)
                    
                    reconfig_doc["affected_nodes"][index]["reconfig_needed"] = new_value
                    
                    # Update timestamps and reasons
                    if new_value:
                        # Being flagged for reconfiguration
                        reconfig_doc["affected_nodes"][index]["flagged_at"] = datetime.now().isoformat()
                        reconfig_doc["affected_nodes"][index]["reconfig_reason"] = f"Parent {current_node_type} was modified: {reason}"
                    else:
                        # Being marked as configured
                        reconfig_doc["affected_nodes"][index]["reconfigured_at"] = datetime.now().isoformat()
                        
                    print(f"Changed node {node_id_changing} ({node_type_changing}) reconfig_needed: {old_value} -> {new_value}")
                else:
                    print(f"Warning: Index {index} is out of range in affected_nodes array")
            
            # Update Neo4j nodes to reflect the changes
            for index, new_value in changes_to_apply.items():
                if index < len(reconfig_doc["affected_nodes"]):
                    node_id_to_update = reconfig_doc["affected_nodes"][index]["node_id"]
                    try:
                        # Update the node in Neo4j
                        await self.db.update_node_by_id(
                            node_id=node_id_to_update,
                            properties={"reconfig_needed": new_value}
                        )
                        print(f"Updated Neo4j node {node_id_to_update} with reconfig_needed={new_value}")
                    except Exception as e:
                        print(f"Error updating Neo4j node {node_id_to_update}: {str(e)}")
            
            # Update stats in reconfig_doc
            reconfig_doc["reconfiguration_progress"] = {
                "total_affected": len(reconfig_doc["affected_nodes"]),
                "completed": sum(1 for node in reconfig_doc["affected_nodes"] if node.get("reconfig_needed", True) is False),
                "remaining": sum(1 for node in reconfig_doc["affected_nodes"] if node.get("reconfig_needed", True) is True)
            }
            
            # Update MongoDB with all changes
            await self.mongo_db.update_one(
                {"project_id": project_id},
                reconfig_doc,
                upsert=True,
                db=self.reconfig_tracker_collection
            )
            
            # Update section flags
            await self.update_section_reconfig_flags(project_id)
            
            # Update version tracker
            await self._update_version_tracker(
                project_id,
                node_id,
                current_node_type,
                current_version,
                cdc_id
            )
            
            print(f"Updated version info for node {node_id}: current_version={current_version}, cdc_id={cdc_id}")
        
        except Exception as e:
            print(f"Error updating node version: {str(e)}")
            self.logger.error(f"Error updating node version: {str(e)}")

    
    async def _process_special_nodes_cdc(self, special_nodes: List[Dict], project_id: int):
        """
        Process CDC data for special nodes in batch.
        This is a helper method for flag_downstream_nodes.
        """
        # Group by element_id to reduce CDC calls
        element_id_to_nodes = {}
        for node in special_nodes:
            element_id = node.get("element_id")
            if element_id:
                if element_id not in element_id_to_nodes:
                    element_id_to_nodes[element_id] = []
                element_id_to_nodes[element_id].append(node)
        
        # Process each element_id once
        for element_id, nodes in element_id_to_nodes.items():
            changes = await self.get_changes_since_timestamp(element_id)
            latest_change = changes[-1] if changes else None
            cdc_id = latest_change['id'] if latest_change else None
            
            if cdc_id:
                # Update all nodes with this element_id
                for node in nodes:
                    await self._update_version_tracker(
                        project_id,
                        node["node_id"],
                        node["node_type"],
                        node["current_version"],
                        cdc_id
                    )
        
    
               
    async def find_downstream_nodes(self, node_id: int) -> List[Dict]:
        """
        Find only the immediate next level of nodes in the hierarchy.
        Based on the specific project hierarchy structure.
        
        Args:
            node_id: ID of the modified node
        
        Returns:
            List of immediate downstream nodes based on the hierarchy
        """
        # Get node info to determine its type
        node_info = await self.db.get_node_by_id(node_id)
        if not node_info:
            return []
        
        # Determine node type from labels
        node_type = None
        if 'labels' in node_info:
            for label in node_info['labels']:
                if label != 'Discussion':
                    node_type = label
                    break
        
        if not node_type:
            return []
        
        # Define the hierarchy for downstream node types
        hierarchy_mapping = {
            "Project": ["RequirementRoot"],
            "RequirementRoot": ["Epic"],
            "Epic": ["UserStory"],
            "UserStory": [],  # End of requirements branch
            "Requirement":["SystemContext"],
            "ArchitecturalRequirement": ["SystemContext"],
            "SystemContext": ["Container"],
            "Container": ["Component"],
            "Component": ["Design"],
            "Design": ["Interface"],
            "Interface": []  # End of architecture branch
        }
        
        # Special case: When RequirementRoot is reconfigured, also include ArchitecturalRequirement
        special_mapping = {
            "RequirementRoot": ["Epic", "ArchitecturalRequirement"]
        }
        
        # Determine target types to find
        target_types = []
        if node_type in special_mapping:
            target_types = special_mapping[node_type]
        elif node_type in hierarchy_mapping:
            target_types = hierarchy_mapping[node_type]
        
        if not target_types:
            self.logger.info(f"No downstream node types defined for {node_type}")
            return []
        
        self.logger.info(f"Finding downstream nodes of types: {target_types} for node {node_id}")
        
        # Get direct children of the specified type
        result_nodes = []
        for target_type in target_types:
            # Query to find direct children of the specified type
            query = f"""
            MATCH (n)-[:HAS_CHILD]->(m:{target_type})
            WHERE ID(n) = $node_id
            AND NOT m:Discussion
            RETURN ID(m) AS id, LABELS(m) AS labels, properties(m) AS properties
            """
            
            result = await self.db.async_run(query, node_id=node_id)
            result_nodes.extend(result.data())
        
        # Remove duplicates by node ID
        unique_nodes = {}
        for node in result_nodes:
            # Extract node type from properties or labels
            if 'properties' in node and 'Type' in node['properties']:
                new_node_type = node['properties']['Type']
            else:
                new_node_type = node['labels'][0] if 'labels' in node and node['labels'] else "Unknown"
            
            # Update node with correct type
            node['node_type'] = new_node_type
            
            # Add to unique nodes dict
            if node['id'] not in unique_nodes:
                unique_nodes[node['id']] = node
        
        return list(unique_nodes.values())

   
        
  
    async def clear_reconfig_flag(self, node_id: int, cdc_event_id: str = None) -> None:
        """
        Clear the reconfiguration flag for a specific node after it has been reconfigured.
        Also flags the next level nodes in the hierarchy for reconfiguration.
        Strictly follows the hierarchy mapping provided.
        
        Args:
            node_id: ID of the reconfigured node
            cdc_event_id: CDC event ID for this version
        """
        # Define the hierarchy mapping to strictly follow
        hierarchy_mapping = {
            "Project": ["RequirementRoot"],
            "RequirementRoot": ["Epic"],
            "Epic": ["ArchitecturalRequirement"],  # Special case that considers dependencies
            "UserStory": [],  # End of requirements branch
            "Requirement":["SystemContext"],
            "ArchitecturalRequirement": ["SystemContext"],
            "SystemContext": ["Container"],
            "Container": ["Component"],  # Special case that considers dependencies
            "Component": ["Design"],  # Special case that considers dependencies
            "Design": ["Interface"],  # Special case that considers dependencies
            "Interface": []  # End of architecture branch
        }
        
        try:
            # Get project_id
            project_node = await self.db.get_absolute_root_node(node_id)
            project_id = project_node['id'] if project_node and project_node.get('id') is not None else node_id
            
            # Get current node info
            current_node = await self.db.get_node_by_id(node_id)
            if not current_node or 'labels' not in current_node:
                self.logger.error(f"Node {node_id} not found or invalid")
                return
            
            # Get node type from labels
            node_type = None
            for label in current_node['labels']:
                if label != 'Discussion':
                    node_type = label
                    break
            
            if not node_type:
                self.logger.warning(f"Could not determine type for node {node_id}")
                return
            
            # Get reconfig tracker document
            reconfig_doc = await self.mongo_db.get_one(
                {"project_id": project_id},
                self.reconfig_tracker_collection
            )
            
            if not reconfig_doc or "affected_nodes" not in reconfig_doc:
                print(f"No affected nodes found for project {project_id}, nothing to update")
                return
            
            # Find the node's index in affected_nodes
            node_index = None
            for i, node in enumerate(reconfig_doc["affected_nodes"]):
                if node["node_id"] == node_id:
                    node_index = i
                    break
            
            # If node wasn't found in affected_nodes, nothing to do
            if node_index is None:
                print(f"Node {node_id} not found in affected_nodes, nothing to update")
                return
            
            print(f"Setting reconfig_needed to false for node {node_id} of type {node_type}")
            
            # Set the current node to not need reconfiguration
            reconfig_doc["affected_nodes"][node_index]["reconfig_needed"] = False
            
            # Update timestamp to show when it was reconfigured
            reconfig_doc["affected_nodes"][node_index]["reconfigured_at"] = datetime.now().isoformat()
            
            # Increment current_version and update CDC ID
            current_version = reconfig_doc["affected_nodes"][node_index].get("current_version", 1) + 1
            reconfig_doc["affected_nodes"][node_index]["current_version"] = current_version
            
            if cdc_event_id:
                reconfig_doc["affected_nodes"][node_index]["current_version_cdc_id"] = cdc_event_id
            
            # Update last_successful ONLY if it was 0 (initial config)
            last_successful = reconfig_doc["affected_nodes"][node_index].get("last_successful", 0)
            if last_successful == 0:
                reconfig_doc["affected_nodes"][node_index]["last_successful"] = current_version
                if cdc_event_id:
                    reconfig_doc["affected_nodes"][node_index]["last_successful_cdc_id"] = cdc_event_id
            
            # Initialize node type mapping for grouping affected nodes by type
            node_type_mapping = {}
            for key in hierarchy_mapping.keys():
                node_type_mapping[key] = []
                
            # Populate the node_type_mapping
            for i, node in enumerate(reconfig_doc["affected_nodes"]):
                node_type_val = node.get("node_type")
                if node_type_val in node_type_mapping:
                    node_type_mapping[node_type_val].append({
                        "index": i,
                        "node_id": node["node_id"],
                        "reconfig_needed": node.get("reconfig_needed", False)
                    })
            
            # Record changes to apply
            changes_to_apply = {}
            
            # Set the current node to not need reconfiguration
            changes_to_apply[node_index] = False
            
            # CRITICAL: Flag next level nodes based on hierarchy mapping
            if node_type in hierarchy_mapping:
                next_types = hierarchy_mapping[node_type]
                
                # Find nodes of each next type to flag
                for next_type in next_types:
                    # Find relevant nodes in the affected_nodes array
                    nodes_of_type = node_type_mapping.get(next_type, [])
                    
                    if nodes_of_type:
                        # Flag existing nodes
                        for node_info in nodes_of_type:
                            print(f"Setting reconfig_needed=True for {next_type} node (ID: {node_info['node_id']})")
                            changes_to_apply[node_info["index"]] = True
                    else:
                        # Special case: For ArchitecturalRequirement -> SystemContext, we may need to find nodes not yet in affected_nodes
                        if node_type == "ArchitecturalRequirement" and next_type == "SystemContext":
                            # Query for SystemContext nodes under this project
                            system_context_query = """
                            MATCH (p:Project)-[:HAS_CHILD]->(arr:ArchitectureRoot)-[:HAS_CHILD]->(sc:SystemContext)
                            WHERE ID(p) = $project_id
                            RETURN ID(sc) AS id, LABELS(sc) AS labels, properties(sc) AS properties
                            """
                            result = await self.db.async_run(system_context_query, project_id=project_id)
                            system_context_nodes = result.data()
                            
                            # If we found any SystemContext nodes, add them to affected_nodes and flag them
                            if system_context_nodes:
                                current_time = datetime.now().isoformat()
                                for sc_node in system_context_nodes:
                                    sc_id = sc_node['id']
                                    # Check if this node is already in the affected_nodes (shouldn't be, but check anyway)
                                    existing_index = None
                                    for i, node in enumerate(reconfig_doc["affected_nodes"]):
                                        if node["node_id"] == sc_id:
                                            existing_index = i
                                            break
                                    
                                    if existing_index is not None:
                                        # Node exists, flag it
                                        changes_to_apply[existing_index] = True
                                    else:
                                        # Node doesn't exist yet, add it to affected_nodes
                                        parent_node = await self.db.get_parent_node(sc_id)
                                        parent_id = parent_node['id'] if parent_node else None
                                        
                                        new_node = {
                                            "node_id": sc_id,
                                            "node_type": "SystemContext",
                                            "parent_id": parent_id,
                                            "reconfig_needed": True,
                                            "reconfig_reason": f"Parent {node_type} was reconfigured",
                                            "flagged_at": current_time,
                                            "current_version": 0,
                                            "last_successful": 0,
                                            "created_at": current_time
                                        }
                                        
                                        # Add it to the affected_nodes array
                                        reconfig_doc["affected_nodes"].append(new_node)
                                        # Flag this new node
                                        changes_to_apply[len(reconfig_doc["affected_nodes"]) - 1] = True
                                        
                                        print(f"Added SystemContext node {sc_id} to affected_nodes and flagged it")
                                        
                                        # Also update Neo4j
                                        await self.db.update_node_by_id(
                                            node_id=sc_id,
                                            properties={"reconfig_needed": True}
                                        )
            
            # Apply all the changes to the reconfig_doc
            changed_nodes = []
            for index, new_value in changes_to_apply.items():
                if index < len(reconfig_doc["affected_nodes"]):
                    node_id_changed = reconfig_doc["affected_nodes"][index]["node_id"]
                    node_type_changed = reconfig_doc["affected_nodes"][index]["node_type"]
                    old_value = reconfig_doc["affected_nodes"][index].get("reconfig_needed", False)
                    
                    # Only record if there's a change
                    if old_value != new_value:
                        reconfig_doc["affected_nodes"][index]["reconfig_needed"] = new_value
                        
                        # Update timestamps and reasons
                        if new_value:
                            # Being flagged for reconfiguration
                            reconfig_doc["affected_nodes"][index]["flagged_at"] = datetime.now().isoformat()
                            reconfig_doc["affected_nodes"][index]["reconfig_reason"] = f"Parent {node_type} was reconfigured"
                        else:
                            # Being marked as configured
                            reconfig_doc["affected_nodes"][index]["reconfigured_at"] = datetime.now().isoformat()
                        
                        changed_nodes.append({
                            "node_id": node_id_changed,
                            "node_type": node_type_changed,
                            "old_value": old_value,
                            "new_value": new_value
                        })
                        
                        print(f"Changed node {node_id_changed} ({node_type_changed}) reconfig_needed: {old_value} -> {new_value}")
                        
                        # Also update the Neo4j database
                        try:
                            await self.db.update_node_by_id(
                                node_id=node_id_changed,
                                properties={"reconfig_needed": new_value}
                            )
                        except Exception as e:
                            print(f"Error updating Neo4j node {node_id_changed}: {str(e)}")
                else:
                    print(f"Warning: Index {index} is out of range in affected_nodes array")
            
            # Count how many nodes still need reconfiguration
            nodes_still_needing_reconfig = sum(
                1 for node in reconfig_doc["affected_nodes"] if node.get("reconfig_needed", True) is True
            )
            
            # Update progress stats
            total = len(reconfig_doc["affected_nodes"])
            completed = total - nodes_still_needing_reconfig
            remaining = nodes_still_needing_reconfig
            
            reconfig_doc["reconfiguration_progress"] = {
                "total_affected": total,
                "completed": completed,
                "remaining": remaining
            }
            
            # Add completion record (if you're tracking this)
            if "completed_reconfigs" not in reconfig_doc:
                reconfig_doc["completed_reconfigs"] = []
            
            reconfig_doc["completed_reconfigs"].append({
                "node_id": node_id,
                "node_type": node_type,
                "version": current_version,
                "cdc_event_id": cdc_event_id,
                "completed_at": datetime.now().isoformat()
            })
            
            # Update MongoDB document
            filter_query = {"project_id": project_id}
            result = await self.mongo_db.update_one(
                filter_query,
                reconfig_doc,
                upsert=True,
                db=self.reconfig_tracker_collection
            )
            
            # Update section flags
            await self.update_section_reconfig_flags(project_id)
            
            print(f"Updated MongoDB document - {len(changed_nodes)} nodes changed")
            print(f"Updated versions: current_version={current_version}, last_successful={reconfig_doc['affected_nodes'][node_index]['last_successful']}")
            print(f"Updated progress: {completed}/{total} completed, {remaining} remaining")
            
            # Update version tracker collection
            await self._update_version_tracker(
                project_id, 
                node_id, 
                node_type,
                current_version,
                cdc_event_id
            )
            
        except Exception as e:
            print(f"Error clearing reconfig flag: {str(e)}")
            self.logger.error(f"Error clearing reconfig flag: {str(e)}")

    async def get_nodes_needing_reconfig(self, project_id: int) -> List[Dict]:
        """
        Get all nodes that need reconfiguration for a project.

        Args:
            project_id: ID of the project

        Returns:
            List of node details needing reconfiguration
        """
        reconfig_doc = await self.mongo_db.get_one(
            {"project_id": project_id},
            self.mongo_db.db["reconfig_tracker"]
        )

        if reconfig_doc and "affected_nodes" in reconfig_doc:
            return reconfig_doc["affected_nodes"]

        return []

    
    async def get_node_reconfig_status(self, node_id: int) -> Dict:
        """
        Get detailed reconfiguration status for a node.

        Args:
            node_id: ID of the node to check

        Returns:
            Dict with reconfiguration details or None if not needed
        """
        # Find the project for this node
        project_node = await self.db.get_absolute_root_node(node_id)
        
        # If project_node['id'] is None, use node_id as project_id
        project_id = project_node['id'] if project_node and project_node.get('id') is not None else node_id

        # Get the reconfig tracker
        reconfig_doc = await self.mongo_db.get_one(
            {"project_id": project_id},
            self.mongo_db.db["reconfig_tracker"]
        )

        if not reconfig_doc or "affected_nodes" not in reconfig_doc:
            return None

        # Find this node in the affected_nodes array
        node_status = next(
            (item for item in reconfig_doc["affected_nodes"] if item["node_id"] == node_id),
            None
        )

        return node_status
