from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from app.utils.datetime_utils import generate_timestamp
class ArchitectureDesignDetails(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Architecture")
        self.template_name = "architecture_design_details.prompt"
        self.function_schema_type = "ArchitectureDesignDetails"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def async_initialize(self):
        # Initialization specific to this configuration
        await super().async_initialize()
        # Additional async setup specific to this class can be added here
    
    
    async def retrieve_info(self):
        await super().retrieve_info()

        # Get project details
        project_node = self.retrieved_info['root_node']
        self.retrieved_info['project_details'] = {
            'description': project_node['properties'].get('Description', ''),
            'scope': project_node['properties'].get('Scope', ''),
            'architecture_strategy': project_node['properties'].get('ArchitectureStrategy', ''),
            'additional_details': project_node['properties'].get('AdditionalDetails', {}),
            'team_composition': project_node['properties'].get('TeamComposition', '')
        }
        self.retrieved_info['architecture_pattern'] = project_node['properties'].get('ArchitecturePattern', 'adaptive')
        # Get parent container information
        parent_container = await self.db.get_parent_node(self.node_id)
        if parent_container:
            self.retrieved_info['container'] = {
                'id': parent_container['id'],
                'title': parent_container['properties'].get('Title', ''),
                'description': parent_container['properties'].get('Description', ''),
                'tech_stack': parent_container['properties'].get('Selected_Tech_Stack', ''),
                'user_interactions': parent_container['properties'].get('UserInteractions', ''),
                'external_system_interactions': parent_container['properties'].get('ExternalSystemInteractions', '')
            }
         # Get project ID from retrieved_info
        project_id = self.retrieved_info['root_node']['id']
        # Get architecture root
        arch_roots = await self.db.get_child_nodes(project_id, "ArchitectureRoot")
        if arch_roots:
            arch_root_id = arch_roots[0]['id']
            architecture_node = self.retrieved_info['current_node']['id']
            
            # Get both requirements and arch info
            requirements = await self.get_architectural_requirements(arch_root_id)
            arch_info = await self.get_architectural_node_info(arch_root_id, architecture_node)
            
            self.retrieved_info.update(requirements)
            self.retrieved_info.update(arch_info)
        
            arch_req_nodes = await self.db.get_child_nodes(arch_roots[0]['id'], "ArchitecturalRequirement")
            if arch_req_nodes:
                # Get the functional and non-functional requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "NonFunctionalRequirement")
                
                # Store both nodes and formatted text for backward compatibility
                self.retrieved_info['functional_requirements'] = functional_reqs
                self.retrieved_info['architectural_requirements'] = nonfunctional_reqs

        # Get the Interface node for this component
        interface_nodes = await self.db.get_child_nodes(self.node_id, "Interface")
        if interface_nodes:
            interface_node = interface_nodes[0]  # Assume there's only one Interface node per component
            self.retrieved_info['interface_node'] = interface_node

            # Extract interface information from the Interface node
            self.retrieved_info['interfaces'] = []
            for key, value in interface_node['properties'].items():
                if key.startswith('interface_'):
                    interface_data = json.loads(value)
                    source_node = await self.db.get_node_by_id(interface_data['source_component_id'])
                    interface_data['source_node'] = source_node
                    self.retrieved_info['interfaces'].append(interface_data)
        
         # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']
        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_nodes:
                # self.retrieved_info['functional_requirements'] = arch_req_nodes[0]['properties'].get('functional_requirements', '')
                # self.retrieved_info['architectural_requirements'] = arch_req_nodes[0]['properties'].get('architectural_requirements', '')
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_node:
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        # Retrieve existing external systems and users
        system_context = self.retrieved_info['current_node']
        self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
        self.retrieved_info['users'] = system_context['properties'].get('Users', '')

        # Retrieve existing containers
        containers = await self.db.get_child_nodes(system_context['id'], "Container")
        parent_node = await self.db.get_parent_node(self.node_id)
        components = await self.db.get_child_nodes(parent_node['id'], "Component") or None

        # components_list = []
        # for container in containers:
        #     components = await self.db.get_child_nodes(container['id'], "Component")
        #     components_list.append(
        #         {'container': container,
        #         'components': components
        #         })

        container_component_details = []
        container_component_details.append({
            'container': parent_node['properties'] if project_node else {},
            'components': components
        })

        # Structure current background info with complete node information
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'system_context': {
                'users': self.retrieved_info['users'],
                'external_systems': self.retrieved_info['external_systems'],
                'container': parent_node['properties'] if project_node else {},  # Already storing full container nodes
                'container_component_details': container_component_details
            },
            'requirements_context': {
                'functional_requirements': architectural_reqs['functional_requirements'],
                'architectural_requirements': architectural_reqs['architectural_requirements'],
                'parent_node': arch_req_node[0] if arch_req_node else None  # Store parent node for context
            }
        }

        # Handle reconfiguration
        if self.is_reconfig():
            self.model_name = 'gpt-4o'
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info

    
    def get_modifications_from_llm_output(self):

        #super().get_modifications_from_llm_output(node_type, root_node_type, discussion_type, llm_output)
        self.modifications['modified_node']['Type'] = 'Architecture'
        self.modifications['child_node_types'] = ['Architecture']
        self.modifications['next_steps'] = self.modifications.get('Next_Steps')

        return
    
    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
    
    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
                await super().merge_captured_items()
                # Save version info
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'reconfig',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)

        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise
# Registering the subclass in the factory
DiscussionFactory.register('design_details', ArchitectureDesignDetails, "Architecture")