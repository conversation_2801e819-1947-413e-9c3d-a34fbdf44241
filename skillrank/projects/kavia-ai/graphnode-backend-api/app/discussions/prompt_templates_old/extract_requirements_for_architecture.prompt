{# Check if the prompt type is "user" #}
{% if prompt_type == "user" %}
{% block user_prompt %}
{% block task_description %}
To provide your response, please use the capture_discussion_output function with the following structure:

capture_discussion_output({
    "Modified-Node": {
        "Title": "Updated title if changed",
        "Description": "Updated description of the component or design",
        // Other fields as needed
    },
    // Additional fields based on the specific prompt and node type
})

Please ensure your response adheres to this function structure. Now, for your specific task:



Your task is to study carefully the list of Epics and UserStories and extract functional and architectural requirements from those 
so that those requirements could be used by an AI agent to create a high-level architectural decomposition, API definitions, and component diagrams for 
the system or the component specified in the current node. All information is stored in the system as nodes. The current node represents the whole system or 
the component for which we are extracting requirements. Epics and UserStories are also stored as nodes and are provided here. Since architectural requirements
may not be explicitly mentioned, you may have to infer them from the requirements. In addition you have to use your knowledge and imagination to create 
comprehensive architectural requirements. Architectural requirements are typically what a software architect would use to design a high level architecture of the system, such as:
scalability , availability, performance, security, modularity, data storage, interfaces/APIs, infrastructure, deployment, etc. You should extract these types of requirements from the
user stories and epics and express them clearly. Architectural requirements should not contain functional requirements.
{% endblock %}

{% block current_node_info %}
Here is the information about the current node: {{current_node | tojson}}
{% endblock %}

{% block batch_of_nodes %}
Here is the list of UserStories and Epics from which you will extract functional and architectural requirements: {{requirement_nodes | tojson(indent=2)}}
{% endblock %}

{% block existing_requirements %}
In addition to extracting requirements from the UserStories and Epics, you will also merge the newly generated/extracted requirements with the existing requirements
and output a summarized list of all requirements. Here is the list of existing functional and architectural requirements: 
Functional requirements: {{current_requirements.get('functional_requirements') | tojson(indent=2)}}
Architectural requirements: {{current_requirements.get('architectural_requirements') | tojson(indent=2)}}
{% endblock %}


{% block output_format %}
Please use the following JSON format for for your output:
{{output_format}}
JSON fields should contain just the content for that field and should not contain any other discussion or comments.
{% endblock %}
{% endblock %}
{% endif %}

{# Check if the prompt type is "system" #}
{% if prompt_type == "system" %}
{% block system_prompt %}
You are an agent that extracts functional and architectural requirements from a set of Epics and UserStories. 
You will output the extracted requirements in a JSON format that can be easily consumed by a requirement processing software. 
There should not be any extra characters in the output outside of the JSON format.
{% endblock %}
{% endif %}