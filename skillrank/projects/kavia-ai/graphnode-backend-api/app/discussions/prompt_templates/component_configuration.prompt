{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}
As an expert software architect, configure a Component for the C4 model:

1. Review the component's purpose and responsibilities within its container.
2. Identify and define the main classes and interfaces that make up this component:
   - For each class, provide:
     a. Name
     b. Brief description
     c. Key attributes (fields)
     d. Important methods
   - For each interface, provide:
     a. Name
     b. Brief description
     c. Method signatures
3. Define the relationships between classes and interfaces within the component:
   - Class implementing an interface (IMPLEMENTS)
   - Class extending another class (EXTENDS)
   - Class using another class or interface (USES)
   - Interface extending another interface (EXTENDS)
4. Identify and describe interactions with other components (if any).
5. Specify the technology stack used for this component, ensuring it aligns with the container's technology.
6. Review the component's purpose and responsibilities within its container.
   a. Verify container's repository details:
      Note parent container's repository strategy (Monorepo/MultiRepo/Hybrid)
      Get container's repository path and container path
      Ensure component path aligns with container structure
   b. Define component path structure:
      Build component path relative to container path
   c. Follow repository strategy conventions:
         Monorepo: ${container.container_path}/components/${component-name}/
         MultiRepo: /src/components/${component-name}/
         Hybrid: Based on container's organization pattern
6. Generate C4 Component diagram using Mermaid syntax, including:
   - The component itself
   - Main classes and interfaces within the component
   - Relationships between classes and interfaces
   - Interactions with other components (if any)

Here's a comprehensive example of how components should be organized within a container following C4 model principles:


graph TB
    %% External
    Gateway[API Gateway]
    DB[(Activity Database)]
    
    subgraph Component[Activity Tracking Component]
        Controller[Activity Controller<br/>Handles REST endpoints]
        DTO[ActivityDTO<br/>Data Transfer Object]
        IService[IActivityService<br/>Service Interface]
        Service[ActivityService<br/>Business Logic]
        IRepo[IActivityRepository<br/>Repository Interface]
        Repository[ActivityRepository<br/>Data Access Layer]
        Domain[Activity Entity<br/>Domain Model]
        Validator[Activity Validator<br/>Business Rules]
    end
    
    %% External Interactions with Protocols
    Gateway -->|"REST/HTTP"| Controller
    Repository -->|"SQL/TCP"| DB
    
    %% API Layer Relationships
    Controller -->|"maps to"| DTO
    Controller -->|"uses"| IService
    DTO -->|"maps to"| Domain
    
    %% Service Layer Relationships
    Service -.->|"implements"| IService
    Service -->|"validates"| Validator
    Service -->|"uses"| IRepo
    Service -->|"uses"| Domain
    
    %% Data Layer Relationships
    Repository -.->|"implements"| IRepo
    Repository -->|"persists"| Domain
    Validator -->|"validates"| Domain

    classDef default fill:#85bbf0,stroke:#5d82a8,color:black
    classDef interface fill:#85bbf0,stroke:#5d82a8,color:black,stroke-dasharray: 5 5
    classDef external fill:#666666,stroke:#333333,color:white
    classDef database fill:#438dd5,stroke:#2e6295,color:white

    class Controller,Service,Repository,Domain,DTO,Validator default
    class IService,IRepo interface
    class Gateway external
    class DB database

Note: Always use standard Mermaid arrow syntax:
- Use --> for dependencies/flows (NOT -->>)
- Use -.-> for implementations (NOT -..>>)
- Add labels using -->|"label"| syntax


1. Component Level:
   - Implementation Relationships:
     * Interface implementations (dotted lines)
     * Show "implements" relationship
   - Usage Relationships:
     * Dependencies between classes
     * Show "uses" relationship
   - Data Flow:
     * DTO mappings
     * Domain object usage
     * Data persistence
   - Validation Flow:
     * Business rule validation
     * Data validation paths

2. Relationship Styling:
   - Solid lines: Direct dependencies
   - Dotted lines: Implementation relationships
   - Labeled arrows: Show purpose and protocol
   - Clear directionality
   - Meaningful descriptions

{% endblock %}

{% block autoconfig %}
Create a comprehensive Component configuration based on the C4 modeling principles and the component's role within its container. Ensure all attributes are thoroughly defined to effectively guide the component's design and address key functional and non-functional requirements. Define classes, interfaces, and their relationships in detail.
{% endblock %}


Guide the user through the Component configuration process:
1. Explain the role of Components, Classes, and Interfaces in the C4 model.
2. Review and refine the component's main responsibilities within its container.
3. For each main class in the component:
   a. Define the class name and description
   b. Identify key attributes (fields)
   c. List important methods
   d. Specify any interfaces it implements or classes it extends
4. For each interface in the component:
   a. Define the interface name and description
   b. List the method signatures
   c. Specify any interfaces it extends
5. Establish relationships between classes and interfaces:
   a. Identify which classes implement which interfaces
   b. Determine if any classes extend other classes
   c. Identify usage relationships between classes and interfaces
6. Identify and describe interactions with other components, if any.
7. Specify and justify the chosen technology stack for this component.
8. Create and iteratively refine the C4 Component diagram, ensuring it reflects all classes, interfaces, and their relationships.
9. Validate the configuration against C4 modeling principles and best practices.


{% block node_details_interactive_reconfig %}
Component, including its classes and interfaces, in C4 model
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Component configuration, including detailed class and interface definitions and their relationships, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Container Information: {{ details_for_discussion.get('container') | tojson(indent=2) }}
    Existing relationships: {{ details_for_discussion.get('existing_relationships') | tojson(indent=2) }}
    Existing classes: {{ details_for_discussion.get('existing_classes') | tojson(indent=2) }}
    Existing interfaces: {{ details_for_discussion.get('existing_interfaces') | tojson(indent=2) }}
    Container Information: {{ details_for_discussion.get('container') | tojson(indent=2) }}
      - Repository Strategy: {{ details_for_discussion.get('container', {}).get('properties', {}).get('RepositoryStrategy') }}
      - Repository Path: {{ details_for_discussion.get('container', {}).get('properties', {}).get('RepositoryPath') }}
      - Container Path: {{ details_for_discussion.get('container', {}).get('properties', {}).get('ContainerPath') }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Other Components in the container: 
    {{ details_for_discussion.get('other_components') | tojson(indent=2) }}
    Architectural Requirements:
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
{% endblock %}