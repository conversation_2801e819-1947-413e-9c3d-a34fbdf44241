# app/repository/mongodb/tenant.py
from app.connection.tenant_middleware import get_tenant_id
from app.core.Settings import settings
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME

def get_tenant_based_db_name(name: str) -> str:
    """
    Get tenant-specific database name
    """
    tenant_id = get_tenant_id()
    
    if name == settings.STAGE:
        return f'{name}_{tenant_id}'
    elif name != KAVIA_ROOT_DB_NAME:
        return f'{settings.STAGE}_{name}_{tenant_id}'
    return name

def is_system_collection(collection_name: str) -> bool:
    """
    Check if collection is a system-level collection
    """
    system_collections = {
        'tenant_permissions', 
        'tenant_users', 
        'tenant_groups', 
        'tenant_organizations',
        'tenant_plans'
    }
    return collection_name in system_collections