from app.tasks import processing_autoconfig
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from app.core.task_framework import Task, TaskStatus , tasks_collection_name
from app.routes.batch_route import get_active_task as get_code_gen_task
from app.connection.establish_db_connection import get_node_db, NodeDB, get_mongo_db_v1, TaskRepository ,get_mongo_db
from app.repository.mongodb.client import get_db
from app.connection.tenant_middleware import tenant_context, get_tenant_id
from app.core.Settings import settings
from app.utils.node_utils import get_node_type
from app.utils.task_utils import format_response, format_title
from app.utils.auth_utils import get_current_user
from app.celery_app import user_context
import json
import asyncio
from app.utils.datetime_utils import generate_timestamp
from app.utils.node_utils import add_parent_node_id
from app.utils.task_utils import configuration_update
import pandas as pd
import io

_SHOW_NAME = "tasks"
router = APIRouter(
    prefix=f"/v2/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {'description': 'Not found'}},
)




@router.get("/{task_id}")
def get_task_by_id(task_id: str):
    task = Task.get_by_id(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    return task.to_dict()


@router.get("/get_detailed_report/{task_id}")
def get_detailed_report(task_id: str):
    task = Task.get_detailed_report(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    return task


@router.get("/work_item/{project_id}/")
async def get_work_items(project_id: int, architecture_id: int, db: NodeDB = Depends(get_node_db)):
    # Validate project exists and is properly configured
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    # Get work items for the architecture (this already includes test cases)
    work_items = await db.get_work_items(architecture_id)
    
    return work_items


@router.get("/work_item_test_case/{project_id}/")
async def get_work_items_test_case(project_id: int, container_id: int, db: NodeDB = Depends(get_node_db)):
    # Validate project exists and is properly configured
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    # Get work items for the architecture (this already includes test cases)
    work_items = await db.get_work_items_for_testcase(container_id)
    
    return work_items



@router.post("/test")
def test_task_trigger():
    task = Task.schedule_task(processing_autoconfig, 1, 2)
    return task.to_dict()

@router.get("/all_active_tasks/{project_id}")
def get_all_active_tasks(project_id: int):
    task = Task.get_pending_by_project_id(project_id)
    if not task:
        task = {"task_id": None}
    else:
        task = task.to_dict()
    code_gen_task = get_code_gen_task(project_id)
    if code_gen_task and code_gen_task.get("task_id"):
        task["code_gen_task_id"] = code_gen_task.get("task_id")

    return task

@router.get("/active_tasks/{node_id}")
async def get_active_tasks(node_id: int, node_type: str= None):
    
    task_repository: TaskRepository = await get_mongo_db_v1("task", tasks_collection_name)
    db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
    
    if node_type:
        node_type = get_node_type(node_type)
        if node_type == "Project":
            task = Task.get_pending_by_project_id(node_id)
        if not task:
            task = Task.get_pending_by_node_id(node_id)
     
    else:
        task = Task.get_pending_by_node_id(node_id)

    if not task:
        query = {
            "project_id": node_id
        }
        # Using sort parameter in the correct format for find_many
        sort_params = [("start_time", -1)]
        previous_tasks = await task_repository.find_many(
            filter_dict=query,
            db=db,
            projection=None,
            sort=sort_params,
            limit=1
        )
        
        previous_task = previous_tasks[0] if previous_tasks else None
        
        response = {
            "task_id": None,
            "prev_task_id": str(previous_task.get("_id")) if previous_task else None,
            "start_time": str(previous_task.get("start_time")) if previous_task else None,
            "type": str(previous_task.get("type", "auto-config")) if previous_task else None
        }

        if response['type'] == 're-config':
            response['approved_or_rejected'] = previous_task.get("approved_or_rejected", False)

        return response
    
    return task.to_dict()

@router.get("/get_latest_active_task/{project_id}")
async def get_latest_active_task(project_id: int, node_id: int, node_type: str):
    node_type = get_node_type(node_type)
    mongo_handler = get_mongo_db().db
    collection_name = "tf_tasks"
    query = {
        "project_id": project_id,
        "node_id": node_id,
        "node_type": node_type
    }
    sort_params = [("start_time", -1)]
    task = await mongo_handler[collection_name].find_one(query, sort=sort_params)
    if not task:
        return {}
    return task


@router.get("/past_tasks/{project_id}")
async def get_past_tasks(project_id: int, page: int = 1, limit: int = 10):
    mongo_handler = get_mongo_db().db
    node_db = get_node_db()
    collection_name = "tf_tasks"
    query = {
        "project_id": project_id
    }
    cached_user_details = {}
    tasks = list(mongo_handler[collection_name].find(query).skip((page - 1) * limit).limit(limit).sort("start_time", -1))
    for task in tasks:
        try:
            user_id = task.get("user_id")
            if user_id:
                if user_id not in cached_user_details:
                    user_details = await node_db.get_user_by_id(user_id)
                    cached_user_details[user_id] = user_details.get("properties", {})
                task["user_details"] = cached_user_details[user_id]
        except Exception as e:
            print(e)
            continue

    total = mongo_handler[collection_name].count_documents(query)
    
    return {
        "tasks": tasks,
        "total": total,
        "page": page,
        "limit": limit
    }

@router.get("/total_past_tasks/{project_id}")
async def get_total_past_tasks(project_id: int):
    mongo_handler = get_mongo_db().db
    collection_name = "tf_tasks"
    query = {
        "project_id": project_id
    }
    total = mongo_handler[collection_name].count_documents(query)
    return total

@router.get("/get_task_by_id/{task_id}")
async def get_task_by_id(task_id: str):
    mongo_handler = get_mongo_db().db
    node_db = get_node_db()
    collection_name = "tf_tasks"
    task = mongo_handler[collection_name].find_one({"task_id": task_id})
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    user_id = task.get("user_id")
    if user_id:
        user_details = await node_db.get_user_by_id(user_id)
        task["user_details"] = user_details.get("properties", {})
    return task


@router.post("/configure/{node_id}/{node_type}/")
async def agent_configure_node(node_id:int, node_type:str, user_level:int, project_id: int = None, type: str = "auto-config", request: dict = None, current_user=Depends(get_current_user)):
    node_type = get_node_type(node_type)
    
    project_task = Task.get_pending_by_project_id(project_id)    
    retrive_task = Task.get_pending_by_node_id(node_id)
    
    current_tenant = tenant_context.get()
    user_context.set(current_user.get('cognito:username'))
    print("current_tenant", current_tenant)
    if retrive_task or project_task:
        print("Active task", retrive_task, "\nProject Task", project_task)
    if retrive_task != None and project_task != None:
        return {
            'error': 'You already have a task in progress. Please cancel the current task to start a new one.',
            'task_id': retrive_task.to_dict()['task_id']
        }
   
    print(f'Scheduling task for autoconfig: node_type : {node_type}, node_id : {node_id}, user_level : {user_level}, request : {request}')
    # Schedule task with explicit tenant_id
    task = Task.schedule_task(
        processing_autoconfig, 
        node_type, 
        node_id, 
        user_level, 
        request,
        type=type,
        tenant_id=current_tenant,
        current_user=current_user.get('cognito:username'),
        project_id=project_id  # Explicitly pass tenant_id
    )
    
    update_dict = {
        'node_id': node_id,
        'start_time': generate_timestamp(),
        'tenant_id': current_tenant,
        'node_type': node_type,
    }
    repoDetails = request.get("repo", None)

    if repoDetails:
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='codebase-extraction-details'
        )
        repo_data= {
            'auto-extract': True,
            'project_id': project_id,
            'repoDetails':repoDetails,
            'task_id': task.task_id,
            'timestamp': generate_timestamp()
        }
        await mongo_handler.insert_extract_data(
        collection_name="codebase-extraction-details",  
        element=repo_data,
        db=mongo_handler.db
    )
    if project_id:
        update_dict['project_id'] = project_id
    Task.update_task(task.task_id, update_dict)
    return task.to_dict()

@router.get("/retrieve-task/")
async def retrieve_task_details(project_id: int, task_id: str):
    """Retrieve stored task details based on project_id and task_id."""
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='codebase-extraction-details'
    )
    query = {'project_id': project_id, 'task_id': task_id}
    collection_name = 'codebase-extraction-details'
    result =  await mongo_handler.get_extract_data(query,collection_name,db=mongo_handler.db)
    if not result:
        return {'error': 'No data found for the given project_id and task_id'}
    return result

@router.delete("/{task_id}")
def delete_task_by_id(task_id: str, delete_record: bool = False):
    task = Task.get_by_id(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    task.cancel()
    
    if delete_record:
        task.delete()
        
    return task.to_dict()

@router.get("/configure/task_updates/{task_id}")
async def get_task_updates(task_id: str):
    """Streams updates for a configuration task."""
    task_repo: TaskRepository = await get_mongo_db_v1("task", tasks_collection_name)
    db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
    
    return StreamingResponse(event_stream(task_id, task_repo, db=db), media_type="text/event-stream" , headers={
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no'  # This is for Nginx specifically
    })


async def event_stream(task_id: str , task_repo: TaskRepository, db= None):
    print('taskid------>',task_id)
    data = await configuration_update(task_id)
    if data:
        yield f"data: {json.dumps(data)}\n\n"
    return 
    if db == None:
        db = await get_db(f"{settings.MONGO_DB_NAME}_{get_tenant_id()}")
        

    continue_stream = True

    start_message = {"message": "Your task is being processed. Please wait..."}
    end_message = {"stop": "STOP"}
    
    previous_result = {}
    yield f"data: {json.dumps(start_message)}\n\n" 
    
    while continue_stream:
        await asyncio.sleep(3)
        result = await task_repo.get_task_by_id(task_id, db=db)
        if not result:
            continue_stream = False
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            yield f"data: {json.dumps(end_message)}\n\n"
            return
        configuration_status = result.get("configuration_status", {})
        title = format_title(configuration_status)
        
        configuration_status = await add_parent_node_id(configuration_status)
        await task_repo.update_by_task_id(task_id, {"configuration_status": configuration_status}, db=db)
        formatted_response = format_response(configuration_status)
        # print(formatted_response)
        progress = result.get('progress', 0)
        task_status = result.get('status', 'In Progress')
        run_completed = result.get('run_completed', False)
        waiting_message = {"message": "Waiting for updates..."}  # New waiting message
        cancel = result.get('cancel', False)
        start_time = result.get('start_time',None)

        if cancel:
            continue_stream = False
            yield f"data: {json.dumps({'title': title,'configuration_status':configuration_status, 'formatted_response': formatted_response ,'progress':progress, 'task_status': 'Canceled' })}\n\n"
            yield f"data: {json.dumps(end_message)}\n\n"
            return
        
        # if there a change in configuration status, send it to the client

        yield f"data: {json.dumps({'title': title,'configuration_status':configuration_status, 'formatted_response': formatted_response ,'progress':progress, 'task_status': task_status ,  })}\n\n"
        if run_completed or progress == 100 or task_status.casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold()]:
            continue_stream = False
            if result.get('status',"") == TaskStatus.FAILED:
                progress = 50
                task_status = TaskStatus.FAILED
            elif result.get('status',"") == TaskStatus.COMPLETE:
                progress = 100
                task_status = TaskStatus.COMPLETE
            elif result.get('status',"") == TaskStatus.CANCELLED:
                progress = 100
                task_status = TaskStatus.CANCELLED
            else:
                progress = 100
                task_status = TaskStatus.COMPLETE
                
            await task_repo.update_by_task_id(task_id,{"progress": progress, "status": task_status}, db=db)  
            yield f"data: {json.dumps({'title': title,'configuration_status':configuration_status, 'progress':progress, 'formatted_response': formatted_response , 'task_status': task_status, 'start_time':start_time })}\n\n"
            yield f"data: {json.dumps(end_message)}\n\n"
            return


@router.get("/configure/task_updates/non_streaming/{task_id}")
async def get_task_updates_non_streaming(task_id: str):
    try:
        return await configuration_update(task_id)
    except Exception as e:
        return {"error": str(e)}

@router.get("/test_stream/")
async def test_stream():
    """Tests a streaming response from a text file."""

    async def generate():
            i = 0
            while True: 
                i= i+1
                yield f"data: {i}\n\n"
                await asyncio.sleep(1)  # Introduce a delay between chunks

    return StreamingResponse(generate(), media_type="text/event-stream")

@router.get("/test_cases/{project_id}/")
async def get_test_cases(project_id: int, architecture_id: int, db: NodeDB = Depends(get_node_db)):
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    # Assuming there is a method to get test cases by architecture_id
    test_cases = await db.get_test_cases(architecture_id)
    return {"test_cases": test_cases}