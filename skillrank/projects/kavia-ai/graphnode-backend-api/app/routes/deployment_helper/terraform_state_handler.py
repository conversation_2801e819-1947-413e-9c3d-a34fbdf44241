import os
import json
import logging
import asyncio
import subprocess
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import boto3
from botocore.exceptions import ClientError
import tempfile
import shutil

logger = logging.getLogger(__name__)

async def ensure_directory_structure(infrastructure_path: str) -> None:
    """
    Ensure all required directories exist.
    
    Args:
        infrastructure_path: Base path for infrastructure files
    """
    try:
        # Create main infrastructure directory
        os.makedirs(infrastructure_path, exist_ok=True)
        
        # Create terraform workspace directory
        terraform_dir = os.path.join(infrastructure_path, '.terraform')
        os.makedirs(terraform_dir, exist_ok=True)
        
        # Create directory for terraform state
        state_dir = os.path.join(terraform_dir, 'terraform.tfstate.d')
        os.makedirs(state_dir, exist_ok=True)
        
        logger.info(f"Directory structure created at {infrastructure_path}")
        
    except Exception as e:
        logger.error(f"Failed to create directory structure: {str(e)}")
        raise

async def get_latest_state_from_s3(project_id: int, container_id: int, s3_bucket: str) -> Optional[str]:
    """Retrieve the latest state file from S3."""
    try:
        s3_client = boto3.client('s3')
        prefix = f"terraform/states/{project_id}/{container_id}/states"
        
        # List all state files
        response = s3_client.list_objects_v2(
            Bucket=s3_bucket,
            Prefix=prefix
        )
        
        if 'Contents' not in response:
            return None
            
        # Sort by last modified and get the latest
        latest_state = sorted(
            response['Contents'],
            key=lambda x: x['LastModified'],
            reverse=True
        )[0]
        
        # Download state file
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            s3_client.download_file(
                s3_bucket,
                latest_state['Key'],
                tmp_file.name
            )
            return tmp_file.name
            
    except Exception as e:
        logger.error(f"Failed to get state from S3: {str(e)}")
        return None

async def restore_workspace_state(
    project_id: int,
    container_id: int,
    infrastructure_path: str,
    workspace_name: str
) -> bool:
    """Restore workspace state from S3."""
    try:
        s3_bucket = os.environ.get("TERRAFORM_STATE_BUCKET")
        if not s3_bucket:
            return False
            
        # Get latest state file
        state_file = await get_latest_state_from_s3(project_id, container_id, s3_bucket)
        if not state_file:
            return False
            
        try:
            # Copy state file to workspace
            terraform_dir = os.path.join(infrastructure_path, '.terraform')
            os.makedirs(terraform_dir, exist_ok=True)
            
            workspace_state_path = os.path.join(
                terraform_dir,
                'terraform.tfstate.d',
                workspace_name,
                'terraform.tfstate'
            )
            
            os.makedirs(os.path.dirname(workspace_state_path), exist_ok=True)
            shutil.copy2(state_file, workspace_state_path)
            
            return True
            
        finally:
            # Cleanup temporary file
            if state_file and os.path.exists(state_file):
                os.unlink(state_file)
                
    except Exception as e:
        logger.error(f"Failed to restore workspace state: {str(e)}")
        return False

async def check_workspace_exists(workspace_name: str, infrastructure_path: str) -> bool:
    """Check if workspace exists."""
    try:
        result = await run_terraform_command(['workspace', 'list'], infrastructure_path)
        return workspace_name in result.stdout if result else False
    except Exception:
        return False
    
async def initialize_or_reuse_workspace(
    project_id: int,
    container_id: int,
    infrastructure_path: str,
    reuse_existing: bool = True
) -> bool:
    """Initialize a new workspace or reuse an existing one."""
    try:
        workspace_name = f"workspace_{project_id}_{container_id}"
        s3_bucket = os.environ.get("TERRAFORM_STATE_BUCKET")
        
        if not s3_bucket:
            raise ValueError("TERRAFORM_STATE_BUCKET environment variable not set")

       # Initialize providers first
        if not await initialize_terraform_providers(infrastructure_path):
            raise Exception("Failed to initialize providers")
        
        # # Initialize Terraform
        # init_result = await run_terraform_command(['init', '-reconfigure'], infrastructure_path)
        # if not init_result:
        #     raise Exception("Terraform initialization failed")

        # Check for existing workspace
        workspace_exists = await check_workspace_exists(workspace_name, infrastructure_path)
        
        if workspace_exists and reuse_existing:
            # Select existing workspace
            logger.info(f"Reusing existing workspace: {workspace_name}")
            select_result = await run_terraform_command(
                ['workspace', 'select', workspace_name],
                infrastructure_path
            )
            if not select_result:
                raise Exception(f"Failed to select workspace {workspace_name}")
                
            # Restore state if available
            if await restore_workspace_state(project_id, container_id, infrastructure_path, workspace_name):
                logger.info("Successfully restored workspace state")
            else:
                logger.warning("No existing state found to restore")
                
        else:
            # Create new workspace
            logger.info(f"Creating new workspace: {workspace_name}")
            workspace_result = await run_terraform_command(
                ['workspace', 'new', workspace_name],
                infrastructure_path
            )
            if not workspace_result:
                raise Exception(f"Failed to create workspace {workspace_name}")

        return True

    except Exception as e:
        logger.error(f"Workspace initialization failed: {str(e)}")
        return False

async def deploy_with_workspace(
    project_id: int,
    container_id: int,
    infrastructure_path: str,
    terraform_files: Dict[str, str],
    reuse_existing: bool = True
) -> Dict[str, Any]:
    """
    Deploy infrastructure using workspace management with reuse option.
    """
    try:
        # Initialize or reuse workspace
        initialized = await initialize_or_reuse_workspace(
            project_id,
            container_id,
            infrastructure_path,
            reuse_existing
        )
        
        if not initialized:
            raise Exception("Failed to initialize workspace")
            
        # Update terraform files
        for filename, content in terraform_files.items():             
            if content and (filename.endswith('.tf') or filename.endswith('.tfvars')):
                file_path = os.path.join(infrastructure_path, filename)                 
                with open(file_path, 'w') as f:                     
                    f.write(content)
                logger.debug(f"Created file: {filename}")

        logger.info("Creating deployment plan")
        # Create plan
        plan_result = await run_terraform_command(
            ['plan', '-out=tfplan'],
            infrastructure_path
        )
        if not plan_result:
            raise Exception("Terraform plan failed")

        logger.info("Applying deployment plan")
        # Apply changes
        apply_result = await run_terraform_command(
            ['apply', '-auto-approve', 'tfplan'],
            infrastructure_path
        )
        if not apply_result:
            raise Exception("Terraform apply failed")

        # Get outputs
        output_result = await run_terraform_command(
            ['output', '-json'],
            infrastructure_path
        )
        outputs = json.loads(output_result.stdout) if output_result and output_result.stdout else {}

        # Save state to S3
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        s3_bucket = os.environ.get("TERRAFORM_STATE_BUCKET")
        
        if s3_bucket:
            s3_client = boto3.client('s3')
            state_path = os.path.join(
                infrastructure_path,
                '.terraform/terraform.tfstate.d',
                f"workspace_{project_id}_{container_id}",
                'terraform.tfstate'
            )
            
            if os.path.exists(state_path):
                key = f"terraform/states/{project_id}/{container_id}/states/terraform_{timestamp}.tfstate"
                s3_client.upload_file(state_path, s3_bucket, key)

        return outputs

    except Exception as e:
        logger.error(f"Error in deployment: {str(e)}")
        raise

async def initialize_terraform_providers(infrastructure_path: str) -> bool:
    """
    Initialize Terraform providers with version locking.
    
    Args:
        infrastructure_path: Path to infrastructure directory
    """
    try:
        # Create provider configuration file
        provider_config = '''terraform {
            required_providers {
                aws = {
                source  = "hashicorp/aws"
                version = "~> 5.0"
                }
            }
            required_version = ">= 1.0.0"
            }

            provider "aws" {
            region = "us-east-2"
            }'''

        # provider_path = os.path.join(infrastructure_path, 'providers.tf')
        # with open(provider_path, 'w') as f:
        #     f.write(provider_config)
            
        # Remove existing lock file if it exists
        lock_file = os.path.join(infrastructure_path, '.terraform.lock.hcl')
        if os.path.exists(lock_file):
            os.remove(lock_file)

        # Run terraform init with provider lock
        init_result = await run_terraform_command(['init', '-reconfigure'], infrastructure_path)
        if not init_result:
            return False
            
        logger.info("Provider initialization successful")
        return True

    except Exception as e:
        logger.error(f"Failed to initialize providers: {str(e)}")
        return False

async def run_terraform_command(command: list, infrastructure_path: str) -> Optional[subprocess.CompletedProcess]:
    """Run Terraform command with proper error handling."""
    try:
        process = await asyncio.create_subprocess_exec(
            'terraform',
            *command,
            cwd=infrastructure_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Terraform command failed: {stderr.decode()}")
            return None
            
        return subprocess.CompletedProcess(
            args=command,
            returncode=process.returncode,
            stdout=stdout.decode() if stdout else "",
            stderr=stderr.decode() if stderr else ""
        )

    except Exception as e:
        logger.error(f"Error running Terraform command: {str(e)}")
        return None