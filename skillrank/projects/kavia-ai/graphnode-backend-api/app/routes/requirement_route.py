from app.connection.establish_db_connection import get_node_db, get_vector_db  , connect_mongo_db, NodeDB
from app.utils.auth_utils import get_current_user
from app.models.requirement_model import RequirementStatus, PriorityType
from fastapi import APIRouter, Depends, Body , Query, Request, responses, HTTPException , BackgroundTasks
from typing import List, Union, Annotated,Dict
from fastapi.responses import JSONResponse
from app.core.function_schema_generator import load_json_file
from datetime import datetime
import json
from app.core.data_model_helper import data_model



_SHOW_NAME = "requirement"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

db = get_node_db()


@router.get("/top_level_requirements/")
async def get_top_level_nodes(root_node_id: int, node_type: str = "Requirement", db:NodeDB = Depends(get_node_db)):
    #Return all top level requirement nodes with their information
    try:
        result = await db.get_top_level_nodes(root_node_id, node_type)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/child_requirements/")
async def get_child_requirements(parent_node_id: int, child_node_type: str = "Requirement",  db:NodeDB = Depends(get_node_db)):
    #Return all child requirement nodes with their information
    try:
        result = await db.get_child_requirements(parent_node_id , child_node_type)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{node_id}/")
async def get_requirement_by_id(node_id: int, project_id: int ,db:NodeDB = Depends(get_node_db)):
    try:
        requirement = await db.get_requirement_by_id(node_id, project_id)
        if requirement:
            ui_metadata = data_model["model"][requirement.get("properties",{}).get("Type","Requirement")]["ui_metadata"]
            requirement["ui_metadata"] = ui_metadata
        return requirement
    
    except Exception as e:
        return JSONResponse(status_code=500, content={"Something went wrong"})

@router.get("/available_statuses", response_model=List[str])
async def get_available_statuses():
    """
    Get a list of available requirement statuses.
    """
    return [status.value for status in RequirementStatus]

@router.get("/available_priorities", response_model=List[str])
async def get_available_priorities():
    """
    Get a list of available priority types.
    """
    return [priority.value for priority in PriorityType]

@router.get("/tasks_count/{project_id}")
async def get_tasks_count(project_id: int, db:NodeDB = Depends(get_node_db)):
    """
    Get the total number of requirements in the system.
    """
    try:
        task_count_map = {
         "Total Tasks":0,
        }
        for status in RequirementStatus:
            task_count_map[status.value] = 0
        requirement_count = await db.get_requirements_count(project_id)
        if requirement_count:
            for record in requirement_count:
                task_count_map[record['status']] = record['count']
                task_count_map["Total Tasks"] += record['count']

        return task_count_map
                

    except Exception as e:
        print("Exception:",e)
        raise HTTPException(status_code=500, detail="Internal Server Error")

@router.get("/total_tasks/{project_id}")
async def get_total_tasks(project_id: int, db:NodeDB = Depends(get_node_db)):
    """
    Get the total number of requirements in the system.
    """
    try:
        requirement_count = await db.get_total_requirements(project_id)

        return requirement_count
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal Server Error")

@router.get("/task_count_by_status/{project_id}", response_model=int)
async def task_count_by_status(project_id: int, status: RequirementStatus, node_db: NodeDB = Depends(get_node_db)):
    """
    Get the count of tasks by status for a given project.
    """
    try:
        status = status.value
        result = await node_db.get_requirements_count_by_status(project_id, status)
        return result
    except Exception as e:
        print("Exception:",e)
        raise HTTPException(status_code=500, detail="Internal Server Error")




