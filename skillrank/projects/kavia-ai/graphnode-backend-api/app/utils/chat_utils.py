functions_deprecated = [
    {
        "type": "function",
        "function": {
            "name": "create_node",
            "description": "A node function to create like project and product etc..",
            "parameters": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Name of the node type"
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of the node type"
                    },
                    "node_type": {
                        "type": "string",
                        "description": "Type of the node. There are two types project and product node types"
                    }
                },
                "required": ['name', 'description', 'node_type']
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "update_node",
            "description": "A node function to update nodes like project and product etc..",
            "parameters": {
                "type": "object",
                "properties": {
                    "node_id": {
                        "type": "integer",
                        "description": "node id of the particular node"
                    },
                    "node_type": {
                        "type": "string",
                        "description": "Type of the node. There are two types project and product node types"
                    },
                    "name": {
                        "type": "string",
                        "description": "Name of the node type"
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of the node typ"
                    }
                },
                "required": ['node_id', 'node_type', 'name', 'description']
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_node",
            "description": "A node function to get the node like project and product etc..",
            "parameters": {
                "type": "object",
                "properties": {
                    "node_id": {
                        "type": "integer",
                        "description": "it's a node id"
                    },
                    "node_type": {
                        "type": "string",
                        "description": "type of the node"
                    }
                },
                "required": ['node_id', 'node_type']
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_nodes",
            "description": "A node function to get all the node type like project and product etc..",
            "parameters": {
                "type": "object",
                "properties": {
                    "node_type": {
                        "type": "string",
                        "description": "type of the node"
                    }
                },
                "required": ['node_type']

            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "delete_node",
            "description": "A node function to delete the node like project and product etc..",
            "parameters": {
                "type": "object",
                "properties": {
                    "node_id": {
                        "type": "integer",
                        "description": "it's a node id"
                    },
                    "node_type": {
                        "type": "string",
                        "description": "Name of the node type"
                    }

                },
                "required": ['node_id', 'node_type']
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "delete_node",
            "description": "A node function to delete the node like project and product etc..",
            "parameters": {
                "type": "object",
                "properties": {
                    "node_id": {
                        "type": "integer",
                        "description": "it's a node id"
                    },
                    "node_type": {
                        "type": "string",
                        "description": "Name of the node type"
                    }

                },
                "required": ['node_id', 'node_type']
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_workitem",
            "description": "Create a work item for project",
            "parameters": {
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Title of the work item"
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of the work item"
                    },
                    "project_id": {
                        "type": "integer",
                        "description": "it was a project id"
                    }
                },
                "required": ['title', 'description', 'project_id']

            }
        }
    },
    {
        "type": "function",
        "function":  {
            "name": "delete_workitem",
            "description": "A function to delete the worktiem for project",
            "parameters": {
                "type": "object",
                "properties": {
                    "workitem_id": {
                        "type": "integer",
                        "description": "it's a work item id"
                    }
                },
                "required": ['workitem_id']
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_requirement",
            "description": "Create a requirement for product",
            "parameters": {
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Title of the requirement"
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of the requirement"
                    },
                    "product_id": {
                        "type": "integer",
                        "description": "it was a product id"
                    }
                },
                "required": ['title', 'description', 'product_id']
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "delete_requirement",
            "description": "A function to delete the requirement for product",
            "parameters": {
                "type": "object",
                "properties": {
                    "requirement_id": {
                        "type": "integer",
                        "description": "it's a requirement id"
                    }
                },
                "required": ['requirement_id']
            }
        }
    }
]

functions = [
    {
        "type": "function",
        "function": {
            "name": "get_nodes",
            "description": "A node function to get all the node type like project, requirement, architecture.",
            "parameters": {
                "type": "object",
                "properties": {
                    "node_type": {
                        "type": "string",
                        "description": "type of the node"
                    }
                },
                "required": ['node_type']
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_project",
            "description": "A function to create a project.",
            "parameters": {
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Title of the project"
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of the project"
                    }
                },
                "required": ['title', 'description']
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_requirement",
            "description": "A function to create a requirement.",
            "parameters": {
                "type": "object",
                "properties": {
                    "project_name": {
                      "type": "string",
                      "description": "Project name of the requirement"  
                    },
                    "title": {
                        "type": "string",
                        "description": "Title of the requirement"
                    },
                    "description": {
                        "type": "string",
                        "description": "Description of the requirement"
                    }
                },
                "required": ['project_name', 'title', 'description']
            },
        }
    },
]
