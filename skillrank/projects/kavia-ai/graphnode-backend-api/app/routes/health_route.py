from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status
import asyncio
import time
import logging
from typing import Dict, Any, List

from app.core.Settings import settings
from app.connection.establish_db_connection import (
    get_node_db, get_vector_db, get_mongo_db
)
from app.connection.llm_init import get_llm_interface

# Import AWS Bedrock client
import boto3
from botocore.exceptions import ClientError

# Import OpenAI client
import openai

router = APIRouter(tags=["health"])

logger = logging.getLogger(__name__)

async def check_mongodb_connection() -> Dict[str, Any]:
    """Check MongoDB connection health."""
    start_time = time.time()
    try:
        mongo_db = get_mongo_db(collection_name='health_check')
        # Simple ping to verify connection
        result = mongo_db.client.admin.command('ping')
        response_time = time.time() - start_time
        return {
            "status": "healthy" if result.get("ok") == 1.0 else "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "details": "MongoDB connection successful"
        }
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"MongoDB health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "error": str(e)
        }

async def check_neo4j_connection() -> Dict[str, Any]:
    """Check Neo4j connection health."""
    start_time = time.time()
    try:
        node_db = get_node_db()
        # Simple query to verify connection
        result = await node_db.async_run("RETURN 1 as test")
        response_time = time.time() - start_time
        return {
            "status": "healthy",
            "response_time_ms": round(response_time * 1000, 2),
            "details": "Neo4j connection successful"
        }
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Neo4j health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "error": str(e)
        }

async def check_bedrock_connection() -> Dict[str, Any]:
    """Check AWS Bedrock connection health."""
    start_time = time.time()
    try:
        # Create Bedrock Runtime client
        bedrock_runtime = boto3.client(
            service_name="bedrock-runtime",
            region_name=settings.AWS_REGION
        )
        
        # Simple ping to verify connection - just get the service metadata
        # This is a lightweight operation to check connectivity
        response = bedrock_runtime.meta.service_model.service_name
        
        response_time = time.time() - start_time
        return {
            "status": "healthy",
            "response_time_ms": round(response_time * 1000, 2),
            "details": "AWS Bedrock connection successful"
        }
    except ClientError as e:
        response_time = time.time() - start_time
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        error_message = e.response.get("Error", {}).get("Message", str(e))
        logger.error(f"AWS Bedrock health check failed: {error_code} - {error_message}")
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "error": f"{error_code}: {error_message}"
        }
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"AWS Bedrock health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "error": str(e)
        }

async def check_openai_connection() -> Dict[str, Any]:
    """Check OpenAI API connection health."""
    start_time = time.time()
    try:
        # Initialize OpenAI client with API key
        client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        
        # Simple models list to verify connection
        # This is a lightweight operation to check connectivity
        models = client.models.list()
        
        response_time = time.time() - start_time
        return {
            "status": "healthy",
            "response_time_ms": round(response_time * 1000, 2),
            "details": "OpenAI API connection successful"
        }
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"OpenAI API health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "error": str(e)
        }

async def check_vertex_ai_connection() -> Dict[str, Any]:
    """Check Google Vertex AI connection health."""
    start_time = time.time()
    try:
        # Import Vertex AI libraries only when needed to avoid dependency issues
        try:
            from google.cloud import aiplatform
            from google.cloud.aiplatform.gapic.schema import predict
        except ImportError:
            return {
                "status": "skipped",
                "response_time_ms": 0,
                "details": "Google Vertex AI libraries not installed"
            }
        
        # Initialize Vertex AI client
        aiplatform.init(
            project=settings.GOOGLE_CLOUD_PROJECT,
            location=settings.GOOGLE_CLOUD_LOCATION
        )
        
        # Simple operation to verify connection
        endpoints = aiplatform.Endpoint.list(max_results=1)
        
        response_time = time.time() - start_time
        return {
            "status": "healthy",
            "response_time_ms": round(response_time * 1000, 2),
            "details": "Google Vertex AI connection successful"
        }
    except Exception as e:
        response_time = time.time() - start_time
        logger.error(f"Google Vertex AI health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time * 1000, 2),
            "error": str(e)
        }

@router.get("/health", summary="Health check endpoint for Kubernetes")
async def health_check():
    """
    Health check endpoint for Kubernetes.
    
    This endpoint checks the health of all external dependencies:
    - MongoDB
    - Neo4j
    - AWS Bedrock
    - OpenAI API
    - Google Vertex AI (if configured)
    
    Returns:
        dict: Health status of all services
    """
    # Run all health checks concurrently
    checks = await asyncio.gather(
        check_mongodb_connection(),
        check_neo4j_connection(),
        check_bedrock_connection(),
        check_openai_connection(),
        check_vertex_ai_connection(),
        return_exceptions=True
    )
    
    # Process results
    results = {
        "mongodb": checks[0] if not isinstance(checks[0], Exception) else {"status": "unhealthy", "error": str(checks[0])},
        "neo4j": checks[1] if not isinstance(checks[1], Exception) else {"status": "unhealthy", "error": str(checks[1])},
        "aws_bedrock": checks[2] if not isinstance(checks[2], Exception) else {"status": "unhealthy", "error": str(checks[2])},
        "openai": checks[3] if not isinstance(checks[3], Exception) else {"status": "unhealthy", "error": str(checks[3])},
        "vertex_ai": checks[4] if not isinstance(checks[4], Exception) else {"status": "unhealthy", "error": str(checks[4])},
    }
    
    # Determine overall status
    # For Kubernetes, we'll consider the service healthy if critical dependencies are working
    critical_services = ["mongodb", "neo4j"]  # Define which services are critical
    critical_healthy = all(results[service]["status"] == "healthy" for service in critical_services)
    
    # Calculate response times
    response_times = [
        results[service].get("response_time_ms", 0) 
        for service in results 
        if results[service].get("status") == "healthy"
    ]
    avg_response_time = sum(response_times) / len(response_times) if response_times else 0
    
    # Prepare response
    response = {
        "status": "healthy" if critical_healthy else "unhealthy",
        "timestamp": time.time(),
        "services": results,
        "avg_response_time_ms": round(avg_response_time, 2)
    }
    
    # If unhealthy, return appropriate status code
    if not critical_healthy:
        return HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=response
        )
    
    return response

@router.get("/health/readiness", summary="Readiness probe for Kubernetes")
async def readiness_probe():
    """
    Readiness probe for Kubernetes.
    
    This endpoint checks if the application is ready to serve traffic.
    It performs a lightweight check of critical dependencies.
    
    Returns:
        dict: Readiness status
    """
    # Check only critical services for readiness
    try:
        # Check MongoDB connection
        mongo_result = await check_mongodb_connection()
        
        # Check Neo4j connection
        neo4j_result = await check_neo4j_connection()
        
        # Determine if ready based on critical services
        is_ready = (
            mongo_result["status"] == "healthy" and
            neo4j_result["status"] == "healthy"
        )
        
        response = {
            "status": "ready" if is_ready else "not_ready",
            "timestamp": time.time(),
            "services": {
                "mongodb": mongo_result,
                "neo4j": neo4j_result
            }
        }
        
        if not is_ready:
            return HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=response
            )
        
        return response
    
    except Exception as e:
        logger.error(f"Readiness probe failed: {str(e)}")
        return HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"status": "not_ready", "error": str(e)}
        )

@router.get("/health/liveness", summary="Liveness probe for Kubernetes")
async def liveness_probe():
    """
    Liveness probe for Kubernetes.
    
    This endpoint checks if the application is alive.
    It's a lightweight check that doesn't connect to external services.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": time.time(),
        "version": getattr(settings, "APP_VERSION", "unknown")
    }
