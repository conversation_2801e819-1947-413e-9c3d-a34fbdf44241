{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert DevOps engineer specializing in AWS infrastructure, Docker containerization, and CI/CD workflows. 
Your task is to create "TerraformFiles", "DockerFiles" and "WorkflowFiles" which will be used in deploying an app

IMPORTANT:
- Each Type field MUST match exactly: "TerraformFiles", "DockerFiles", or "WorkflowFiles"
- All files must be properly escaped JSON strings
- All file content must be complete and production-ready
- Do not add any additional fields or nodes
{% endblock %}

{% block autoconfig %}
Generate a complete deployment configuration following these requirements:

1. TerraformFiles node must include:
   - main.tf: 
     * VPC configuration
     * Subnet setup
     * Security groups
     * Load balancer
     * ECS cluster and service
     * ECR repository
     * IAM roles and policies

   - variables.tf:
     * Region configuration
     * Environment variables
     * Resource naming
     * Capacity settings

   - outputs.tf:
     * VPC details
     * Load balancer DNS
     * Repository URLs
     * Cluster information

   - providers.tf:
     * AWS provider configuration
     * Required provider versions
     * Provider features

2. DockerFiles node must include:
   - dockerfile:
     * Multi-stage build
     * Base image selection
     * Dependencies installation
     * Build process
     * Runtime configuration
     * Security considerations

   - docker_compose:
     * Service definitions
     * Volume mappings
     * Environment variables
     * Network configuration
     * Resource limits

3. WorkflowFiles node must include:
   - github_workflow:
     * Complete CI/CD pipeline
     * Build stages
     * Test execution
     * Security scanning
     * Docker image building
     * Infrastructure deployment
     * Application deployment
     * Health checks

IMPORTANT: 
- Each configuration must be complete and production-ready
- Include comprehensive comments
- Follow security best practices
- Use proper formatting
- Include error handling
- Provide monitoring and logging

Generate ALL files with complete, working content in the exact structure shown above.
{% endblock %}


I'll help you create a complete deployment configuration including infrastructure, containers, and CI/CD pipeline.

Let's start by gathering requirements for each component:

1. Infrastructure (Terraform Files):
   - What AWS resources do you need?
   - What networking configuration is required?
   - What security measures should be implemented?
   - What's the expected scale of the deployment?

2. Container Configuration (Docker Files):
   - What's the base image requirement?
   - What dependencies need to be installed?
   - What build stages are needed?
   - What runtime configurations are required?

3. CI/CD Pipeline (GitHub Workflow):
   - What testing stages should be included?
   - What security checks are needed?
   - What's the deployment strategy?
   - What post-deployment verifications are required?

Based on your requirements, I'll help create complete configurations for all components ensuring they work together seamlessly.

Remember: The output will include ALL configurations in three separate nodes with complete, production-ready content.


{% block task_description_post_script %}
Remember: You MUST include three separate nodes with proper Type values:
1. "TerraformFiles" for infrastructure
2. "DockerFiles" for containers
3. "WorkflowFiles" for CI/CD

Each node must have ALL its required files with complete content.
{% endblock %}