from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum
import uuid

class ExtractionTypes(Enum):
    Figma = "figma"
    Images = "images"

class ImageTemplate(BaseModel):

    filename: str
    size: int
    file_type: str
    base64url: str
    error_message: str
    file_id : str = Field(default_factory=lambda: str(uuid.uuid4())[:8])

class UserRequest(BaseModel):
    user_message: str
    file_attachments:Optional[list] = Field(default_factory=[])

class FigmaExtractionRequest(BaseModel):
    discussion_id: str
    user_request: UserRequest
    # optional
    extraction_type: Optional[ExtractionTypes] = ExtractionTypes.Figma
    selected_design_id: Optional[str] = None
    selected_frame: Optional[str] = None



class FrameLink(BaseModel):
    figma_link: str
    name: Optional[str] = None
    sizes: Optional[dict] = None
    added_by: Optional[dict] = None
    unlink: Optional[bool] = False
    