# app/utils/figma_utils.py

import requests
import re
import time
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
from app.connection.tenant_middleware import figma_access_token
import json
import httpx
from app.models.uiux.figma_model import (
    FigmaSizesModel,
    ProcessingStatus,
    FigmaFrameModel,
)
import asyncio
from app.utils.datetime_utils import generate_timestamp


def get_figma_access_token():
    return figma_access_token.get()

def retry_request(func):
    def wrapper(*args, **kwargs):
        max_retries = 5
        retry_delay = 1
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise HTTPException(status_code=500, detail=f"Failed after {max_retries} attempts: {str(e)}")
                print(f"Request failed, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
    return wrapper

def extract_file_key(link: str) -> Optional[str]:
    """Extract the Figma file key from a given link."""
    file_match = re.search(r'file/([^/]+)', link)
    if file_match:
        return file_match.group(1)

    design_match = re.search(r'design/([^/]+)', link)
    if design_match:
        return design_match.group(1)

    return None

def extract_frame_data(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract frame data from the Figma API response recursively."""
    frames = []
    
    def find_frames(node: Dict[str, Any]):
        if node.get("type") == "FRAME":
            frames.append(node)
        
        if "children" in node:
            for child in node["children"]:
                find_frames(child)
    
    if figma_data.get("document"):
        find_frames(figma_data["document"])
        
    return frames
def extract_all_node_data(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract all node data from the Figma API response recursively."""
    nodes = []
    
    def find_nodes(node: Dict[str, Any]):
        # Add ALL nodes, not just frames
        nodes.append(node)
        
        if "children" in node:
            for child in node["children"]:
                find_nodes(child)
    
    if figma_data.get("document"):
        find_nodes(figma_data["document"])
        
    return nodes


def extract_file_key_v1(figma_link: str) -> str:
    # Updated pattern to handle wider range of characters in file key
    match = re.search(r"file/([^/]+)", figma_link)
    return match.group(1) if match else None


def extract_frame_data_v1(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    frames = []
    def traverse(node):
        if node.get("type") == "FRAME":
            frames.append({
                "id": node["id"],
                "name": node["name"],
                "type": node["type"]
            })
        for child in node.get("children", []):
            traverse(child)

    traverse(data["document"])
    return frames

def get_figma_file_data(figma_link: str, access_token: str) -> Dict[str, Any]:
    """
    Extract Figma file data including frames and their images
    
    Args:
        figma_link (str): Figma file URL
        access_token (str): Figma access token
    
    Returns:
        Dict containing frames with images, file key, and document data
    """
    # Extract file key from link
    file_key = extract_file_key_v1(figma_link)
    if not file_key:
        raise ValueError("Invalid Figma link")
    # Get file data
    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": access_token}

    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()
    data = response.json()

    # Extract frames
    frames = extract_frame_data_v1(data)
    frame_ids = [frame["id"] for frame in frames]

    # Get frame images
    image_url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": ",".join(frame_ids), "scale": 1, "format": "png"}
    img_response = requests.get(image_url, headers=headers, params=params, timeout=300)
    img_response.raise_for_status()
    image_urls = img_response.json()["images"]

    # Combine frames with their image URLs
    frames_with_images = [
        {**frame, "imageUrl": image_urls.get(frame["id"])}
        for frame in frames
    ]

    return {
        "frames": frames_with_images,
        "fileKey": file_key,
        "document": data["document"]
    }

@retry_request
def fetch_frame_images(file_key: str, frame_ids: List[str],limit:int=500) -> Dict[str, str]:
    """
    Fetch image URLs for multiple frames with retry logic and size validation.
    Ensures each image is under 500kb by adjusting scale if needed.
    """
    try:
        url = f"https://api.figma.com/v1/images/{file_key}"
        headers = {"X-Figma-Token": get_figma_access_token()}
        # Try different scales until images are under 500kb
        params = {"ids": ",".join(frame_ids)}
        response = requests.get(url, params=params, headers=headers, timeout=300)
        response.raise_for_status()
            
            # Check image sizes
        images = response.json()['images']
            
        return images
    except Exception as e:
        raise ValueError("Error while fetching images")

@retry_request
def fetch_frame_image(file_key: str, frame_id: str) -> Optional[str]:
    """Fetch image URL for a single frame with retry logic."""
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 2}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    return response.json()['images'].get(frame_id)

def get_frame_details(file_key: str, frame_id: str):
    # Fetch frame JSON data
    url = f"https://api.figma.com/v1/files/{file_key}/nodes?ids={frame_id}"
    headers = {"X-Figma-Token": get_figma_access_token()}
    
    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()
    frame_data = response.json()['nodes'][frame_id]['document']

    # Fetch frame image
    image_url = fetch_frame_image(file_key, frame_id)
    
    # Fetch frame thumbnail
    thumbnail_url = f"https://api.figma.com/v1/images/{file_key}?ids={frame_id}&scale=0.5&format=png"
    thumbnail_response = requests.get(thumbnail_url, headers=headers, timeout=300)
    thumbnail_response.raise_for_status()
    thumbnail_url = thumbnail_response.json()['images'].get(frame_id)

    return {
        "frame_id": frame_id,
        "file_key": file_key,
        "json_data": frame_data,
        "imageUrl": image_url,
        "thumbnailUrl": thumbnail_url
    }

def get_figma_file_data_limited(file_link, access_token, byte_limit=None, kb_limit=None, mb_limit=None):
    """
    Get Figma file data without size restrictions.
    
    Args:
        file_link (str): Figma file URL
        access_token (str): Figma access token
        byte_limit (int, optional): Ignored - kept for backward compatibility
        kb_limit (float, optional): Ignored - kept for backward compatibility
        mb_limit (float, optional): Ignored - kept for backward compatibility

    Returns:
        tuple: (response JSON data, size information dict)

    Raises:
        ValueError: If access token is invalid or file key cannot be extracted
    """
    if type(access_token) == str:
        if len(access_token.strip()) == 0:
            raise ValueError("Figma access token is required")
        
    file_key = extract_file_key(file_link)
    
    response = requests.get(
        f"https://api.figma.com/v1/files/{file_key}",
        headers={"X-Figma-Token": access_token},
        timeout=300
    )
    response.raise_for_status()
    
    data = response.json()
    
    # Calculate actual file size for informational purposes
    json_str = json.dumps(data)
    size_bytes = len(json_str.encode('utf-8'))
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    
    sizes = {
        "byte_limit": None,  # No limits applied
        "kb_limit": None,    # No limits applied
        "mb_limit": None,    # No limits applied
        "size_bytes": size_bytes,
        "size_kb": size_kb,
        "size_mb": size_mb
    }
    
    return data, sizes

def get_figma_file_size(data):
    """Calculate the size of Figma file data in bytes, KB, and MB"""
    json_str = json.dumps(data)
    size_bytes = len(json_str.encode('utf-8'))
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    return {
        "size_bytes": size_bytes,
        "size_kb": size_kb,
        "size_mb": size_mb
    }

async def get_figma_file_data_limited_async(client: httpx.AsyncClient, figma_link: str, figma_api_key: str, mb_limit: float = None) -> tuple:
    """
    Asynchronously fetch Figma file data with size limit check
    """
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": figma_api_key}

    # Check file size first
    try:
        head_response = await client.head(url, headers=headers)
        content_length = int(head_response.headers.get('content-length', 0))
        size_mb = content_length / (1024 * 1024)
        
        # if mb_limit and size_mb > mb_limit:
        #     raise ValueError(f"File size ({size_mb:.2f}MB) exceeds limit of {mb_limit}MB")
            
    except Exception as e:
        print(f"Warning: Could not check file size: {str(e)}")
        # Continue anyway since this is just a precaution

    # Get actual file data
    response = await client.get(url, headers=headers)
    response.raise_for_status()
    data = response.json()

    # Calculate sizes
    sizes = {
        'size_kb': round(len(str(data).encode('utf-8')) / 1024, 2),
        'size_mb': round(len(str(data).encode('utf-8')) / (1024 * 1024), 2),
        'byte_limit': mb_limit * 1024 * 1024 if mb_limit else None,
        'mb_limit': mb_limit
    }

    return data, sizes

async def fetch_frame_images_async(client: httpx.AsyncClient, file_key: str, frame_ids: List[str]) -> Dict[str, str]:
    """
    Asynchronously fetch frame images from Figma API
    """
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {
        "ids": ",".join(frame_ids),
        "scale": 2,
        "format": "png"
    }
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = await client.get(url, params=params, headers=headers)
    response.raise_for_status()
    return response.json().get('images', {})

async def process_frame(
    frame: dict, file_key: str, image_urls: Dict[str, str]
) -> FigmaFrameModel:
    """
    Process individual frame and return frame model with status.

    Args:
        frame (dict): Frame data from Figma API
        file_key (str): Figma file key
        image_urls (Dict[str, str]): Pre-fetched image URLs

    Returns:
        FigmaFrameModel: Processed frame model with status
    """
    try:
        # Initialize frame model with base data
        frame_model = FigmaFrameModel(
            id=frame["id"],
            name=frame.get("name", "Untitled Frame"),
            status=ProcessingStatus.PROCESSING,
            time_updated=generate_timestamp(),
        )

        # Validate frame data
        if not frame["id"]:
            frame_model.status = ProcessingStatus.FAILED
            frame_model.error_message = "Invalid frame ID"
            return frame_model

        # Try to get image URL
        image_url = image_urls.get(frame["id"])

        if not image_url:
            # If no pre-fetched URL, try to fetch individually
            try:
                image_url = fetch_frame_image(file_key, frame["id"])
                await asyncio.sleep(0.1)  # Small delay to prevent rate limiting
            except Exception as e:
                image_url = None

        if image_url:
            frame_model.imageUrl = image_url
            frame_model.status = ProcessingStatus.COMPLETED
        else:
            frame_model.status = ProcessingStatus.FAILED
            frame_model.error_message = "Failed to fetch frame image"

        # Add any additional frame metadata if available
        if frame.get("absoluteBoundingBox"):
            frame_model.metadata = {
                "width": frame["absoluteBoundingBox"].get("width"),
                "height": frame["absoluteBoundingBox"].get("height"),
            }

        return frame_model

    except Exception as e:
        # Return failed frame model on any error
        return FigmaFrameModel(
            id=frame.get("id", "unknown"),
            name=frame.get("name", "Error Frame"),
            status=ProcessingStatus.FAILED,
            error_message=f"Error processing frame: {str(e)}",
            time_updated=generate_timestamp(),
        )
