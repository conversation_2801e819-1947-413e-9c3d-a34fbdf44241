# app/services/enhanced_tracking_service.py
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional
import asyncio
from app.connection.establish_db_connection import get_mongo_db, MongoDBHandler
from ..models.tracking_model import APITrackingLog, DailyTenantStats
from app.services.geolocation_service import geolocation_service
from app.telemetry.logger_config import get_logger
import os
from app.core.Settings import settings
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
MONGO_URI = settings.MONGO_CONNECTION_URI
logger = get_logger(__name__)

class EnhancedTrackingService:
    def __init__(self):
        self.db_handler = None
        self.collection_name = "request_tracker"
    
    def _get_root_database_name(self) -> str:
        """Extract root database name from current database name following your pattern"""
        return KAVIA_ROOT_DB_NAME
            
    
    async def initialize(self):
        """Initialize the service with database connection"""
        try:         
            # Get root database name following your pattern
            self.root_db_name = self._get_root_database_name()
            
            # Initialize MongoDB handler for tracking
            self.db_handler = MongoDBHandler(
                mongo_connection_uri=MONGO_URI,  # Using existing connection
                db_name=self.root_db_name,
                collection_name=self.collection_name
            )
            
            # Set the root database
            self.db_handler.change_db(self.root_db_name)
            self.db_handler.change_collection(self.collection_name)
            
            logger.info(f"✅ Enhanced tracking service initialized")
            logger.info(f"📊 Root DB: {self.root_db_name}")
            logger.info(f"📝 Collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"❌ Error initializing tracking service: {str(e)}")
            raise
    
    async def store_tracking_data(self, log_data: APITrackingLog):
        """Store API tracking data with location information"""
        try:
            # Initialize if not already done
            if not self.db_handler:
                await self.initialize()
            
            # Get location information (only for non-localhost IPs)
            if not self._is_localhost_ip(log_data.client_ip):
                location_data = await geolocation_service.get_location(log_data.client_ip)
                if location_data:
                    log_data.country = location_data.get("country")
                    log_data.region = location_data.get("region") 
                    log_data.city = location_data.get("city")
                    log_data.latitude = location_data.get("latitude")
                    log_data.longitude = location_data.get("longitude")
                    log_data.timezone_name = location_data.get("timezone_name")
                    log_data.isp = location_data.get("isp")
            else:
                # Set default values for localhost
                # Set default values for localhost
               log_data.country = "Local"
               log_data.region = "Local"
               log_data.city = "Local"
           
            # Get the database connection following your pattern
            mongo_client = get_mongo_db()
            root_db = mongo_client.client[self.root_db_name]
            
            # Store main log using your MongoDB handler
            log_dict = log_data.dict()
            
            # Log the exact database and collection being used
            logger.info(f"💾 Storing tracking data:")
            # logger.info(f"   Current DB Context: {current_db_name}")
            logger.info(f"   Root DB: {self.root_db_name}")
            logger.info(f"   Collection: {self.collection_name}")
            logger.info(f"   Document: Tenant={log_data.tenant_id}, User={log_data.user_email}, Endpoint={log_data.method} {log_data.endpoint}")
            
            await self.db_handler.insert(log_dict, root_db)
            
            logger.info(f"✅ Successfully stored tracking data to {self.root_db_name}.{self.collection_name}")
            
            # Update aggregations asynchronously (optional, but skip errors)
            try:
                asyncio.create_task(self._update_daily_stats_safe(log_data, root_db))
            except Exception as e:
                logger.warning(f"⚠️  Daily stats update failed (non-critical): {str(e)}")
            
        except Exception as e:
            logger.error(f"❌ Error storing tracking data: {str(e)}")
            logger.error(f"❌ Target was: DB={getattr(self, 'root_db_name', 'unknown')}, Collection={self.collection_name}")
    
    def _is_localhost_ip(self, ip: str) -> bool:
        """Check if IP is localhost"""
        localhost_ips = ["127.0.0.1", "::1", "localhost", "0.0.0.0"]
        return ip in localhost_ips or ip.startswith("192.168.") or ip.startswith("10.") or ip.startswith("172.")
    
    async def _update_daily_stats_safe(self, log_data: APITrackingLog, root_db):
        """Safely update daily tenant statistics"""
        try:
            stats_collection = "daily_tenant_stats"
            
            # Create daily stats handler
            stats_handler = MongoDBHandler(
                mongo_connection_uri=None,
                db_name=self.root_db_name,
                collection_name=stats_collection
            )
            stats_handler.change_db(self.root_db_name)
            stats_handler.change_collection(stats_collection)
            
            # Update/create daily stats
            stats_filter = {
                "tenant_id": log_data.tenant_id,
                "date": log_data.date_partition
            }
            
            # Prepare update data
            update_data = {
                "tenant_id": log_data.tenant_id,
                "date": log_data.date_partition,
                "updated_at": datetime.now(timezone.utc)
            }
            
            # Use upsert to create or update
            await stats_handler.update_one(stats_filter, update_data, upsert=True, db=root_db)
            
        except Exception as e:
            logger.warning(f"Error updating daily stats (non-critical): {str(e)}")
    
    async def get_dashboard_data(self, tenant_id: str, days: int = 7) -> Dict:
        """Get comprehensive dashboard data for a tenant"""
        try:
            # Initialize if not already done
            if not self.db_handler:
                await self.initialize()
            
            # Get root database following your pattern
            mongo_client = get_mongo_db()
            
            # Get root database name
            root_db_name = self._get_root_database_name()
            root_db = mongo_client.client[root_db_name]
            
            logger.info(f"📊 Getting dashboard data:")
            # logger.info(f"   Current DB Context: {current_db_context}")
            logger.info(f"   Root DB: {root_db_name}")
            logger.info(f"   Collection: {self.collection_name}")
            logger.info(f"   Tenant: {tenant_id}")
            
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            # Get data using simple operations
            summary_data = await self._get_summary_metrics_simple(tenant_id, start_date, end_date, root_db)
            daily_stats = await self._get_daily_stats_simple(tenant_id, start_date, end_date, root_db)
            hourly_trends = await self._get_hourly_trends_simple(tenant_id, start_date, end_date, root_db)
            top_endpoints = await self._get_top_endpoints_simple(tenant_id, start_date, end_date, root_db)
            top_users = await self._get_top_users_simple(tenant_id, start_date, end_date, root_db)
            status_dist = await self._get_status_distribution_simple(tenant_id, start_date, end_date, root_db)
            geographic_data = await self._get_geographic_data_simple(tenant_id, start_date, end_date, root_db)
            
            logger.info(f"✅ Retrieved dashboard data: {summary_data.get('total_requests', 0)} requests found")
            
            return {
                "tenant_id": tenant_id,
                "period": {
                    "days": days,
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                },
                "summary": summary_data,
                "daily_stats": daily_stats,
                "hourly_trends": hourly_trends,
                "top_endpoints": top_endpoints,
                "top_users": top_users,
                "status_distribution": status_dist,
                "geographic_data": geographic_data
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting dashboard data: {str(e)}")
            return self._get_empty_dashboard_data(tenant_id, days)
    
    async def _get_summary_metrics_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> Dict:
        """Get summary metrics using simple MongoDB operations"""
        try:
            # Use direct collection access
            collection = db[self.collection_name]
            
            # Count total requests
            total_requests = collection.count_documents({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            })
            
            logger.debug(f"📊 Found {total_requests} requests for tenant {tenant_id}")
            
            if total_requests == 0:
                return {
                    "total_requests": 0,
                    "unique_users": 0,
                    "avg_response_time": 0,
                    "total_errors": 0,
                    "error_rate": 0
                }
            
            # Get all documents for calculations
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Calculate metrics
            unique_users = len(set(doc.get("user_id", "") for doc in docs))
            total_response_time = sum(doc.get("duration_ms", 0) for doc in docs)
            avg_response_time = total_response_time / total_requests if total_requests > 0 else 0
            total_errors = sum(1 for doc in docs if doc.get("status_code", 200) >= 400)
            error_rate = (total_errors / total_requests) * 100 if total_requests > 0 else 0
            
            return {
                "total_requests": total_requests,
                "unique_users": unique_users,
                "avg_response_time": round(avg_response_time, 2),
                "total_errors": total_errors,
                "error_rate": round(error_rate, 2)
            }
            
        except Exception as e:
            logger.error(f"Error getting summary metrics: {str(e)}")
            return {
                "total_requests": 0,
                "unique_users": 0,
                "avg_response_time": 0,
                "total_errors": 0,
                "error_rate": 0
            }
    
    async def _get_daily_stats_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get daily statistics using simple operations"""
        try:
            collection = db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Group by date
            daily_data = {}
            for doc in docs:
                date = doc.get("date_partition")
                if date not in daily_data:
                    daily_data[date] = {
                        "requests": 0,
                        "errors": 0,
                        "response_times": [],
                        "users": set()
                    }
                
                daily_data[date]["requests"] += 1
                daily_data[date]["users"].add(doc.get("user_id", ""))
                daily_data[date]["response_times"].append(doc.get("duration_ms", 0))
                if doc.get("status_code", 200) >= 400:
                    daily_data[date]["errors"] += 1
            
            # Convert to list format
            result = []
            for date, data in daily_data.items():
                avg_response_time = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
                error_rate = (data["errors"] / data["requests"]) * 100 if data["requests"] > 0 else 0
                
                result.append({
                    "date": date,
                    "requests": data["requests"],
                    "unique_users": len(data["users"]),
                    "avg_response_time": round(avg_response_time, 2),
                    "errors": data["errors"],
                    "error_rate": round(error_rate, 2)
                })
            
            return sorted(result, key=lambda x: x["date"])
            
        except Exception as e:
            logger.error(f"Error getting daily stats: {str(e)}")
            return []
    
    async def _get_hourly_trends_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get hourly trends using simple operations"""
        try:
            collection = db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Count by hour
            hourly_counts = {}
            for doc in docs:
                hour = doc.get("hour_partition", 0)
                hourly_counts[hour] = hourly_counts.get(hour, 0) + 1
            
            # Create 24-hour array
            result = []
            for hour in range(24):
                result.append({
                    "hour": f"{hour:02d}:00",
                    "requests": hourly_counts.get(hour, 0)
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting hourly trends: {str(e)}")
            return [{"hour": f"{h:02d}:00", "requests": 0} for h in range(24)]
    
    async def _get_top_endpoints_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get top endpoints using simple operations"""
        try:
            collection = db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Group by endpoint
            endpoint_data = {}
            for doc in docs:
                endpoint = f"{doc.get('method', 'GET')} {doc.get('endpoint', '')}"
                if endpoint not in endpoint_data:
                    endpoint_data[endpoint] = {
                        "requests": 0,
                        "response_times": [],
                        "errors": 0
                    }
                
                endpoint_data[endpoint]["requests"] += 1
                endpoint_data[endpoint]["response_times"].append(doc.get("duration_ms", 0))
                if doc.get("status_code", 200) >= 400:
                    endpoint_data[endpoint]["errors"] += 1
            
            # Convert to list and sort
            result = []
            for endpoint, data in endpoint_data.items():
                avg_response_time = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
                error_rate = (data["errors"] / data["requests"]) * 100 if data["requests"] > 0 else 0
                
                result.append({
                    "endpoint": endpoint,
                    "requests": data["requests"],
                    "avg_response_time": round(avg_response_time, 2),
                    "error_rate": round(error_rate, 2)
                })
            
            return sorted(result, key=lambda x: x["requests"], reverse=True)[:10]
            
        except Exception as e:
            logger.error(f"Error getting top endpoints: {str(e)}")
            return []
    
    async def _get_top_users_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get top users using simple operations"""
        try:
            collection = db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Group by user
            user_data = {}
            for doc in docs:
                user_id = doc.get("user_id", "")
                if user_id not in user_data:
                    user_data[user_id] = {
                        "requests": 0,
                        "user_email": doc.get("user_email", ""),
                        "username": doc.get("username", ""),
                        "user_role": doc.get("user_role", "")
                    }
                
                user_data[user_id]["requests"] += 1
            
            # Convert to list and sort
            result = []
            for user_id, data in user_data.items():
                result.append({
                    "_id": user_id,
                    "requests": data["requests"],
                    "user_email": data["user_email"],
                    "username": data["username"],
                    "user_role": data["user_role"]
                })
            
            return sorted(result, key=lambda x: x["requests"], reverse=True)[:10]
            
        except Exception as e:
            logger.error(f"Error getting top users: {str(e)}")
            return []
    
    async def _get_status_distribution_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> Dict:
        """Get status distribution using simple operations"""
        try:
            collection = db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Count by status code
            status_counts = {}
            for doc in docs:
                status = str(doc.get("status_code", 200))
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return status_counts
            
        except Exception as e:
            logger.error(f"Error getting status distribution: {str(e)}")
            return {}
    
    async def _get_geographic_data_simple(self, tenant_id: str, start_date: datetime, end_date: datetime, db) -> List[Dict]:
        """Get geographic data using simple operations"""
        try:
            collection = db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find({
                "tenant_id": tenant_id,
                "created_at": {"$gte": start_date, "$lte": end_date}
            }))
            
            # Group by location
            location_data = {}
            for doc in docs:
                country = doc.get("country", "Unknown")
                region = doc.get("region", "Unknown")
                key = f"{country}|{region}"
                
                if key not in location_data:
                    location_data[key] = {
                        "country": country,
                        "region": region,
                        "requests": 0
                    }
                
                location_data[key]["requests"] += 1
            
            # Convert to list and sort
            result = []
            for data in location_data.values():
                if data["country"] not in ["Unknown", "Local"]:  # Skip unknown/local locations
                    result.append({
                        "_id": {
                            "country": data["country"],
                            "region": data["region"]
                        },
                        "requests": data["requests"]
                    })
            
            return sorted(result, key=lambda x: x["requests"], reverse=True)[:20]
            
        except Exception as e:
            logger.error(f"Error getting geographic data: {str(e)}")
            return []
    
    def _get_empty_dashboard_data(self, tenant_id: str, days: int) -> Dict:
        """Return empty dashboard data structure"""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        return {
            "tenant_id": tenant_id,
            "period": {
                "days": days,
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d")
            },
            "summary": {
                "total_requests": 0,
                "unique_users": 0,
                "avg_response_time": 0,
                "total_errors": 0,
                "error_rate": 0
            },
            "daily_stats": [],
            "hourly_trends": [{"hour": f"{h:02d}:00", "requests": 0} for h in range(24)],
            "top_endpoints": [],
            "top_users": [],
            "status_distribution": {},
            "geographic_data": []
        }
    
    async def get_all_tenants(self, current_db_context: str = None) -> List[Dict]:
        """Get all tenants with basic stats"""
        try:
            # Initialize if not already done
            if not self.db_handler:
                await self.initialize()
            
            # Get root database following your pattern
            mongo_client = get_mongo_db()
            
            root_db_name = self._get_root_database_name()
            root_db = mongo_client.client[root_db_name]
            
            collection = root_db[self.collection_name]
            
            # Get all documents
            docs = list(collection.find())
            
            logger.info(f"📊 Found {len(docs)} total tracking documents across all tenants")
            
            # Group by tenant
            tenant_data = {}
            for doc in docs:
                tenant_id = doc.get("tenant_id", "unknown")
                if tenant_id not in tenant_data:
                    tenant_data[tenant_id] = {
                        "total_requests": 0,
                        "users": set(),
                        "response_times": [],
                        "errors": 0,
                        "tenant_name": doc.get("tenant_name", tenant_id)
                    }
                
                tenant_data[tenant_id]["total_requests"] += 1
                tenant_data[tenant_id]["users"].add(doc.get("user_id", ""))
                tenant_data[tenant_id]["response_times"].append(doc.get("duration_ms", 0))
                if doc.get("status_code", 200) >= 400:
                    tenant_data[tenant_id]["errors"] += 1
            
            # Convert to list
            result = []
            for tenant_id, data in tenant_data.items():
                avg_response_time = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
                error_rate = (data["errors"] / data["total_requests"]) * 100 if data["total_requests"] > 0 else 0
                
                result.append({
                    "id": tenant_id,
                    "name": data["tenant_name"],
                    "total_requests": data["total_requests"],
                    "unique_users": len(data["users"]),
                    "avg_response_time": round(avg_response_time, 2),
                    "error_count": data["errors"],
                    "error_rate": round(error_rate, 2)
                })
            
            logger.info(f"✅ Retrieved {len(result)} tenants with stats")
            return sorted(result, key=lambda x: x["total_requests"], reverse=True)
            
        except Exception as e:
            logger.error(f"Error  all tenants: {str(e)}")
            return []

    # Global service instance
enhanced_tracking_service = EnhancedTrackingService()