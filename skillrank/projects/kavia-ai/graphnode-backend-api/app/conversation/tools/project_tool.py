from datetime import datetime
from app.connection.establish_db_connection import connect_mongo_db, get_node_db
from llm_wrapper.utils.base_tools import BaseTool
from app.core.Settings import settings
from app.routes.node_route import get_node
from app.connection.establish_db_connection import get_mongo_db

class ProjectTools(BaseTool):
    
    global mongo_handler
    mongo_handler = get_mongo_db(collection_name='confirmation')
    
    def __init__(self, base_path, logger, llm=None, user_id=None, discussion=None):
        super().__init__(logger)
        self.user_id = user_id
        self.node_db = get_node_db()
        self.function_schemas = [
            {
                "type": "function",
                "function": {
                    "name": "create_project",
                    "description": "Create a new project with the given title and description and scope",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string", "description": "The title of the project"},
                            "description": {"type": "string", "description": "A brief description of the project"},
                            "scope": {"type": "string", "scope": "A brief scope of the project"}
                        },
                        "required": ["title", "description","scope"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "update_project",
                    "description": "Update the project based on title.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "project_title": {"type": "string", "description": "The title of the project"},
                            "project_new_title": {"type": "string", "description": "The new project title"},
                            "description": {"type": "string", "description": "New description for the project"},
                            "scope": {"type": "string", "scope": "New scope for the project"}
                        },
                        "required": ["project_title", "project_new_title"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_all_projects",
                    "description": "A function to list all the projects",
                    "parameters": {}
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "delete_project",
                    "description": "A function to delete the project using name or project_id",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "project_title": {"type": "string", "description": "name of the project"},
                            "project_id": {"type": "integer", "description": "id of the project"}
                        },
                        "required": ["project_title"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_project_details",
                    "description": "A function to get the project details using name",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "project_title": {"type": "string", "description": "name of the project"},
                            "project_id": {"type": "integer", "description": "id of the project"}
                        },
                        "required": ["project_title"]
                    }
                }
            }
        ]

        self.function_mapping.update({
            "create_project": self.create_project,
            "update_project": self.update_project,
            "get_all_projects": self.get_all_projects,
            "delete_project": self.delete_project,
            "get_project_details": self.get_project_details
        })

    async def create_project(self, title, description, scope):
        params = {
            'request': {
                'node_type': 'Project',
                'name': title,
                'description': description,
                'scope': scope,
            }
        }

        details = {
            'Node Type': 'Project',
            'Name': title,
            'Description': description,
            'Scope': scope

        }
            
        asking_confirmation = {
            'operation': 'create',
            'function': 'create_node',
            'type': 'Project',
            'is_active': 1,
            'parameters': params,
            'details': details
        }
        
        mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

        # append the mongo_result _id as the task_id to that data variable
        asking_confirmation["task_id"] = str(mongo_result['_id'])
        
        return {'status': 200, 'message': asking_confirmation, 'skip_last_call': True}
    
    async def update_project(self, project_title, project_new_title, description=None , scope=None):

        project = await self.node_db.get_node_by_property('Project', 'Title', project_title)
        
        if project:
            node_id = project['id']
            name = project_new_title
            if description is None:
                description = project['properties']['Description']
                scope = project['properties']['Scope']
    
            node_type = 'Project'
            print("node_id", node_id)

            params = {
                'node_id': node_id,
                'request': {
                    'node_type': 'Project',
                    'properties': {
                        'Name': name,
                        'Description': description,
                        'Scope': scope
                    }
                }

            }

            details = {
                'Node Type': node_type,
                'Name': name,
                'Description': description,
                'Scope': scope
            }

            asking_confirmation = {
                'operation': 'update',
                'function': 'update_node',
                'type': node_type,
                'is_active': 1,
                'parameters': params,
                'details': details
            }

            mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

            # append the mongo_result _id as the task_id to that data variable
            asking_confirmation["task_id"] = str(mongo_result['_id'])

            return {'status': 200, 'message': asking_confirmation, 'skip_last_call': True}

        
        else:
            return "project not found, please provide the current name"
    
    async def get_all_projects(self,):
        result = await self.node_db.get_nodes_by_label('Project')
        if result:
            items = 'create it as like for the user\n'
            for project in result:
                print(project['id'])
                items += f"ID: {project['id']}, Title: \"{project['properties']['Title']}\"\n"
            
            return {'status': 1, 'message': items}
        else:
            return {'status': 404, 'message': 'No projects found'}
    
    async def get_project_details(self,project_title):
        projects = await self.node_db.get_node_by_property('Project', 'Title', project_title)
        if projects:
            return {'status': 1, 'message': projects}
        else:
            return {'status': 404, 'message': 'No projects found'}
        
    async def delete_project(self, project_title):
        '''
        Search for the project using different case variations before deleting.
        '''
        case_variations = [
            project_title,
            project_title.lower(),
            project_title.upper(),
            project_title.capitalize()
        ]

        project = None
        for title_variation in case_variations:
            project = await self.node_db.get_node_by_property('Project', 'Title', title_variation)
            if project:
                break

        if project:
            node_id = project['id']
            node_type = 'Project'

            params = {
                'node_id': node_id,
                'node_type': 'Project'
            }

            details = {
                'Id': node_id,
                'Name': project_title
            }

            asking_confirmation = {
                'operation': 'delete',
                'function': 'delete_node',
                'type': node_type,
                'is_active': 1,
                'parameters': params,
                'details': details
            }
            
            mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

            # append the mongo_result _id as the task_id to that data variable
            asking_confirmation["task_id"] = str(mongo_result['_id'])

            return {'status': 200, 'message': asking_confirmation, 'skip_last_call': True}
        else:
            return {'status': 404, 'message': "Project not found in the database"}