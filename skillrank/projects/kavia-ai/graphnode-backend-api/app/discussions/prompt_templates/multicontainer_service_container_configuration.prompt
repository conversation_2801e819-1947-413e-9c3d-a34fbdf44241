{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, configure this service container based on the C4 model:

{% else %}
You are an expert system architect reviewing the Service Container for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
     - Components: {{ bg_info.get('system_context', {}).get('components') | tojson(indent=2) }}

   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}


Analysis Instructions:

1. Impact Analysis:
   - Compare original and new functional and non-functional requirements, system_context, and project context.
   - Identify potential changes in user interactions and external system interactions.

2. Required Changes:
  - Modify Existing container, add or modify or remove any required components, corresponding interfaces based on the above analysis.
  - Follow the below guidelines as a reference while modifying the existing containers and its children.
  - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %} 

GENERIC GUIDELINES:

1. Provide a description of the service container - explaining its core functionalities, its role in the overall system, and its interaction with other containers, users, and external systems. 

2. Review the container's purpose and responsibilities, project details, and consider potential technology options for this container, such as:
   - Programming languages and frameworks
   - Infrastructure components
   - Integration technologies
   - Databases and storage solutions
   
3. Tech Stack Selection: 
   - Based on the technology options considered in the previous section for this container
   - Suggest the selected optimal technology stack and provide a brief rationale for your decision
   - Consider compatibility with other service containers in the system

4. User Interactions:
   - Identify which user types from the system context interact with this container
   - Define the container's role in supporting user needs
   
5. Create relationships with External Systems and Other Containers:
   - Review the available external containers and specify dependencies for this container
   - Identify which external systems this container interacts with
   - Identify which other service containers this container interacts with
   - Create USES Relationship with external containers and other service containers with Properties:
     * type: Fixed value "USES" indicating dependency
     * name: Service or API name that is being consumed
     * description: Purpose and functionality being used from the external service or other container
     * source id: Node id of the current container consuming the service - should be an integer
     * target id: Node id of the container providing the service - should be an integer
     * technology: Protocol or method used to integrate with the service

6. Component Creation:
   - By default, create EXACTLY ONE component that encapsulates all container functionality
   - This single component must address ALL the container's responsibilities
   - For new Component:
     * Assign ID 'NEW-ARCH-1'
     * Title: Should match or closely relate to the container's name
     * Description: Comprehensive explanation of ALL the component's responsibilities
     * Type: Component classification
   - Component Properties: The Component must have:
     * Title: Clear, descriptive name
     * Description: Detailed purpose and responsibilities
     * Type: Component classification

7. Define Interfaces:
   - This service container MUST define Provider interfaces that expose its functionality to other containers or external entities
   - Create these interfaces as child nodes of the container
   - Review the Functional Requirements carefully and ensure to expose software interfaces following industry best practices
   - For each interface, provide:
     * Title: Clear descriptive name
     * Description: Detailed description including API signatures where applicable
     * Type: "Interface" (always fixed)
     * InterfaceType: Appropriate interface type (REST, GraphQL, gRPC, etc.)
     * PublicAPIDetails: API specifications in OpenAPI format - capture OpenAPI specification wrapped in <JSON> tags 
       - Endpoints/methods
       - Request/response formats
       - Authentication requirements
       - Error handling
       - Sample usage examples
       - Any provided API documentation should be referenced and incorporated
   
   - IMPORTANT: Create these interfaces independent of other containers - focus on what functionality THIS container exposes

8. Map Implemented Requirements:
   - Analyze the Container Purpose and Responsibilities 
   - Review the requirements given below and identify the requirements that this container implements 
   - Add only those requirement node IDs to the ImplementedRequirementIDs property, as an array of integers 

9. Repository Name:
   - The container's repository name - provide suitable name based on its functionality
   - Repository Name: <container-name-in-kebab-case>

10. Container Type:
    - For multi-container-service architecture, set the ContainerType as "internal"

11. Generate a C4 Container diagram showing:
    - The container with its components
    - External system interactions
    - Other service container interactions
    - Key interfaces at container level

Example C4 Container Diagram - use this diagram for reference purpose only - generate suitable diagram based on the given container details.
{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}
```
graph TB
    %% External Container with Interface
    ExtContainer["External Container Name<br/>[Container: External]"]
    ExtInterface["External Interface<br/>[Interface]"]
    ExtContainer -->|"exposes"| ExtInterface

    %% Other Service Container
    OtherService["Other Service Container<br/>[Container: Service]"]
    ServiceInterface["Service Interface<br/>[Interface]"]
    OtherService -->|"exposes"| ServiceInterface

    %% Current Container being configured
    subgraph ThisContainer[Service Container Name]
        direction TB
        Component1["Component 1<br/>[Component]"]
        Component2["Component 2<br/>[Component]"]
        
        %% Internal Component Interactions
        Component1 -->|"uses"| Component2
    end

    %% This Container Interface
    ThisInterface["Container Interface<br/>[Interface]"]
    ThisContainer -->|"exposes"| ThisInterface

    %% USES relationships
    Component1 -->|"USES<br/>Service: Service Name<br/>Protocol: Protocol Type"| OtherService
    Component2 -->|"USES<br/>Service: External Service<br/>Protocol: Protocol Type"| ExtContainer
    
    %% Styling
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef component fill:#85bbf0,stroke:#5d82a8,color:#000000
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    classDef interface fill:#85bbf0,stroke:#5d82a8,color:#000000,stroke-dasharray: 5 5
    
    class ThisContainer container
    class Component1,Component2 component
    class ExtContainer,OtherService external
    class ThisInterface,ExtInterface,ServiceInterface interface
```



Change Needed: 
   - Set to False if changes are not required.

Change Log:
   - Capture history of changes.
{% endblock %}

{% block autoconfig %}
Create a comprehensive C4 container-level design with one or more components that provides a clear structural view of the service's organization while adhering to C4 modeling principles.
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them.
{% endblock %}

{% block auto_reconfig %}
Create updated container design based on the above guidelines. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them.
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Container configuration, including its components and interfaces, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing relationships with other containers: {{ details_for_discussion.get('existing_uses_relationships') | tojson(indent=2) }}
    Existing components within this container: {{ details_for_discussion.get('existing_components') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    System Context Information:
    Users: {{ details_for_discussion.system_context.users | join(', ') }}
    External Systems: {{ details_for_discussion.system_context.external_systems | join(', ') }}
    System Context Description: {{ details_for_discussion.system_context.description }}
    {{ details_for_discussion.project_details.architecture_strategy }}
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
{% endblock %} 