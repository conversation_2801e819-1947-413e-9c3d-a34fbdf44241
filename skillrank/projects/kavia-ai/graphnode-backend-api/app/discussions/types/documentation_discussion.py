from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
import logging
import inspect
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
from app.utils.prodefn.docs_session_manager import get_or_create_session
import re
from app.utils.file_utils.upload_utils import upload_and_process, get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from datetime import datetime

logger = logging.getLogger(__name__)

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
documentation_mapping_path = os.path.join(base_dir, 'discussions', 'types', 'documentation_mapping.json')

class DocumentationDiscussion(Discussion):
    """
    A unified Discussion class for handling all documentation-related discussions.
    This class handles both document root configuration and section management,
    selecting the appropriate behavior based on node type.
    """
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Documentation")
        self.version_manager = NodeVersionManager()
        self.background_info = None
        
        # Set template and schema based on discussion type and node labels
        self._configure_template_and_schema()
        
        # Load documentation mapping
        try:
            with open(documentation_mapping_path, 'r') as f:
                self.documentation_mapping = json.load(f)
        except Exception as e:
            self.update_logger.error(f"Error loading documentation mapping: {str(e)}")
            self.documentation_mapping = {}
    
    async def async_initialize(self):
        """Initialize the discussion with node-specific configuration"""
        await super().async_initialize()
        self._configure_template_and_schema()
    
    def _configure_template_and_schema(self):
        """Configure template and schema based on node type and discussion type"""
        if not hasattr(self, 'node') or not self.node:
            # Default configuration before node is loaded
            self.template_name = "documentation_configuration.prompt"
            self.function_schema_type = "DocumentationConfiguration"
            return
            
        # Determine appropriate template and schema based on node labels and discussion type
        if "Sub_Section" in self.node.get('labels', []):
            # For section nodes, use section management template
            self.template_name = "documentation_section_management.prompt"
            self.function_schema_type = "SubsectionManagementConfiguration"
        else:
            # For root nodes or other documentation nodes, use configuration template
            self.template_name = "documentation_configuration.prompt"
            self.function_schema_type = "DocumentationConfiguration"

    async def retrieve_info(self):
        """Retrieve context information for documentation nodes"""
        await super().retrieve_info()
        
        try:
            # Run different retrieval logic based on node type
            if "DocumentationRoot" in self.node.get('labels', []):
                await self._retrieve_root_info()
            elif "Sub_Section" in self.node.get('labels', []):
                await self._retrieve_section_info()
            else:
                self.update_logger.warning(f"Unrecognized node type with labels: {self.node.get('labels', [])}")
                
            # Convert DateTime fields in current_node before it reaches JSON serialization
            if 'current_node' in self.retrieved_info:
                self.retrieved_info['current_node'] = self._convert_datetime_in_node(self.retrieved_info['current_node'])
            
            # Also convert DateTime in node itself
            self.node = self._convert_datetime_in_node(self.node)


            self.update_logger.info(f"Retrieved documentation context for node {self.node_id}")
            return self.retrieved_info
            
        except Exception as e:
            self.update_logger.error(f"Error in retrieve_info: {str(e)}", exc_info=True)
            return self.retrieved_info
        
    def prompt_for_starting_discussion(self):
        """Create prompts for starting a discussion with LLM"""
        # Before rendering the template, remove the LastUpdated field from current_node
        if hasattr(self, 'details_for_discussion') and self.details_for_discussion:
            if 'current_node' in self.details_for_discussion and self.details_for_discussion['current_node']:
                # Create a copy to avoid modifying the original
                import copy
                current_node = copy.deepcopy(self.details_for_discussion['current_node'])
                
                # Remove LastUpdated field from node properties
                if 'properties' in current_node and 'LastUpdated' in current_node['properties']:
                    del current_node['properties']['LastUpdated']
                
                # Also remove top-level LastUpdated if it exists
                if 'LastUpdated' in current_node:
                    del current_node['LastUpdated']
                
                # Update the details_for_discussion with the modified node
                self.details_for_discussion['current_node'] = current_node
        
        # Call the parent class method
        return super().prompt_for_starting_discussion()

    
    async def _retrieve_root_info(self):
        """Retrieve information specific to DocumentationRoot nodes"""
        try:
            # Get parent node and all its context
            try:
                parent_node = await self.db.get_parent_node(self.node_id)
                if parent_node:
                    self.retrieved_info['parent_node'] = parent_node
                    self.retrieved_info['node_context'] = await self.get_complete_node_context(parent_node['id'])
                else:
                    self.update_logger.warning(f"No parent node found for DocumentationRoot {self.node_id}")
                    self.retrieved_info['parent_node'] = None
                    self.retrieved_info['node_context'] = {}
            except Exception as e:
                self.update_logger.error(f"Error retrieving parent node: {str(e)}")
                self.retrieved_info['parent_node'] = None
                self.retrieved_info['node_context'] = {}
            
            # Get existing sections
            try:
                existing_sections = await self.db.get_child_nodes(self.node_id, "Sub_Section") or []
                
                # Format the sections for display
                self.retrieved_info['current_sections'] = sorted([
                    {
                        'title': section['properties'].get('Title', '') if 'properties' in section else '',
                        'type': section['properties'].get('SectionType', '') if 'properties' in section else '',
                        'description': section['properties'].get('Description', '') if 'properties' in section else '',
                        'order': section['properties'].get('Order', 999) if 'properties' in section else 999,
                        'content': section['properties'].get('Content', '') if 'properties' in section else '',
                        'configuration_state': section['properties'].get('configuration_state', '') if 'properties' in section else ''
                    }
                    for section in existing_sections
                    if 'properties' in section
                ], key=lambda x: x['order'])
                
                self.retrieved_info['existing_section_nodes'] = existing_sections
                
            except Exception as e:
                self.update_logger.error(f"Error retrieving sections: {str(e)}")
                self.retrieved_info['current_sections'] = []
                self.retrieved_info['existing_section_nodes'] = []
            
            # Get documentation type specific configuration
            doc_type = self.node['properties'].get('DocumentationType') if 'properties' in self.node else None
            if doc_type:
                try:
                    if doc_type in self.documentation_mapping:
                        self.retrieved_info['documentation_config'] = self.documentation_mapping[doc_type]
                    else:
                        self.update_logger.warning(f"Documentation type {doc_type} not found in mapping")
                        self.retrieved_info['documentation_config'] = {}
                except Exception as e:
                    self.update_logger.error(f"Error processing documentation config: {str(e)}")
                    self.retrieved_info['documentation_config'] = {}
            else:
                self.update_logger.warning("No DocumentationType found in node properties")
                self.retrieved_info['documentation_config'] = {}
                
        except Exception as e:
            self.update_logger.error(f"Error in _retrieve_root_info: {str(e)}", exc_info=True)
    
    def _convert_datetime_in_node(self, node):
        """Convert DateTime objects in a node to ISO format strings"""
        if not node:
            return node
            
        # Create a copy to avoid modifying the original
        import copy
        node_copy = copy.deepcopy(node)
        
        # Convert DateTime objects in properties
        if 'properties' in node_copy:
            for key, value in node_copy['properties'].items():
                if isinstance(value, datetime):
                    node_copy['properties'][key] = value.isoformat()
        
        # Handle the LastUpdated field which is commonly a DateTime
        if 'LastUpdated' in node_copy:
            if isinstance(node_copy['LastUpdated'], datetime):
                node_copy['LastUpdated'] = node_copy['LastUpdated'].isoformat()
        
        return node_copy

    async def _retrieve_section_info(self):
        """Retrieve information specific to Sub_Section nodes"""
        try:
            # Get parent documentation root
            try:
                parent_doc = await self.db.get_parent_node(self.node_id)
                if parent_doc:
                    self.retrieved_info['documentation_root'] = parent_doc
                    
                    # Get documentation type from parent
                    doc_type = parent_doc['properties'].get('DocumentationType') if 'properties' in parent_doc else None
                    if doc_type:
                        try:
                            if doc_type in self.documentation_mapping:
                                self.retrieved_info['documentation_config'] = self.documentation_mapping[doc_type]
                            else:
                                self.update_logger.warning(f"Documentation type {doc_type} not found in mapping")
                                self.retrieved_info['documentation_config'] = {}
                        except Exception as e:
                            self.update_logger.error(f"Error loading documentation config: {str(e)}")
                            self.retrieved_info['documentation_config'] = {}
                    
                    # Get sibling sections for context
                    try:
                        sibling_sections = await self.db.get_child_nodes(parent_doc['id'], "Sub_Section") or []
                        self.retrieved_info['sibling_sections'] = sorted([
                            {
                                'title': section['properties'].get('Title', '') if 'properties' in section else '',
                                'type': section['properties'].get('SectionType', '') if 'properties' in section else '',
                                'order': section['properties'].get('Order', 999) if 'properties' in section else 999,
                            }
                            for section in sibling_sections
                            if 'properties' in section and section['id'] != self.node_id  # Exclude current node
                        ], key=lambda x: x['order'])
                    except Exception as e:
                        self.update_logger.error(f"Error retrieving sibling sections: {str(e)}")
                        self.retrieved_info['sibling_sections'] = []
                else:
                    self.update_logger.warning(f"No parent documentation root found for section {self.node_id}")
                    self.retrieved_info['documentation_root'] = None
                    self.retrieved_info['documentation_config'] = {}
                    self.retrieved_info['sibling_sections'] = []
            except Exception as e:
                self.update_logger.error(f"Error retrieving parent documentation: {str(e)}")
                self.retrieved_info['documentation_root'] = None
                self.retrieved_info['documentation_config'] = {}
                self.retrieved_info['sibling_sections'] = []
            
            # Get source node content if this is an API section
            try:
                if 'properties' in self.node and 'SourceNodeId' in self.node['properties']:
                    source_id = self.node['properties']['SourceNodeId']
                    source_node = await self.db.get_node_by_id(source_id)
                    if source_node:
                        self.retrieved_info['source_node'] = source_node
                        
                        # Get additional context based on source node type
                        if "Interface" in source_node.get('labels', []):
                            # For interfaces, get endpoints and models
                            endpoints = await self.db.get_child_nodes(source_id, "Endpoint") or []
                            self.retrieved_info['endpoints'] = endpoints
                            
                            # Get models referenced by endpoints
                            data_models = []
                            for endpoint in endpoints:
                                if 'properties' in endpoint:
                                    if 'RequestModel' in endpoint['properties']:
                                        model_id = endpoint['properties']['RequestModel']
                                        model = await self.db.get_node_by_id(model_id)
                                        if model:
                                            data_models.append(model)
                                            
                                    if 'ResponseModel' in endpoint['properties']:
                                        model_id = endpoint['properties']['ResponseModel']
                                        model = await self.db.get_node_by_id(model_id)
                                        if model:
                                            data_models.append(model)
                                            
                            self.retrieved_info['data_models'] = data_models
            except Exception as e:
                self.update_logger.error(f"Error retrieving source node context: {str(e)}")
                self.retrieved_info['source_node'] = None
                
        except Exception as e:
            self.update_logger.error(f"Error in _retrieve_section_info: {str(e)}", exc_info=True)

    async def get_complete_node_context(self, node_id):
        """Get complete context for a node including properties and relationships"""
        try:
            node = await self.db.get_node_by_id(node_id)
            if not node:
                return {}
                
            context = {
                'node': node,
                'children': {}
            }
            
            # Get child nodes grouped by type
            child_nodes = await self.db.get_child_nodes(node_id, None) or []
            
            # Group children by label
            for child in child_nodes:
                if not child or 'labels' not in child:
                    continue
                    
                for label in child['labels']:
                    if label not in context['children']:
                        context['children'][label] = []
                    context['children'][label].append(child)
            
            return context
        except Exception as e:
            self.update_logger.error(f"Error in get_complete_node_context: {str(e)}", exc_info=True)
            return {}

    def get_modifications_from_llm_output(self):
        """Process LLM output into modifications for documentation"""
        super().get_modifications_from_llm_output()
        
        # Set appropriate child node types based on node type
        if 'DocumentationRoot' in self.node.get('labels', []):
            self.modifications['child_node_types'] = ['Sub_Section']
        
        return self.modifications
    
    async def merge_captured_items(self):
        """Merge the captured items from LLM into the node database"""
        try:
            await super().merge_captured_items()
            
            # Update configuration state based on node type
            if self.modifications.get('modified_node') or self.modifications.get('created_nodes'):
                node_type = "DocumentationRoot" if "DocumentationRoot" in self.node.get('labels', []) else "Sub_Section"
                await self.db.update_node_by_id(
                    self.node_id,
                    {'configuration_state': 'configured', 'LastUpdated': datetime.now().isoformat()},
                    node_type
                )
                
            self.update_logger.info(f"Documentation updates completed for node {self.node_id}")
            
        except Exception as e:
            self.update_logger.error(f"Error in merge_captured_items: {str(e)}", exc_info=True)

# Register this unified discussion class for both documentation discussion types
DiscussionFactory.register('documentation_configuration', DocumentationDiscussion)
DiscussionFactory.register('documentation_section_management', DocumentationDiscussion) 