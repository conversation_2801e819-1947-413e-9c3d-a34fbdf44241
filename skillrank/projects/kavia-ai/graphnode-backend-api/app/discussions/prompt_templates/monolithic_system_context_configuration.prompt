{% extends "base_discussion.prompt" %}

You are an expert software architect configuring a monolithic system.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model:

{% else %}
You are an expert system architect reviewing the System Context for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}

   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
   
   - Updated architecture requirements context:
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:

1. Impact Analysis:
   - Compare original and new project context
   - Compare original and new functional/non-functional requirements
   
2. Container Architecture Analysis:
   - Review existing containers against new requirements
   - Evaluate container responsibilities and boundaries


4. Required Changes:
   - Modify Existing System Context, containers, users, external_systems as needed based on the above analysis
   - Follow the below guidelines as a reference while modifying the existing system_context.
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %}

GENERIC GUIDELINES:

1.Review the project Details, Functional and Non-Functional Requirements and configure the c4 model based System context for this project. 

2. Provide detailed description of the system's purpose and main functionalities.
3. CRITICAL ARCHITECTURAL CONSTRAINTS:You MUST follow these rules strictly:
    Create EXACTLY ONE internal container - set the ContainerType as internal -  as child for the current node.
4. This single container MUST encapsulate ALL system functionality
5. Identify and describe the users of the system.
6. Identify and create external systems as containers - that interact with this system.
    For each external system identified:
   - Create a corresponding container representing the external system - as child node 
   - Set ContainerType as "external"
   - Provide a suitable name
   - Describe the external system's purpose and role - in description
   - Leave other properties as there is no relevance.

{% if details_for_discussion.get('architecture_pattern', '').lower() == "monolithic-service" %}
7. Define Interfaces.
    This container is designed as an micro service, MUST define Provider interfaces that shall expose its functionality to external entities as new interface relationship. 
    Review the functional Requirements carefully and ensure to expose software interfaces for each of the core Functional Requirement following the Industry best practices. 
     - For each interface, determine and provide:
     * Appropriate interface type based on:
       - Communication patterns required
       - Performance requirements
       - Reliability needs
       - Integration constraints
     * Clear descriptive name 
        -- Examples of good interface names
        * "SMS notification Interface for Weather Alerts"
        * "API for accessing User settings from database"
        * "Retrieve weather data from backend"
     * Detailed description

    For example for a calculator service it should be able to expose the following :
    Arithmetic Operations Interface
    Name: ArithmeticOperationsAPI
    Description: Provides basic arithmetic operations such as addition, subtraction, multiplication, and division. This interface ensures accuracy and handles edge cases.
    API Signature:
    POST /api/arithmetic
    Request Body: { "operation": "add", "operands": [5, 3] }
    Response: { "result": 8 }

    User Authentication Interface
    Name: UserAuthAPI
    Description: Manages user authentication, including login, password recovery, and two-factor authentication.
    API Signature:
    POST /api/auth/login
    Request Body: { "username": "user", "password": "pass" }
    Response: { "token": "jwt-token" }
{% else %}
7. Interfaces are not required for this kind of monolithic.
{% endif %}
8. Define interactions between:
   - The monolithic container and users
   - The monolithic container and external systems
9. Generate a C4 System Context diagram using Mermaid syntax showing:
    - System boundaries using subgraphs
    - User types relevant to your specific system
    - External systems that your system actually needs to interact with
    - Relationships based on real system interactions
    - Protocols and data flows specific to your system


{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

{% if details_for_discussion.get('architecture_pattern', '').lower() == "monolithic-service" %}
    System Context Diagram Guidelines:
    ```
   graph TB
    %% API Consumers
    subgraph Consumers[API Consumers]
        Client1["Client System 1\n[System]"]
        Client2["Client System 2\n[System]"]
    end

    %% Main Service System
    subgraph MonolithicService[Monolithic Service]
        ServiceContainer["Service Container\n[Container: Internal]"]
    end

    %% External Systems
    subgraph ExternalSystems[External Systems]
        ExtSystem1["External System 1\n[Container: External]"]
        ExtSystem2["External System 2\n[Container: External]"]
    end

    %% API Consumer Interactions
    Client1 -->|"Uses API"| ServiceContainer
    Client2 -->|"Uses API"| ServiceContainer
    
    %% External System Interactions
    ServiceContainer -->|"Uses"| ExtSystem1
    ServiceContainer -->|"Uses"| ExtSystem2

    %% Styling
    classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#0b4884,color:#ffffff
    classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

    class Client1,Client2 system
    class ServiceContainer container
    class ExtSystem1,ExtSystem2 external
    class Consumers,MonolithicService,ExternalSystems boundary
    ```

    10.Container Diagram Guidelines:
    ```
   graph TB
    %% External API Consumers
    Consumer1["Client System 1\n[System]"]
    Consumer2["Client System 2\n[System]"]

    subgraph ServiceBoundary[Monolithic Service]
        MonolithicContainer["Service Container\n[Container: Your Technology Stack]\nImplements all service functionality"]
    end

    %% External System Containers
    ExtSystem1["External System\n[Container: External]"]
    ExtSystem2["External System\n[Container: External]"]
    
    %% Interactions
    Consumer1 -->|"REST/gRPC"| MonolithicContainer
    Consumer2 -->|"REST/gRPC"| MonolithicContainer
    MonolithicContainer -->|"Uses External API"| ExtSystem1
    MonolithicContainer -->|"Uses External API"| ExtSystem2

    %% Styling
    classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef external fill:#666666,stroke:#0b4884,color:#ffffff
    classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

    class Consumer1,Consumer2 system
    class MonolithicContainer container
    class ExtSystem1,ExtSystem2 external
    class ServiceBoundary boundary
    ```
    Adapt to show:
    - Actual container structure
    - Real technology choices
    - Required external infrastructure
    - Proper interaction patterns

{% else %}
    
    Example System Context Diagram Structure (to be adapted for your specific system):
    ```
    graph TB
    %% Users/Actors - Include only relevant user types
    subgraph Users[System Users]
        User1["User Type 1<br/>[Person]<br/><i>Description of this user's role</i>"]
        User2["User Type 2<br/>[Person]<br/><i>Description of this user's role</i>"]
    end

   %% Main System
   subgraph MainSystem[Your System Name]
       System["System Name<br/>[System]<br/><i>Core system description<br/>focused on actual functionality</i>"]
   end

   %% External Systems - Only those specifically required
   subgraph ExternalSystems[External Systems]
       Ext1["External System 1<br/>[System_Ext]<br/><i>Required external<br/>system description</i>"]
       Ext2["External System 2<br/>[System_Ext]<br/><i>Required external<br/>system description</i>"]
   end

   %% Relationships - Only show actual interactions
   User1 -->|"Specific interaction<br/>Actual protocol"| System
   User2 -->|"Specific interaction<br/>Actual protocol"| System
   System -->|"Required interaction<br/>Actual protocol"| Ext1
   System -->|"Required interaction<br/>Actual protocol"| Ext2

   %% Styling
   classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class System system
   class User1,User2 person
   class Ext1,Ext2 external
   class Users,ExternalSystems,MainSystem boundary
    ```
    10. Create a Container diagram showing:
    - Single container with its technology stack
    - External system interactions if applicable
    - User interactions
    - Data flows and protocols

    The single container must define:

    Container Diagram Structure
    IMPORTANT: The following is only an example structure. You must:

    Adapt it to your specific system requirements
    Include only users and external systems actually needed by your system
    Do not copy relationships unless they exist in your system
    Modify all names, descriptions, and interactions to match your actual system

    Example Structure (TO BE ADAPTED, NOT COPIED):
    ```
    graph TB
        %% NOTE: This is an EXAMPLE ONLY
        %% Replace all elements with those specific to your system
        %% Do not include any users, systems, or interactions unless required

        %% Example users - Replace with your system's actual users
        ActualUser["Your Actual User Type<br/>[Person]<br/><i>Replace with your system's<br/>real user description</i>"]

        subgraph YourSystemName[Replace With Your System Name]
            %% Single monolithic container - Customize for your system
            MainContainer["Your System Name<br/>[Container: Your Actual Technology Stack]<br/><i>Describe your system's actual<br/>functionality and responsibilities</i>"]
        end

        %% Example external system - Include ONLY if your system needs it
        ExternalSystem["Your Required External System<br/>[System_Ext]<br/><i>Only if actually needed</i>"]

        %% Example relationships - Replace with your actual interactions
        ActualUser -->|"Describe actual interaction<br/>Real protocol"| MainContainer
        MainContainer -->|"Only if this interaction exists<br/>Actual protocol"| ExternalSystem

        %% Styling
        classDef person fill:#08427b,stroke:#052e56,color:#ffffff
        classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
        classDef external fill:#666666,stroke:#0b4884,color:#ffffff
        classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4
        ```
{% endif %}

Change Needed: 
    - Set to False if changes are not required.

Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}
Design a system context with a single container that encapsulates all system functionality while ensuring proper integration with users and external systems. 
The monolithic container must support all required capabilities, integrations, and maintain system qualities.
{% endblock %}


{% block node_details_interactive_reconfig %}
Follow the above guidelines make the required changes to the system context. 

{% endblock %}


{% block node_details_interactive_reconfig_update_specifications %}
system context configuration, including its internal containers, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
      Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details :  {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Existing Containers:
    {{ details_for_discussion.get('containers') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}