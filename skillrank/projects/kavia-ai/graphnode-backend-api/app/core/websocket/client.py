# app/core/websocket/client.py

import asyncio
import json
import os
import time
import websockets
import logging
import threading
from typing import Optional, Dict, Any
from queue import Queue, Empty


log_directory = "logs"
if not os.path.exists(log_directory):
    os.makedirs(log_directory)

# log_file = os.path.join(log_directory, "websocket_client.log")
# file_handler = logging.FileHandler(log_file)
# file_handler.setFormatter(logging.Formatter(
#     '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# ))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# logger.addHandler(file_handler)
# Remove any existing console handlers
logger.propagate = False


class WebSocketThread:
    def __init__(self):
        self.loop = asyncio.new_event_loop()
        self._running_tasks = set()

    def run_coroutine(self, coro):
        """Run a coroutine in the thread's event loop"""
        if not self.loop.is_running():
            threading.Thread(target=self._run_loop, daemon=True).start()
        
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        self._running_tasks.add(future)
        future.add_done_callback(self._running_tasks.discard)
        return future

    def _run_loop(self):
        """Run the event loop"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def stop(self):
        """Stop all running tasks and the event loop"""
        for task in self._running_tasks:
            task.cancel()
        if self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)


class WebSocketClient:
    def __init__(self, task_id: str, uri: str = "ws://localhost:8765", client_type: str = "agent"):
        self.ws_thread = WebSocketThread()
        self.task_id = task_id
        self.uri = f"{uri}/{task_id}"
        self.client_type = client_type
        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self.connected = False
        self._message_queue = Queue()
        self._connect_lock = threading.Lock()
        self._message_handlers = []
        self._handler_running = False 
        self._message_lock = asyncio.Lock()
        self._input_queue = Queue()
        self._last_message = None
        self._message_processors = []
        self._offline_mode = False
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 1
        self._last_connection_check = time.time()
        self._connection_check_interval = 5


    def reset_thread_and_reconnect(self):
        self.stop_message_handler()
        self.disconnect()
        if self.ws_thread:
            self.ws_thread.stop()
        self.ws_thread = WebSocketThread()
        if self.connect():
            
            self.start_message_handler(force_start=True)
            logger.info("WebSocket thread and handlers successfully reset and reconnected.")
        else:
            logger.error("Failed to reconnect after resetting WebSocket thread.")
            
    def connect(self) -> bool:
        """Connect to WebSocket server with improved error handling"""
        try:
            print("Starting connection attempt...")  # Debug

            if self.connected:
                self.start_message_handler()
                print("Already connected, returning")  # Debug
                return True
                
            ws_uri = self.uri
            retry_delay = self._reconnect_delay
            while self._reconnect_attempts < self._max_reconnect_attempts:
                try:
                    async def _connect():
                        try:
                            print("Initiating WebSocket connection...")  # Debug
                            self.websocket = await websockets.connect(
                                self.uri
                            )
                            # Register with the specified client type
                            print(f"Connecting to websocket with {ws_uri} and {self.client_type} and {self.task_id}")
                            await self.websocket.send(json.dumps({
                                "type": self.client_type,
                                "task_id": self.task_id
                            }))
                            
                            return True
                        except Exception as e:
                            print(f"Inner connection error: {str(e)}")  # Debug
                            return False

                    print(f"Attempt {self._reconnect_attempts + 1} of {self._max_reconnect_attempts}")  # Debug
                    future = self.ws_thread.run_coroutine(_connect())
                    self.connected = future.result()
                    
                    if self.connected:
                        print("Connection and registration successful")  # Debug
                        self._reconnect_attempts = 0
                        self._reconnect_delay = 1
                        logger.info("Successfully connected to WebSocket server")
                        return True
                        
                except Exception as e:
                    print(f"Connection attempt failed: {str(e)}")  # Debug
                    self._reconnect_attempts += 1
                    if self._reconnect_attempts < self._max_reconnect_attempts:
                        print(f"Waiting {retry_delay} seconds before next attempt...")  # Debug
                        time.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 30)
                    else:
                        print("Max reconnection attempts reached")  # Debug
                        pass

  
                return True  # Return True even in offline mode to allow operation
                
        except Exception as e:
            print(f"Outer exception occurred: {str(e)}")  # Debug

            return False  # Return True to allow offline operation

    def disconnect(self):
        """Disconnect from WebSocket server"""
        try:
            async def _disconnect():
                if self.websocket:
                    await self.websocket.close()
                    
            if self.connected and not self._offline_mode:
                self.ws_thread.run_coroutine(_disconnect())
                
            self.connected = False
            self.websocket = None
            self._handler_running = False
                
        except Exception as e:
            pass
            # logger.error(f"Error disconnecting from WebSocket: {e}")
    

    def send_message(self, message_type: str, data: Dict[str, Any]):
        """Send a message with offline mode support"""
        if self._offline_mode:
            logger.info(f"Offline mode - Message queued: {message_type} - {json.dumps(data)}")
            return

        try:
            async def _send():
                if self.websocket and self.connected:
                    message = {
                        "type": message_type,
                        "task_id": self.task_id,
                        "data": data
                    }
                    await self.websocket.send(json.dumps(message))

            if not self.connected:
                self.connect()
                
            if self.connected:
                self.ws_thread.run_coroutine(_send())
                
        except Exception as e:
            # logger.error(f"Error sending message: {str(e)}")
            # logger.info(f"Offline mode - Message queued: {message_type} - {json.dumps(data)}")
            self._offline_mode = True

    async def receive_message(self):
        """Receive a message from WebSocket"""
        if self.websocket and not self._offline_mode:
            try:
                message = await self.websocket.recv()
                return json.loads(message)
            except websockets.exceptions.ConnectionClosed:
                # logger.warning("WebSocket connection closed, attempting to reconnect...")
                self.connected = False
                self._reconnect_attempts = 0
                if self.connect():
                    return await self.receive_message()
            except Exception as e:
                # logger.error(f"Error receiving message: {e}")
                self.connected = False
                return None
        return None

    def _monitor_connection(self):
        """Continuously monitor WebSocket connection"""
        while not self._handler_running:
            current_time = time.time()
            
            if current_time - self._last_connection_check >= self._connection_check_interval:
                self._last_connection_check = current_time
                
                if not self.connected and not self._offline_mode:
                    # logger.info("WebSocket connection lost, attempting to reconnect...")
                    self._reconnect_attempts = 0
                    if self.reset_thread_and_reconnect():
                        logger.info("Successfully reconnected to WebSocket")
                
            time.sleep(1)

    async def handle_messages(self):
        """Handle incoming messages with improved error handling"""
        while self._handler_running:
            try:
                if self.websocket:
                    async with self._message_lock:
                        # Process any pending input first
                        while not self._input_queue.empty():
                            try:
                                input_data = self._input_queue.get_nowait()
                                for handler in self._message_handlers:
                                    await handler({
                                        "type": "user_input",
                                        "input": input_data
                                    })
                            except Empty:
                                break

                        # Then process received message
                        message = await self.receive_message()
                        if message:
                            for handler in self._message_handlers:
                                await handler(message)

            except websockets.exceptions.ConnectionClosed:
                # logger.warning("Connection closed, attempting to reconnect...")
                self.connected = False
                if not self.connect():
                    break
            except Exception as e:
                pass
                # logger.error(f"Error in message handler: {e}")
            
            await asyncio.sleep(0.1)

    def start_message_handler(self,force_start=False):
        """Start message handler with connection monitoring"""
        if not self._handler_running or force_start:
            self._handler_running = True
            self.ws_thread.run_coroutine(self.handle_messages())


    def stop_message_handler(self):
        """Stop message handler"""
        self._handler_running = False

    def add_message_handler(self, handler):
        """Add message handler"""
        if handler not in self._message_handlers:
            self._message_handlers.append(handler)

    def remove_message_handler(self, handler):
        """Remove message handler"""
        if handler in self._message_handlers:
            self._message_handlers.remove(handler)

    def add_message_processor(self, processor):
        """Add message processor"""
        if processor not in self._message_processors:
            self._message_processors.append(processor)

    def cleanup(self):
        """Cleanup WebSocket thread"""
        self.ws_thread.stop()