from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional,List
import os
import logging
import subprocess 
import json
import asyncio
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
import shutil
import boto3
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.routes.deployment_helper.repo_handler import validate_github_url,clone_repository
from app.routes.deployment_helper.terraform_handler import generate_terraform_graph,initialize_terraform,setup_terraform_workspace,run_terraform_plan,run_terraform_apply,get_terraform_outputs,update_deployment_status
from app.routes.deployment_helper.directory_handler import setup_infrastructure_directory,setup_workflows_directory
from app.routes.deployment_helper.github_push_hander import configure_git,setup_remote,commit_changes,push_to_branch,setup_github_secrets
from app.routes.deployment_helper.sample_tf import get_main_tf,get_variables_tf,get_providers_tf,get_outputs_tf,get_workflows  
from app.routes.repository_route import get_repository,list_branches
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.deployment_helper.directory_finder import find_application_directory
import time
import random
import string
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, Union, Callable
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

_SHOW_NAME = "deployment"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)


async def upload_repo_to_s3(repo_path: str, bucket: str, prefix: str, exclude_dirs: List[str] = ["infrastructure"]):
    """
    Upload repository to S3 excluding specified directories
    
    Args:
        repo_path (str): Local path to repository
        bucket (str): S3 bucket name
        prefix (str): S3 prefix (folder path)
        exclude_dirs (list): Directories to exclude
    """
    try:
        s3_client = boto3.client('s3')
        
        # Walk through the directory
        for root, dirs, files in os.walk(repo_path):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                # Get the full path of the file
                file_path = os.path.join(root, file)
                
                # Calculate relative path for S3 key
                relative_path = os.path.relpath(file_path, repo_path)
                s3_key = f"{prefix}/repo/{relative_path}"
                
                try:
                    # Upload file
                    s3_client.upload_file(file_path, bucket, s3_key)
                    logger.info(f"Uploaded {relative_path} to {s3_key}")
                except Exception as e:
                    logger.error(f"Failed to upload {file_path}: {str(e)}")
                    
        return True
    except Exception as e:
        logger.error(f"Error uploading repo to S3: {str(e)}")
        return False
    
async def save_terraform_files_to_s3(plan_output: str, tf_output: dict, bucket: str, prefix: str, region: str = 'us-east-2'):
    """
    Save Terraform plan and output files to S3
    
    Args:
        plan_output (str): Terraform plan output
        tf_output (dict): Terraform output
        bucket (str): S3 bucket name
        prefix (str): S3 prefix path
        region (str): AWS region name (default: 'us-east-1')
    
    Returns:
        dict: Dictionary containing the S3 keys of saved files
        None: If an error occurs
    """
    try:
        # Initialize S3 client with specified region
        s3_client = boto3.client('s3', region_name=region)
        
        # Try to create bucket directly without checking existence
        try:
            if region == 'us-east-1':
                s3_client.create_bucket(Bucket=bucket)
            else:
                s3_client.create_bucket(
                    Bucket=bucket,
                    CreateBucketConfiguration={
                        'LocationConstraint': region
                    }
                )
            logger.info(f"Created bucket {bucket} in region {region}")
        except s3_client.exceptions.ClientError as e:
            error_code = e.response['Error']['Code']
            # Ignore BucketAlreadyOwnedByYou and BucketAlreadyExists errors
            if error_code not in ['BucketAlreadyOwnedByYou', 'BucketAlreadyExists', '409']:
                if error_code in ['AccessDenied', 'Forbidden', '403']:
                    logger.warning(f"No permission to create bucket {bucket}. Attempting to use existing bucket.")
                else:
                    logger.error(f"Unexpected error during bucket creation: {str(e)}")
                    raise e

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save plan output
        plan_key = f"{prefix}/terraform_plan_{timestamp}.txt"
        s3_client.put_object(
            Bucket=bucket,
            Key=plan_key,
            Body=plan_output.encode('utf-8')
        )
        logger.info(f"Saved terraform plan to {plan_key}")
        
        # Save terraform output
        output_key = f"{prefix}/terraform_output_{timestamp}.json"
        s3_client.put_object(
            Bucket=bucket,
            Key=output_key,
            Body=json.dumps(tf_output, indent=2).encode('utf-8')
        )
        logger.info(f"Saved terraform output to {output_key}")
        
        return {
            "plan_file": plan_key,
            "output_file": output_key
        }
    except Exception as e:
        logger.error(f"Error saving terraform files to S3: {str(e)}")
        return None
    
def generate_unique_domain(base_name: str ) -> str:
    """
    Generate a valid unique domain name for AWS Amplify.
    
    Args:
        base_name (str): Base name for the domain
        
    Returns:
        str: Valid unique domain name
    """
    # Clean the base name - remove special characters and convert to lowercase
    clean_base = ''.join(c if c.isalnum() or c == '-' else '-' for c in base_name.lower())
    clean_base = clean_base.strip('-')
    
    # Generate timestamp
    timestamp = int(time.time())
    
    # Generate random string
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
    
    # Combine with default AWS Amplify domain
    # Format: base-name-timestamp-random.amplifyapp.com
    domain = f"{clean_base}-{timestamp}-{random_suffix}.amplifyapp.com"
    
    return domain

async def commit_changes(repo_path: str, commit_message: str, working_dir: str) -> bool:
    """Commit changes with improved file tracking"""
    try:
        logger.info("Starting commit process...")
        
        # List all files in directory
        process = await asyncio.create_subprocess_exec(
            'ls', '-la',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        logger.info(f"Directory contents:\n{stdout.decode()}")

        # Initialize repository if needed
        init_process = await asyncio.create_subprocess_exec(
            'git', 'init',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await init_process.communicate()

        # Force add all files
        add_process = await asyncio.create_subprocess_exec(
            'git', 'add', '-A', '-f',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        add_stdout, add_stderr = await add_process.communicate()
        
        if add_process.returncode != 0:
            logger.error(f"Git add failed: {add_stderr.decode()}")
            return False

        # Check git status
        status_process = await asyncio.create_subprocess_exec(
            'git', 'status',
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        status_stdout, status_stderr = await status_process.communicate()
        logger.info(f"Git status:\n{status_stdout.decode()}")

        # Commit changes
        commit_process = await asyncio.create_subprocess_exec(
            'git', 'commit', '-m', commit_message,
            cwd=working_dir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        commit_stdout, commit_stderr = await commit_process.communicate()
        
        if commit_process.returncode != 0 and "nothing to commit" not in commit_stderr.decode():
            logger.error(f"Commit failed: {commit_stderr.decode()}")
            return False

        logger.info(f"Commit completed: {commit_stdout.decode()}")
        return True

    except Exception as e:
        logger.error(f"Commit failed with exception: {str(e)}")
        return False

async def clone_codecommit_repo(repo_url: str, local_path: str, branch: str) -> bool:
    """
    Clone a repository from AWS CodeCommit using AWS CLI credential helper.
    
    Args:
        repo_url (str): CodeCommit repository URL
        local_path (str): Local path to clone to
        branch (str): Branch to clone (default: "master")
        aws_region (str): AWS region (default: "us-east-1")
    """
    try:
        aws_region = os.environ.get('AWS_DEFAULT_REGION', 'us-east-1')
        # Ensure target directory doesn't exist
        if os.path.exists(local_path):
            shutil.rmtree(local_path)

        # Configure git to use AWS credential helper
        git_config_commands = [
            # Remove any existing credential helper configuration
            ['git', 'config', '--global', '--unset', 'credential.helper'],
            # Add AWS credential helper
            ['git', 'config', '--global', 'credential.helper', '!aws codecommit credential-helper $@'],
            ['git', 'config', '--global', 'credential.UseHttpPath', 'true']
        ]

        # Set up git configuration
        for cmd in git_config_commands:
            try:
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
            except:
                # Ignore errors from --unset if the config doesn't exist
                pass

        logger.info(f"Attempting to clone repository to: {local_path} (branch: {branch})")

        # Clone repository using the same environment as terminal
        # Add -b flag to specify branch
        process = await asyncio.create_subprocess_exec(
            'git', 'clone', '-b', branch, repo_url, local_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={
                **os.environ,  # Include all current environment variables
                'AWS_DEFAULT_REGION': aws_region
            }
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            error_msg = stderr.decode() if stderr else "Unknown error"
            logger.error(f"Clone failed: {error_msg}")
            
            # If branch doesn't exist, try cloning without branch specification
            if "Remote branch not found" in error_msg or "couldn't find remote ref" in error_msg:
                logger.info(f"Branch {branch} not found, attempting to clone default branch")
                
                # Clean up failed clone attempt
                if os.path.exists(local_path):
                    shutil.rmtree(local_path)
                
                # Try cloning without branch specification
                process = await asyncio.create_subprocess_exec(
                    'git', 'clone', repo_url, local_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env={
                        **os.environ,
                        'AWS_DEFAULT_REGION': aws_region
                    }
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode != 0:
                    error_msg = stderr.decode() if stderr else "Unknown error"
                    logger.error(f"Clone failed with default branch: {error_msg}")
                    return False
                
                # Create and checkout the specified branch
                checkout_process = await asyncio.create_subprocess_exec(
                    'git', 'checkout', '-b', branch,
                    cwd=local_path,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                checkout_stdout, checkout_stderr = await checkout_process.communicate()
                if checkout_process.returncode != 0:
                    logger.error(f"Failed to create branch {branch}: {checkout_stderr.decode()}")
                    return False
            else:
                return False
            
        logger.info(f"Successfully cloned repository to {local_path}")
        return True

    except Exception as e:
        logger.error(f"Error cloning repository: {str(e)}")
        return False
    
async def check_and_create_repo(github_token: str, github_username: str, repo_name: str) -> str:
    """
    Check if repository exists and create if it doesn't.
    Returns repository URL.
    """
    try:
        g = Github(github_token)
        user = g.get_user()

        try:
            repo = user.get_repo(f"{github_username}/{repo_name}")
            logger.info(f"Repository already exists: {repo.clone_url}")
        except GithubException:
            logger.info(f"Creating new repository: {repo_name}")
            repo = user.create_repo(
                name=repo_name,
                private=True,
                auto_init=False,
                description=f"Deployment repository"
            )
            logger.info(f"Successfully created repository: {repo.clone_url}")
        
        return f"https://github.com/{github_username}/{repo_name}.git"

    except Exception as e:
        logger.error(f"GitHub operation failed: {str(e)}")
        # Return default URL format instead of raising exception
        return f"https://github.com/{github_username}/{repo_name}.git"
    

# Status Enums for Consistency
class StepStatus(Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"

def get_step_response(status: str, step: str, message: str) -> Dict[str, Any]:
    return {
        "status": status,
        "step": step,
        "message": message
    }

@router.post("/{project_id}/{container_id}/{branch}/deploy_infrastructure")
async def deploy_infrastructure(
    project_id: int,
    container_id: int,
    branch: str,
    db = Depends(get_node_db)
) -> StreamingResponse:
    async def generate_progress():
        temp_dir = "./deployments"
        try:
            logger.info(f"Starting deployment process for project {project_id}, container {container_id}, branch {branch}")
            
            # Initialization
            init_step = get_step_response(
                StepStatus.IN_PROGRESS.value,
                "initialization",
                "Starting deployment process"
            )
            yield f"data: {json.dumps(init_step)}\n\n"
            
            # Cleanup and create temp directory
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            logger.debug(f"Created temporary directory: {temp_dir}")

            # Configuration and Validation
            logger.info("Starting input validation")
            access_token = os.environ.get("GITHUB_ACCESS_TOKEN")
            if not access_token:
                raise ValueError("GitHub access token not found in environment variables")

            
            repo_info = await get_repository(project_id,container_id,db)
            repo_url = repo_info["repository"]["cloneUrlHttp"]

        
            # These should be configurable
            # repo_url = "https://git-codecommit.us-east-1.amazonaws.com/v1/repos/movie-match-20-movie-match-20-83372-83468-experimental"

            
            # if not repo_url:
            #     logger.error("Missing repository URL")
            #     raise HTTPException(status_code=400, detail="Missing repository URL")
            
            # is_valid, error = await validate_github_url(repo_url)
            # if not is_valid:
            #     logger.error(f"Invalid GitHub URL: {error}")
            #     raise HTTPException(status_code=400, detail=error)
                
            logger.info("Input validation completed successfully")

            '''
            # Keeping for future reference
            validation_complete = {
                "status": "completed",
                "step": "validation",
                "message": "All inputs validated successfully"
            }
            yield f"data: {json.dumps(validation_complete)}\n\n"
            '''

            # Context Retrieval
            logger.info("Retrieving system context")
            system_context = await db.get_child_nodes(project_id, "SystemContext")
            if not system_context:
                logger.error("System context not found")
                raise ValueError("System context not found")
            
            deployment_node = await get_deployment_node(project_id, container_id, db)
            if not deployment_node:
                raise ValueError("Deployment node not found")
            
            init_complete = get_step_response(
                StepStatus.COMPLETED.value,
                "initialization",
                "Deployment process initialized"
            )
            yield f"data: {json.dumps(init_complete)}\n\n"

            # Terraform Preparation
            logger.info("Preparing Terraform files")
            terraform_prep = get_step_response(
                StepStatus.IN_PROGRESS.value,
                "terraform_prep",
                "Preparing Terraform files"
            )
            yield f"data: {json.dumps(terraform_prep)}\n\n"
            
             # Prepare terraform files
            terraform_files = {
                'main.tf': deployment_node['properties'].get('main_tf'),
                'variables.tf': deployment_node['properties'].get('variables_tf'),
                'providers.tf': deployment_node['properties'].get('providers_tf'),
                'outputs.tf': deployment_node['properties'].get('outputs_tf'),
                'workflow_file': deployment_node['properties'].get('workflow_file'),
            }

            github_token = os.environ["GITHUB_ACCESS_TOKEN"]
            
            # Extract repository name
            repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
            new_repo_name = f"{repo_name}_deploy"

             # Repository Creation
            logger.info(f"Creating GitHub repository: {new_repo_name}")
            repo_step = {
                "status": "in_progress",
                "step": "repo_creation",
                "message": f"Creating GitHub repository: {new_repo_name}"
            }
            yield f"data: {json.dumps(repo_step)}\n\n"
            
            if not await create_codecommit_repository(repo_name):
                raise Exception(f"Failed to ensure repository {repo_name} exists")

            logger.info("GitHub repository created successfully")
            repo_complete = {
                "status": "completed",
                "step": "repo_creation",
                "message": "GitHub repository created successfully"
            }
            yield f"data: {json.dumps(repo_complete)}\n\n"

            # terraform_files = {
            #     'main.tf': get_main_tf(),
            #     'variables.tf': get_variables_tf(access_token, repo_url, app_name, branch, domain_name),
            #     # 'variables.tf': get_variables_tf(access_token, app_name, branch, domain_name),
            #     'providers.tf': get_providers_tf(),
            #     'outputs.tf': get_outputs_tf(),
            #     'workflow_file': get_workflows(branch)
            # }

            # Validate terraform files
            for file_name, content in terraform_files.items():
                if not content and file_name != 'workflow_file':
                    raise ValueError(f"Failed to generate {file_name}")
            
            terraform_prep_complete = get_step_response(
                StepStatus.COMPLETED.value,
                "terraform_prep",
                "Terraform files prepared successfully"
            )
            yield f"data: {json.dumps(terraform_prep_complete)}\n\n"

            # Directory Setup
            logger.info("Setting up deployment directories")
            directory_step = get_step_response(
                StepStatus.IN_PROGRESS.value,
                "directory_setup",
                "Setting up deployment directories"
            )
            yield f"data: {json.dumps(directory_step)}\n\n"
            
            infrastructure_path = os.path.join(temp_dir, 'infrastructure')
            workflows_path = os.path.join(temp_dir, '.github/workflows')
            os.makedirs(infrastructure_path, exist_ok=True)
            os.makedirs(workflows_path, exist_ok=True)

            # Validate directory creation
            if not os.path.exists(infrastructure_path) or not os.path.exists(workflows_path):
                raise Exception("Failed to create required directories")
            
            directory_complete = get_step_response(
                StepStatus.COMPLETED.value,
                "directory_setup",
                "Deployment directories created successfully"
            )
            yield f"data: {json.dumps(directory_complete)}\n\n"

            # File Creation
            logger.info("Creating infrastructure files")
            saved_files = {"terraform": []}
            for filename, content in terraform_files.items():
                if content and filename != 'workflow_file':
                    file_path = os.path.join(infrastructure_path, filename)
                    print(content)
                    with open(file_path, 'w') as f:
                        f.write(content)
                    saved_files["terraform"].append(filename)
                    logger.debug(f"Created file: {filename}")

            if terraform_files['workflow_file']:
                workflow_path = os.path.join(workflows_path, 'deploy.yml')
                with open(workflow_path, 'w') as f:
                    f.write(terraform_files['workflow_file'])
                logger.debug("Created workflow file")

            # Terraform Operations (Simulated)
            logger.info("Starting Terraform operations (Simulated)")
            os.chdir(infrastructure_path)
            terraform_commands = ["init", "plan", "apply"]
            plan_output = ""
            terraform_outputs = {}

            for command in terraform_commands:
                logger.debug(f"Simulating terraform command: {command}")
                command_step = get_step_response(
                    StepStatus.IN_PROGRESS.value,
                    f"terraform_{command}",
                    f"Running terraform {command}"
                )
                yield f"data: {json.dumps(command_step)}\n\n"
                
                success_step = get_step_response(
                    StepStatus.COMPLETED.value,
                    f"terraform_{command}",
                    f"Terraform {command} completed successfully"
                )
                yield f"data: {json.dumps(success_step)}\n\n"
                
                if command == "plan":
                    plan_output = "Simulated plan output"

            # Simulated terraform outputs
            logger.info("Retrieving terraform outputs (Simulated)")
            terraform_outputs = {
                "amplify_app_id": "simulated_app_id",
                # "domain_name": domain_name,
                "repository_url": repo_url
                # "app_name": app_name
            }
            logger.info("Retrieved terraform outputs successfully")

            # terraform_commands = [
            #     ["terraform", "init"],
            #     ["terraform", "plan", "-out=tfplan"],
            #     ["terraform", "apply", "-auto-approve", "tfplan"]
            # ]

            # for command in terraform_commands:
            #     command_step = get_step_response(
            #         StepStatus.IN_PROGRESS.value,
            #         f"terraform_{command[1]}",
            #         f"Running terraform {command[1]}"
            #     )
            #     yield f"data: {json.dumps(command_step)}\n\n"

            #     process = await asyncio.create_subprocess_exec(
            #         *command,
            #         stdout=asyncio.subprocess.PIPE,
            #         stderr=asyncio.subprocess.PIPE
            #     )
            #     stdout, stderr = await process.communicate()
                
            #     if process.returncode != 0:
            #         raise HTTPException(
            #             status_code=500,
            #             detail=f"Terraform command failed: {' '.join(command)}\n{stderr.decode()}"
            #         )
            #     success_step = get_step_response(
            #         StepStatus.COMPLETED.value,
            #         f"terraform_{command[1]}",
            #         f"Terraform {command[1]} completed successfully"
            #         )
            #     yield f"data: {json.dumps(success_step)}\n\n"
                    
                
            #     # Save plan output
            #     if command[1] == "plan":
            #         plan_output = stdout.decode()
            #     logger.info(f"Completed: {' '.join(command)}")

            # # Get terraform outputs
            # process = await asyncio.create_subprocess_exec(
            #     "terraform", "output", "-json",
            #     stdout=asyncio.subprocess.PIPE,
            #     stderr=asyncio.subprocess.PIPE
            # )
            # stdout, stderr = await process.communicate()
            # terraform_outputs = json.loads(stdout.decode()) if stdout else {}

            secrets = {
            'AWS_ACCESS_KEY_ID': os.getenv("AWS_ACCESS_KEY_ID"),
            'AWS_SECRET_ACCESS_KEY': os.getenv("AWS_SECRET_ACCESS_KEY"),
            'GITHUB_ACCESS_TOKEN': os.getenv("GITHUB_ACCESS_TOKEN"),
            
            }

            secrets_result = await setup_github_secrets(
                repo_path = created_repo_url,
                aws_credentials = secrets, 
                amplify_app_id = "d29skq6zj9tfnm"
            )
            
            # S3 Upload
            logger.info("Starting S3 upload")
            s3_step = get_step_response(
                StepStatus.IN_PROGRESS.value,
                "s3_upload",
                "Uploading to S3"
            )
            yield f"data: {json.dumps(s3_step)}\n\n"
            
            s3_bucket = os.environ.get("S3_BUCKET_NAME")
            if not s3_bucket:
                raise ValueError("S3 bucket name not found in environment variables")

            s3_prefix = f"deployments/{project_id}/{container_id}"

            terraform_files_s3 = await save_terraform_files_to_s3(
                plan_output=plan_output,
                bucket=s3_bucket,
                prefix=s3_prefix
            )
            logger.debug(f"Terraform files uploaded to S3: {s3_bucket}/{s3_prefix}")

            exclude_dirs = ["infrastructure", ".git", "node_modules", ".next"]
            repo_upload_success = await upload_repo_to_s3(
                repo_path=temp_dir,
                bucket=s3_bucket,
                prefix=s3_prefix,
                exclude_dirs=exclude_dirs
            )
            logger.info("Repository uploaded to S3 successfully")
            
            s3_complete = get_step_response(
                StepStatus.COMPLETED.value,
                "s3_upload",
                "Files uploaded to S3 successfully"
            )
            yield f"data: {json.dumps(s3_complete)}\n\n"

            # Final completion
            logger.info("Deployment completed successfully")
            completion_data = {
                "status": "completed",
                "step": "deployment",
                "message": "Infrastructure deployed successfully",
                "data": {
                    "repo_upload": repo_upload_success,
                    "project_id": project_id,
                    "container_id": container_id,
                    "s3_location": f"s3://{s3_bucket}/{s3_prefix}",
                    "saved_files": saved_files,
                    "terraform_outputs": terraform_outputs,
                    "terraform_files_s3": terraform_files_s3,
                    "next_steps": [
                        "Monitor the deployment in AWS Console",
                        "Check GitHub Actions for CI/CD pipeline status",
                        "Update application configurations with new infrastructure details"
                    ]
                }
            }
            yield f"data: {json.dumps(completion_data)}\n\n"

        except FileNotFoundError as e:
            logger.error(f"File operation failed: {str(e)}")
            error_data = get_step_response(
                StepStatus.ERROR.value,
                "error",
                f"File operation failed: {str(e)}"
            )
            yield f"data: {json.dumps(error_data)}\n\n"
        
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {str(e)}")
            error_data = get_step_response(
                StepStatus.ERROR.value,
                "error",
                f"JSON parsing failed: {str(e)}"
            )
            yield f"data: {json.dumps(error_data)}\n\n"

        except Exception as e:
            logger.error(f"Deployment failed: {str(e)}")
            error_data = get_step_response(
                StepStatus.ERROR.value,
                "error",
                f"Error deploying infrastructure: {str(e)}"
            )
            yield f"data: {json.dumps(error_data)}\n\n"

        finally:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.info("Cleaned up temporary directory")
                except Exception as e:
                    logger.error(f"Failed to clean up directory: {str(e)}")

    return StreamingResponse(
        generate_progress(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.post("/{project_id}/{container_id}/{branch}/push_to_new_repo")
async def push_to_new_repo(
    project_id: int,
    container_id: int,
    branch: str,
    repo_url: str,
    db = Depends(get_node_db)
) -> StreamingResponse:
    logger = logging.getLogger(__name__)
    
    async def stream_process():
        

        working_dir = "./deployments_latest"
        try:
            logger.info(f"Starting push to new repo process for project {project_id}, container {container_id}")
            
            # Initialization
            init_step = {
                "status": "in_progress",
                "step": "initialization",
                "message": "Starting repository push process"
            }
            yield f"data: {json.dumps(init_step)}\n\n"

            github_token = os.environ["GITHUB_ACCESS_TOKEN"]
            commit_message = "added repo with git actions"
            force_push = True

            deployment_node = await get_deployment_node(project_id, container_id, db)
            # Get deployment_config string from properties
            deployment_config_str = deployment_node['properties']['deployment_config']

            # Parse the deployment_config JSON string
            deployment_config = json.loads(deployment_config_str)

            # Access the branch value
            branch = deployment_config['branch']
            # branch = deployment_node['properties'].get('deployment_config', {}).get('branch', '')
            # Extract repository name
            repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
            new_repo_name = f"{repo_name}_deploy"
            github_username = "cjay91"
            github_repo_url = f"https://github.com/{github_username}/{new_repo_name}.git"

            # Create temp directory
            temp_clone_dir = f"{working_dir}_full"
            if os.path.exists(temp_clone_dir):
                shutil.rmtree(temp_clone_dir)
            os.makedirs(temp_clone_dir)
            
            logger.info("Created temporary directory for cloning")
            init_complete = {
                "status": "completed",
                "step": "initialization",
                "message": "Setup completed successfully"
            }
            yield f"data: {json.dumps(init_complete)}\n\n"

            # Repository Cloning
            logger.info(f"Cloning repository from {repo_url}")
            clone_step = {
                "status": "in_progress",
                "step": "clone",
                "message": f"Cloning repository from {repo_url}"
            }
            yield f"data: {json.dumps(clone_step)}\n\n"
            
            clone_success = await clone_codecommit_repo(
                repo_url=repo_url,
                local_path=temp_clone_dir,
                branch=branch
            )
            
            if not clone_success:
                raise Exception(f"Failed to clone repository from {repo_url}")

            logger.info("Repository cloned successfully")
            clone_complete = {
                "status": "completed",
                "step": "clone",
                "message": "Repository cloned successfully"
            }
            yield f"data: {json.dumps(clone_complete)}\n\n"

            # Frontend Setup
            logger.info("Setting up frontend directory")
            frontend_step = {
                "status": "in_progress",
                "step": "frontend_setup",
                "message": "Setting up frontend directory"
            }
            yield f"data: {json.dumps(frontend_step)}\n\n"
            
            # frontend_path = os.path.join(temp_clone_dir, 'src/movie-match-app')
            data = await find_application_directory(temp_clone_dir)
            frontend_path = data['absolute_path']
            if not os.path.exists(frontend_path):
                raise Exception("Frontend directory not found in repository")

            if os.path.exists(working_dir):
                shutil.rmtree(working_dir)
            os.makedirs(working_dir)

            shutil.copytree(frontend_path, working_dir, dirs_exist_ok=True)
            shutil.rmtree(temp_clone_dir)

            logger.info("Frontend directory setup completed")
            frontend_complete = {
                "status": "completed",
                "step": "frontend_setup",
                "message": "Frontend directory setup completed"
            }
            yield f"data: {json.dumps(frontend_complete)}\n\n"

            # Workflow Setup
            logger.info("Setting up GitHub workflows")
            workflow_step = {
                "status": "in_progress",
                "step": "workflow_setup",
                "message": "Setting up GitHub workflows"
            }
            yield f"data: {json.dumps(workflow_step)}\n\n"
            
            workflow_content = get_workflows(branch)
            workflows_path = os.path.join(working_dir, '.github/workflows')
            os.makedirs(workflows_path, exist_ok=True)
            
            with open(os.path.join(workflows_path, 'deploy.yml'), 'w') as f:
                f.write(workflow_content)

            logger.info("GitHub workflows setup completed")
            workflow_complete = {
                "status": "completed",
                "step": "workflow_setup",
                "message": "GitHub workflows setup completed"
            }
            yield f"data: {json.dumps(workflow_complete)}\n\n"

            # Repository Creation
            logger.info(f"Creating GitHub repository: {new_repo_name}")
            repo_step = {
                "status": "in_progress",
                "step": "repo_creation",
                "message": f"Creating GitHub repository: {new_repo_name}"
            }
            yield f"data: {json.dumps(repo_step)}\n\n"
            
            g = Github(github_token)
            user = g.get_user()
            created_repo_url = await check_and_create_repo(github_token, github_username, new_repo_name)

            logger.info("GitHub repository created successfully")
            repo_complete = {
                "status": "completed",
                "step": "repo_creation",
                "message": "GitHub repository created successfully"
            }
            yield f"data: {json.dumps(repo_complete)}\n\n"

            # Git Operations
            logger.info("Initializing git repository")
            git_step = {
                "status": "in_progress",
                "step": "git_operations",
                "message": "Initializing git repository"
            }
            yield f"data: {json.dumps(git_step)}\n\n"

            git_commands = [
                ["rm", "-rf", ".git"],
                ['git', 'init'],
                ['git', 'config', 'user.name', 'GitHub Actions'],
                ['git', 'config', 'user.email', '<EMAIL>'],
                ['git', 'remote', 'add', 'origin', created_repo_url],
                ['git', 'add', '-A'],
                ['git', 'commit', '-m', commit_message],
                ['git', 'checkout', '-b', branch],
                ['git', 'push', '-u', 'origin', branch, '--force']
            ]
            
            for cmd in git_commands:
                logger.info(f"Executing git command: {' '.join(cmd)}")
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    cwd=working_dir
                )
                await process.communicate()

            logger.info("Git operations completed successfully")
            git_complete = {
                "status": "completed",
                "step": "git_operations",
                "message": "Git operations completed successfully"
            }
            yield f"data: {json.dumps(git_complete)}\n\n"

            # Amplify Setup
            logger.info("Setting up Amplify")
            amplify_step = {
                "status": "in_progress",
                "step": "amplify_setup",
                "message": "Setting up Amplify"
            }
            yield f"data: {json.dumps(amplify_step)}\n\n"

            amplify_client = boto3.client('amplify', region_name='us-east-2')
            response = amplify_client.list_apps()
            all_apps = response.get('apps', [])
            sorted_apps = sorted(
                all_apps,
                key=lambda x: x.get('createTime', 0),
                reverse=True
            )

            logger.info("Amplify setup completed")
            amplify_complete = {
                "status": "completed",
                "step": "amplify_setup",
                "message": "Amplify setup completed"
            }
            yield f"data: {json.dumps(amplify_complete)}\n\n"

            # Final completion
            logger.info("Repository push completed successfully")
            completion_data = {
                "status": "completed",
                "step": "completion",
                "message": "Repository push completed successfully",
                "data": {
                    "source_repo": repo_url,
                    "new_repo": github_repo_url,
                    "branch": branch,
                    "commit_message": commit_message
                }
            }
            yield f"data: {json.dumps(completion_data)}\n\n"

        except Exception as e:
            logger.error(f"Push to repository failed: {str(e)}")
            error_data = {
                "status": "error",
                "step": "error",
                "message": f"Error pushing to repository: {str(e)}",
                "error": str(e)
            }
            yield f"data: {json.dumps(error_data)}\n\n"
        
        finally:
            logger.info("Operation completed")

    return StreamingResponse(
        stream_process(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
    

@router.post("/{project_id}/{container_id}/{branch}/trigger_github_actions")
async def trigger_github_actions(
    project_id: int,
    container_id: int,
    branch: str,
    repo_url: str,
    db = Depends(get_node_db)
) -> StreamingResponse:
    logger = logging.getLogger(__name__)
    
    async def stream_process():
        try:
            # Initialization
            init_step = {
                "status": "in_progress",
                "step": "initialization",
                "message": "Starting GitHub Actions workflow"
            }
            yield f"data: {json.dumps(init_step)}\n\n"

            github_token = os.environ["GITHUB_ACCESS_TOKEN"]
            g = Github(github_token)

            # Extract repository details
            repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
            deploy_repo_name = f"{repo_name}_deploy"
            github_username = ""
            
            # Get repository and workflow
            repo = g.get_repo(f"{github_username}/{deploy_repo_name}")
            workflows = repo.get_workflows(branch)
            
            # Find deploy.yml workflow
            deploy_workflow = None
            for workflow in workflows:
                if workflow.name == "deploy.yml" or workflow.path == ".github/workflows/deploy.yml":
                    deploy_workflow = workflow
                    break

            if not deploy_workflow:
                raise Exception("deploy.yml workflow not found in repository")
            
            # Trigger workflow
            logger.info(f"Triggering deploy workflow in repository: {deploy_repo_name}")
            trigger_step = {
                "status": "in_progress",
                "step": "trigger",
                "message": "Triggering deploy.yml workflow"
            }
            yield f"data: {json.dumps(trigger_step)}\n\n"
            
            # Create workflow dispatch event
            deploy_workflow.create_dispatch(
                ref=branch,
                inputs={
                    "project_id": str(project_id),
                    "container_id": str(container_id)
                }
            )
            
            trigger_complete = {
                "status": "completed",
                "step": "trigger",
                "message": "Deploy workflow triggered successfully"
            }
            yield f"data: {json.dumps(trigger_complete)}\n\n"

            # Monitor workflow execution
            monitor_step = {
                "status": "in_progress",
                "step": "monitor",
                "message": "Monitoring deploy workflow execution"
            }
            yield f"data: {json.dumps(monitor_step)}\n\n"

            # Wait for workflow to start
            await asyncio.sleep(5)
            
            # Monitor workflow status
            while True:
                runs = deploy_workflow.get_runs(actor=github_username, branch=branch)
                if not runs.totalCount:
                    await asyncio.sleep(2)
                    continue
                    
                latest_run = runs[0]
                status = latest_run.status
                conclusion = latest_run.conclusion
                
                # Get jobs
                jobs = latest_run.jobs()
                if jobs.totalCount > 0:
                    job = jobs[0]
                    
                    # Access steps directly from the job's steps property
                    for step in job.steps:
                        step_status = {
                            "status": "completed" if step.conclusion else "in_progress",
                            "step": "workflow_step",
                            "message": f"Step: {step.name}",
                            "data": {
                                "step_name": step.name,
                                "status": step.conclusion or "in_progress",
                                "started_at": step.started_at.isoformat() if step.started_at else None,
                                "completed_at": step.completed_at.isoformat() if step.completed_at else None
                            }
                        }
                        yield f"data: {json.dumps(step_status)}\n\n"

                if status == "completed":
                    if conclusion == "success":
                        completion_data = {
                            "status": "completed",
                            "step": "completion",
                            "message": "Deploy workflow completed successfully",
                            "data": {
                                "workflow_url": latest_run.html_url,
                                "conclusion": conclusion
                            }
                        }
                    else:
                        completion_data = {
                            "status": "error",
                            "step": "completion",
                            "message": f"Deploy workflow failed with conclusion: {conclusion}",
                            "data": {
                                "workflow_url": latest_run.html_url,
                                "conclusion": conclusion
                            }
                        }
                    yield f"data: {json.dumps(completion_data)}\n\n"
                    break

                await asyncio.sleep(10)

        except Exception as e:
            logger.error(f"GitHub Actions workflow failed: {str(e)}")
            error_data = {
                "status": "error",
                "step": "error",
                "message": f"Error in GitHub Actions workflow: {str(e)}",
                "error": str(e)
            }
            yield f"data: {json.dumps(error_data)}\n\n"
        
        finally:
            logger.info("GitHub Actions monitoring completed")

    return StreamingResponse(
        stream_process(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
    
@router.get("/{app_id}/{branch}/deployment-status")
async def check_deployment_status(
    app_id: str,
    branch: str
) -> StreamingResponse:
    """
    Check deployment status for an Amplify app using event streaming
    
    Args:
        app_id: Amplify App ID
        branch: Branch name
        
    Returns:
        StreamingResponse with deployment status updates
    """
    async def generate_status():
        try:
            amplify_client = boto3.client('amplify')
            
            # Initial status update
            init_data = {
                'status': 'in_progress',
                'step': 'initialization',
                'message': f'Checking deployment status for app {app_id} on branch {branch}'
            }
            yield f"data: {json.dumps(init_data)}\n\n"

            try:
                # Get jobs for the specific branch
                response = amplify_client.list_jobs(
                    appId=app_id,
                    branchName=branch,
                    maxResults=10
                )
                
                jobs = response.get('jobSummaries', [])

                if not jobs:
                    complete_data = {
                        'status': 'completed',
                        'step': 'check',
                        'message': f'No deployments found for branch {branch}',
                        'data': {
                            'app_id': app_id,
                            'branch': branch,
                            'domain': f"https://{branch}.{app_id}.amplifyapp.com"
                        }
                    }
                    yield f"data: {json.dumps(complete_data)}\n\n"
                    return

                # Get latest job
                latest_job = jobs[0]
                status = latest_job['status']

                if status == 'SUCCEED':
                    # Calculate build duration
                    start_time = datetime.fromisoformat(latest_job['startTime'].isoformat())
                    end_time = datetime.fromisoformat(latest_job['endTime'].isoformat())
                    build_duration = end_time - start_time
                    build_minutes = build_duration.total_seconds() / 60
                    
                    # Get app details for domain
                    app_details = amplify_client.get_app(appId=app_id)
                    domain = app_details['app'].get('defaultDomain', '')
                    
                    success_data = {
                        'status': 'completed',
                        'step': 'deployment',
                        'message': 'Deployment completed successfully',
                        'data': {
                            'job_id': latest_job['jobId'],
                            'branch': branch,
                            'status': status,
                            'start_time': start_time.isoformat(),
                            'end_time': end_time.isoformat(),
                            'build_duration': f"{build_minutes:.2f} minutes",
                            'domain': f"https://{branch}.{domain}" if domain else f"https://{branch}.{app_id}.amplifyapp.com"
                        }
                    }
                    yield f"data: {json.dumps(success_data)}\n\n"

                elif status == 'FAILED':
                    error_data = {
                        'status': 'error',
                        'step': 'deployment',
                        'message': 'Deployment failed',
                        'data': {
                            'job_id': latest_job['jobId'],
                            'branch': branch,
                            'status': status,
                            'error': latest_job.get('summary', 'Unknown error'),
                            'start_time': latest_job['startTime'].isoformat(),
                            'end_time': latest_job.get('endTime', '').isoformat() if latest_job.get('endTime') else None
                        }
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"

                else:  # IN_PROGRESS, PENDING, etc.
                    progress_data = {
                        'status': 'in_progress',
                        'step': 'deployment',
                        'message': 'Deployment in progress',
                        'data': {
                            'job_id': latest_job['jobId'],
                            'branch': branch,
                            'status': status,
                            'start_time': latest_job['startTime'].isoformat(),
                            'duration_so_far': f"{(datetime.now() - latest_job['startTime']).total_seconds() / 60:.2f} minutes"
                        }
                    }
                    yield f"data: {json.dumps(progress_data)}\n\n"
                    
                    # Continue monitoring if deployment is in progress
                    while status not in ['SUCCEED', 'FAILED']:
                        await asyncio.sleep(5)
                        
                        response = amplify_client.list_jobs(
                            appId=app_id,
                            branchName=branch,
                            maxResults=1
                        )
                        
                        if response['jobSummaries']:
                            latest_job = response['jobSummaries'][0]
                            status = latest_job['status']
                            
                            if status == 'SUCCEED':
                                start_time = datetime.fromisoformat(latest_job['startTime'].isoformat())
                                end_time = datetime.fromisoformat(latest_job['endTime'].isoformat())
                                build_duration = end_time - start_time
                                build_minutes = build_duration.total_seconds() / 60
                                
                                final_data = {
                                    'status': 'completed',
                                    'step': 'deployment',
                                    'message': 'Deployment completed successfully',
                                    'data': {
                                        'job_id': latest_job['jobId'],
                                        'branch': branch,
                                        'status': status,
                                        'start_time': start_time.isoformat(),
                                        'end_time': end_time.isoformat(),
                                        'build_duration': f"{build_minutes:.2f} minutes",
                                        'domain': f"https://{branch}.{app_id}.amplifyapp.com"
                                    }
                                }
                                yield f"data: {json.dumps(final_data)}\n\n"
                                break
                                
                            elif status == 'FAILED':
                                final_error = {
                                    'status': 'error',
                                    'step': 'deployment',
                                    'message': 'Deployment failed',
                                    'data': {
                                        'job_id': latest_job['jobId'],
                                        'branch': branch,
                                        'status': status,
                                        'error': latest_job.get('summary', 'Unknown error'),
                                        'start_time': latest_job['startTime'].isoformat(),
                                        'end_time': latest_job.get('endTime', '').isoformat() if latest_job.get('endTime') else None
                                    }
                                }
                                yield f"data: {json.dumps(final_error)}\n\n"
                                break
                            
                            else:
                                progress_update = {
                                    'status': 'in_progress',
                                    'step': 'deployment',
                                    'message': 'Deployment in progress',
                                    'data': {
                                        'job_id': latest_job['jobId'],
                                        'branch': branch,
                                        'status': status,
                                        'start_time': latest_job['startTime'].isoformat(),
                                        'duration_so_far': f"{(datetime.now() - latest_job['startTime']).total_seconds() / 60:.2f} minutes"
                                    }
                                }
                                yield f"data: {json.dumps(progress_update)}\n\n"

            except amplify_client.exceptions.NotFoundException as e:
                error_data = {
                    'status': 'error',
                    'step': 'check',
                    'message': f'App or branch not found: {str(e)}'
                }
                yield f"data: {json.dumps(error_data)}\n\n"

            except Exception as e:
                logger.error(f"Error checking deployment status: {str(e)}")
                error_data = {
                    'status': 'error',
                    'step': 'check',
                    'message': f'Error checking deployment status: {str(e)}'
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        except Exception as e:
            logger.error(f"Error in deployment status check: {str(e)}")
            error_data = {
                'status': 'error',
                'step': 'error',
                'message': f'Error in deployment status check: {str(e)}'
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_status(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
  

@router.get("/{project_id}/{container_id}/get_deployment_form")
async def get_deployment_form(
    project_id: int,
    container_id: int,
    db = Depends(get_node_db)
) -> JSONResponse:
    """
    Get deployment form configuration and any existing values
    
    Args:
        project_id: Project ID
        container_id: Container ID
    Returns:
        Form configuration and existing values
    """
    try:
        # Get deployment node
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        if not deployment_nodes:
            raise HTTPException(status_code=404, detail="Deployment configuration not found")
        
        deployment_node = deployment_nodes[0]
        
        
        project_repositories = await get_repository(project_id, container_id, db)
        if "error" in project_repositories:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        repo_info = project_repositories["repository"]
        repo_url = repo_info.get("cloneUrlHttp", "")
        repo_name = repo_info.get("repositoryName", "")
        github_token = os.environ["GITHUB_ACCESS_TOKEN"]

        repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
        new_repo_name = f"{repo_name}_deploy"
        github_username = "cjay91"
        
        github_username = "cjay91"
        github_repo_url = f"https://github.com/{github_username}/{new_repo_name}.git"
        
        branches_info = await list_branches(project_id, container_id, db)
        branch_options = []
        for branch in branches_info.get("branches", []):
            # Convert datetime objects to strings if they exist
            branch_data = {
                "label": f"{branch['name']}", 
                "value": branch['name']
            }
            branch_options.append(branch_data)

        default_branch = branches_info.get("branches", [{}])[0].get("name", "kavia-main")

        # Generate unique domain name
        domain_name = generate_unique_domain("basic-quickvote")

        # Get existing configuration or create default
        existing_config = deployment_node['properties'].get('deployment_config', {})
        # Create default configuration
        default_config = {
            "repo_url": github_repo_url,
            "branch": default_branch,  # Just store the branch name, not the full info
            "app_name": f"{repo_name}-app",
            "domain_name": domain_name
        }
                
        # Update deployment node with default config
        updated_properties = {
            **deployment_node['properties'],
            'deployment_config': json.dumps(default_config),
            'updated_at': datetime.now().isoformat()  # Convert datetime to string
        }
        
        await db.update_node_by_id(
            deployment_node['id'],
            updated_properties
        )
        
        existing_config = default_config
        print(existing_config)

        if isinstance(existing_config, str):
            try:
                config_dict = json.loads(existing_config)
                # Extract the form configuration
                existing_form_config = config_dict.get("form_config", {})
                # Get the fields
                existing_fields = {
                    field["name"]: field["value"] 
                    for field in existing_form_config.get("fields", [])
                } if existing_form_config else {}
            except json.JSONDecodeError:
                existing_fields = {}
        else:
            existing_fields = {}

        form_config = {
            "title": "Deployment Configuration",
            "description": "Configure your infrastructure deployment settings",
            "fields": [
                {
                    "name": "repo_url",
                    "label": "Repository URL",
                    "type": "text",
                    "placeholder": "https://github.com/username/repo.git",
                    "required": True,
                    "value": github_repo_url,
                    "validation": "url"
                },
                {
                    "name": "branch",
                    "label": "Branch",
                    "type": "select",
                    "required": True,
                    "value": existing_fields.get("branch", default_branch),
                    "options": branch_options  
                },
                {
                    "name": "app_name",
                    "label": "Application Name",
                    "type": "text",
                    "placeholder": "my-application",
                    "required": True,
                    "value": repo_name
                },
                {
                    "name": "domain_name",
                    "label": "Domain Name",
                    "type": "text",
                    "placeholder": "example.com",
                    "required": False,
                    "value": domain_name
                },
                {
                    "name": "aws_region",
                    "label": "AWS Region",
                    "type": "select",
                    "required": True,
                    "value": "us-east-2",
                    "options": [
                        {"label": "US West (Oregon)", "value": "us-west-2"},
                        {"label": "US East (N. Virginia)", "value": "us-east-1"},
                        {"label": "EU (Ireland)", "value": "eu-west-1"},
                        {"label": "Asia Pacific (Singapore)", "value": "ap-southeast-1"}
                    ]
                }
            ],
        "submit": {
        "label": "Deploy Infrastructure",
        "endpoint": f"/api/deployment/{project_id}/{container_id}/update_deployment_config",
        "method": "POST"
    }
        }
        
        return JSONResponse(
            status_code=200,
            content={
                "form_config": form_config,
                "deployment_node_id": deployment_node['id']
            }
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deployment form configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    

@router.post("/{project_id}/{container_id}/update_deployment_config")
async def update_deployment_config(
    project_id: int,
    container_id: int,
    form_data: dict = Body(...),
    db: NodeDB = Depends(get_node_db)
) -> JSONResponse:
    """
    Update deployment configuration and store for template generation
    """
    try:
        # Get deployment node
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        if not deployment_nodes:
            raise HTTPException(status_code=404, detail="Deployment configuration not found")
        
        deployment_node = deployment_nodes[0]
        
        # Store form data for template generation
        updated_properties = {
            **deployment_node['properties'],
            'deployment_config': form_data,
            'updated_at': datetime.now().isoformat()
        }
        
        # Update node in database
        result = await db.update_node_by_id(
            deployment_node['id'],
            updated_properties
        )
        
        if not result:
            raise HTTPException(
                status_code=500,
                detail="Failed to update deployment configuration"
            )
            
        return JSONResponse(
            status_code=200,
            content={
                "message": "Deployment configuration updated successfully",
                "deployment_config": form_data
            }
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deployment configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))