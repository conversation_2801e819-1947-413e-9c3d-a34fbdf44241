import os
import shutil

from app.utils.respository_utils import process_files

def setup_workspace(repo, project_id, tenant_id, task_id):
    base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

    # Create temp workspace directory
    temp_workspace = f'/tmp/kavia/workspace/{task_id}'
    os.makedirs(temp_workspace, exist_ok=True)

    selected_repos = repo['selected_repos']
    
    for repo_name in selected_repos:
        # Get the repository name without owner (e.g., 'filetrace' from 'Kavia-ai/filetrace')
        repo_dirname = repo_name.split('/')[-1]
        # Create repo-specific directory in workspace
        repo_workspace = os.path.join(temp_workspace, repo_dirname)
        os.makedirs(repo_workspace, exist_ok=True)
        
        # Convert repo name to path format
        repo_path = repo_name.replace('/', os.sep)
        
        if os.environ.get('LOCAL_DEBUG'):
            source_path = os.path.join(base_path, 'data', str(tenant_id), str(project_id), repo_path)
        else:
            source_path = os.path.join('/', 'efs', str(tenant_id), str(project_id), repo_path)
            
        # Copy all files from source path to repo workspace
        if os.path.exists(source_path):
            for item in os.listdir(source_path):
                source_item = os.path.join(source_path, item)
                
                # Copy everything else to repo_workspace
                dest_item = os.path.join(repo_workspace, item)
                if os.path.isdir(source_item):
                    shutil.copytree(source_item, dest_item)
                else:
                    shutil.copy2(source_item, dest_item)

        # Handle .knowledge directory for single repository
        process_files(repo_workspace, repo_dirname, temp_workspace)
        
        knowledge_dest = os.path.join(temp_workspace, '.knowledge')
        os.makedirs(knowledge_dest, exist_ok=True)
        repo_workspace_knowledge = os.path.join(repo_workspace, '.knowledge')
        
        if os.path.exists(repo_workspace_knowledge):
            for file in os.listdir(repo_workspace_knowledge):
                src_file = os.path.join(repo_workspace_knowledge, file)
                dst_file = os.path.join(knowledge_dest, file)
                shutil.move(src_file, dst_file)
    
    return temp_workspace