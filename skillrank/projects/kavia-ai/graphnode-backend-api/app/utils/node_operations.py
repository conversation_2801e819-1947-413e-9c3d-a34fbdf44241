import json

from app.connection.establish_db_connection import get_node_db
from app.routes.node_route import get_child_nodes, get_node, get_nodes
from app.connection.establish_db_connection import connect_mongo_db, get_node_db, get_vector_db
from app.core.Settings import settings
from app.utils.auth_utils import get_current_user
from app.utils.node_utils import get_node_type
from app.connection.establish_db_connection import get_mongo_db



class NodeOperations:

    global mongo_handler
    mongo_handler = get_mongo_db(collection_name='confirmation')

    def __init__(self, function_name: str, function_arguments: dict):
        self.function_name = function_name
        self.function_arguments = function_arguments
        self.node_types = ['Project']
        self.node_db = get_node_db()
        
    async def execute(self):
        if hasattr(self, self.function_name):
            method_to_call = getattr(self, self.function_name)
            return await method_to_call(**self.function_arguments)
        else:
            raise NotImplementedError(
                f"Function '{self.function_name}' is not implemented in NodeOperations")

    async def create_project(self, **function_args):

        name = function_args.get("title")
        description = function_args.get("description")
        node_type = 'Project'


        if node_type in self.node_types and not name and not description:
            return 'Please provide the name, description and specify '.join(self.node_types), False

        params = {
            'request': {
                'node_type': node_type,
                'title': name,
                'description': description
            }
        }

        details = {
            'Node Type': node_type,
            'Title': name,
            'Description': description
        }

        asking_confirmation = {
            'operation': 'create',
            'function': self.function_name,
            'type': node_type,
            'is_active': 1,
            'parameters': params,
            'details': details
        }

        mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

        # append the mongo_result _id as the task_id to that data variable
        asking_confirmation["task_id"] = str(mongo_result['_id'])

        return asking_confirmation, True

    async def update_node(self, **function_args):
        node_id = function_args.get("node_id")
        name = function_args.get("name")
        description = function_args.get("description")
        node_type = get_node_type(function_args.get('node_type'))
        print("node_id", node_id)

        if not node_id or (node_type in self.node_types and not name and not description):
            return 'Please provide the node_id, name, description, and specify the node type.', False

        call_fn = await get_node(node_id, node_type, get_node_db())
        print("call_fn", call_fn)

        if call_fn:
            params = {
                'node_id': node_id,
                'request': {
                    'node_type': node_type,
                    'properties': {
                        'Name': name,
                        'Description': description,
                    }
                }

            }

            details = {
                'Node Type': node_type,
                'Name': name,
                'Description': description
            }

            asking_confirmation = {
                'operation': 'update',
                'function': self.function_name,
                'type': node_type,
                'is_active': 1,
                'parameters': params,
                'details': details
            }

            mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

            # append the mongo_result _id as the task_id to that data variable
            asking_confirmation["task_id"] = str(mongo_result['_id'])

            return asking_confirmation, True

        else:
            return 'Node not found in the database. Please try again with a different ID.', False

    async def delete_node(self, **function_args):
        node_type = get_node_type(function_args.get("node_type"))
        node_id = function_args.get("node_id")

        if not node_id and node_type in self.node_types:
            return 'Please provide the name, description and specify '.join(self.node_types), False

        call_fn = await get_node(node_id, node_type, get_node_db())

        if call_fn:
            params = {
                'node_id': node_id,
                'node_type': call_fn['labels'][0]
            }

            details = {
                'Id': node_id,
                'Name': call_fn['properties']['Name']
            }

            asking_confirmation = {
                'operation': 'delete',
                'function': self.function_name,
                'type': node_type,
                'is_active': 1,
                'parameters': params,
                'details': details
            }
            mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

            # append the mongo_result _id as the task_id to that data variable
            asking_confirmation["task_id"] = str(mongo_result['_id'])

            return asking_confirmation, True

        else:
            return 'Please try again with different ID.it not found in the database.', False

    async def get_nodes(self, **kwargs):
        node_type = get_node_type(kwargs.get("node_type"))
        if node_type not in ['Requirement'] and node_type == 'Project':
            result = await get_nodes(node_type, db=get_node_db())
            if result:
                data = result
                result = ""
                for node in data:
                    node_id = node['id']
                    node_name = node['properties']['Name']
                    result += "node_type" + node_type + "id : " + \
                        str(node_id) + "name" + node_name
            else:
                result = f"There is no {node_type} available in database"
        else:
            result = f"we can't able list all the {node_type} but you can ask for specifc project"
        
        return result

    async def get_node(self, **function_args):
        node_id = function_args.get("node_id")
        node_type = get_node_type(function_args.get('node_type'))
        
        if node_id and node_type:
            result = await get_node(node_id, node_type.capitalize(), get_node_db(), get_current_user())
        else:
            result = "Node id and type is not found."
        
        return str(result)
                
    async def create_requirement(self, **function_args):
        title = function_args.get("title")
        description = function_args.get("description")
        project_name = function_args.get("project_name")

        print(project_name)
        print(type(project_name))
        # find the product id
        project = await self.node_db.get_node_by_property('Project', 'Title', project_name)
        if project is None:
            return 'Please proivde the correct project name.', False
        
        project_id = project['id']
        
        if not title and not description and not project_id:
            return 'Please provide the title, description and specify ', False

        rootWorkItem = await get_child_nodes(int(project_id), 'Project', 'RequirementRoot', db=get_node_db())
        root_node_id = rootWorkItem[0]['id']
        result = await self.node_db.get_node_by_id(int(project_id))

        params = {
            'request': {
                'node_type': "Epic",
                'name': title,
                'description': description,
                'properties': {
                    "Title": title,
                    "Description": description,
                    "Type": "Epic",
                    "parent_id": root_node_id
                }
            }
        }

        details = {
            'Product Name': result['properties']['Name'],
            'Node Type': 'Epic',
            'Title': title,
            'Description': description,
        }

        asking_confirmation = {
            'operation': 'create',
            'function': "create_node",
            'type': "Requirement",
            'is_active': 1,
            'parameters': params,
            'details': details
        }

        mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

        # append the mongo_result _id as the task_id to that data variable
        asking_confirmation["task_id"] = str(mongo_result['_id'])

        return asking_confirmation, True

    async def delete_requirement(self, **function_args):
        requirement_id = function_args.get("requirement_id")
        if not requirement_id:
            return 'Please provide the name, description and specify '.join(self.requirement_id), False

        call_fn = await get_node(int(requirement_id), "Epic", get_node_db())

        if call_fn:
            params = {
                'node_id': requirement_id,
                'node_type': call_fn['labels'][0]
            }

            details = {
                'Id': requirement_id,
                'Name': call_fn['properties']['Title']
            }

            asking_confirmation = {
                'operation': 'delete',
                'function': "delete_node",
                'type': "Epic",
                'is_active': 1,
                'parameters': params,
                'details': details
            }
            mongo_result = await mongo_handler.create({'function_call': asking_confirmation}, mongo_handler.db)

            # append the mongo_result _id as the task_id to that data variable
            asking_confirmation["task_id"] = str(mongo_result['_id'])

            return asking_confirmation, True
        else:
            return False, False
