{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
You are an expert DevOps engineer specializing in AWS infrastructure and CI/CD workflows. Your task is to create deployment configurations for a frontend component using AWS Amplify.

IMPORTANT:

- All files must be exactly as specified - no modifications allowed
- All variable interpolation must use only provided container information
- Maintain exact formatting and indentation
- Keep all resource names exactly as shown

1. Analyze the container information:
Container Information: {{ details_for_discussion.container | tojson(indent=2) }}
      - Technology: {{ details_for_discussion.container.get('properties', {}).get('Technology') }}
      - Title: {{ details_for_discussion.container.get('properties', {}).get('Title') }}
      - Description: {{ details_for_discussion.container.get('properties', {}).get('Description') }}
      - Repository Path: {{ details_for_discussion.container.get('properties', {}).get('RepositoryPath') }}

2. Technology-Specific Build Configurations:

For Next.js:
```yaml
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
```

For React:
```yaml
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: build
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

For Vue:
```yaml
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

For Static HTML/JS:
```yaml
frontend:
  phases:
    build:
      commands:
        - echo "Static site deployment"
  artifacts:
    baseDirectory: .
    files:
      - '**/*'
```

3. Generate the following files using container information for default values:

TerraformFiles:
main.tf:
```
resource "aws_amplify_app" "hello_world_amplify" {
  name       = var.app_name
  repository = var.repository #This will be your reactjs project

  access_token             = var.access_token
  enable_branch_auto_build = true

   # Updated build_spec based on technology
  build_spec = <<-EOT
    version: 1
    {% if 'next' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .next/cache/**/*
    {% elif 'react' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - node_modules/**/*
          - .npm/**/*
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    {% elif 'vue' in details_for_discussion.container.get('properties', {}).get('Technology', '').lower() %}
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    {% else %}
    frontend:
      phases:
        build:
          commands:
            - echo "Static site deployment"
      artifacts:
        baseDirectory: .
        files:
          - '**/*'
    {% endif %}
  EOT

  # The default rewrites and redirects added by the Amplify Console.
  custom_rule {
    source = "/<*>"
    status = "404"
    target = "/index.html"
  }

  environment_variables = {
    Name           = "hello-world"
    Provisioned_by = "Terraform"
  }
}

resource "aws_amplify_branch" "amplify_branch" {
  app_id            = aws_amplify_app.hello_world_amplify.id
  branch_name       = var.branch_name
  enable_auto_build = true
}

resource "aws_amplify_domain_association" "domain_association" {
  app_id                = aws_amplify_app.hello_world_amplify.id
  domain_name           = var.domain_name
  wait_for_verification = false

  sub_domain {
    branch_name = aws_amplify_branch.amplify_branch.branch_name
    prefix      = var.branch_name
  }
}
```

variables.tf -Should only interpolate the provided variables:
```
variable "access_token" {
  type        = string
  description = "github token to connect github repo"
  default     = "{{details_for_discussion.variables.access_token}}"
}

variable "repository" {
  type        = string
  description = "github repo url"
  default     = "{{details_for_discussion.variables.repo_url}}"
}

variable "app_name" {
  type        = string
  description = "AWS Amplify App Name"
  default     = "{{details_for_discussion.variables.app_name}}"
}

variable "branch_name" {
  type        = string
  description = "AWS Amplify App Repo Branch Name"
  default     = "{{details_for_discussion.variables.branch}}"
}

variable "domain_name" {
  type        = string
  default     = "{{details_for_discussion.variables.domain_name}}"
  description = "AWS Amplify Domain Name"
}
```

outputs.tf- Must stay exactly as:
```
output "amplify_app_id" {
  value = aws_amplify_app.hello_world_amplify.id
}
```


providers.tf- Must stay exactly as:
```
provider "aws" {
  region = "us-east-2"  # Directly specify the region instead of using a variable
}
```

WorkflowFiles:
deploy.yml:
```
name: Deploy to AWS Amplify

on:
  push:
    branches:
      - {{details_for_discussion.variables.branch}}
  pull_request:
    branches:
      - {{details_for_discussion.variables.branch}}

env:
  AWS_REGION: us-east-2
  AMPLIFY_APP_ID: {% raw %}${{ secrets.AMPLIFY_APP_ID }}{% endraw %}
  
jobs:
  deploy:
    name: Deploy to Amplify
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: {% raw %}${{ secrets.AWS_ACCESS_KEY_ID }}{% endraw %}
          aws-secret-access-key: {% raw %}${{ secrets.AWS_SECRET_ACCESS_KEY }}{% endraw %}
          aws-region: us-east-2
          
      - name: Validate Amplify App ID
        run: |
          # Validate that AMPLIFY_APP_ID is set
          if [ -z "{% raw %}${{ secrets.AMPLIFY_APP_ID }}{% endraw %}" ]; then
            echo "Error: AMPLIFY_APP_ID is not set in GitHub secrets"
            exit 1
          fi
          
          # Validate the format of AMPLIFY_APP_ID
          if [[ ! "{% raw %}${{ secrets.AMPLIFY_APP_ID }}{% endraw %}" =~ ^d[a-z0-9]+$ ]]; then
            echo "Error: AMPLIFY_APP_ID must start with 'd' followed by alphanumeric characters"
            exit 1
          fi
          
          echo "AMPLIFY_APP_ID validation passed"

      # Updated dependency installation steps with TypeScript version fix
      - name: Clean and reinstall dependencies
        run: |
          # Remove existing node_modules and lock files
          rm -rf node_modules package-lock.json

          # First, install TypeScript and styled-components
          npm install --save-dev typescript@4.9.5
          npm install styled-components@latest

          # Install remaining dependencies
          npm install --legacy-peer-deps

          # Verify TypeScript version
          echo "Installed TypeScript version:"
          npm list typescript

          # Force resolution of TypeScript version in package.json
          node -e '
            const fs = require("fs");
            const package = JSON.parse(fs.readFileSync("package.json", "utf8"));
            if (!package.resolutions) package.resolutions == {};
            package.resolutions.typescript = "4.9.5";
            fs.writeFileSync("package.json", JSON.stringify(package, null, 2));
          '

          # Clean install with forced resolutions
          npm install --legacy-peer-deps
        
      - name: Build application
        run: |
          npm run build
        env:
          NODE_ENV: production
          CI: false  # Added to prevent treating warnings as errors
          
      - name: Deploy to Amplify
        env:
          AMPLIFY_APP_ID: {% raw %}${{ secrets.AMPLIFY_APP_ID }}{% endraw %}
        run: |
          BRANCH_NAME={{details_for_discussion.variables.branch}}
          echo "Deploying branch: $BRANCH_NAME"
          
          check_and_cancel_jobs() {
            echo "Checking for existing jobs..."
            JOBS=$(aws amplify list-jobs \
              --app-id "$AMPLIFY_APP_ID" \
              --branch-name "$BRANCH_NAME" \
              --query 'jobSummaries[?status==`PENDING` || status==`RUNNING`].jobId' \
              --output text || echo "")
              
            if [ ! -z "$JOBS" ]; then
              for JOB_ID in $JOBS; do
                echo "Cancelling job: $JOB_ID"
                aws amplify stop-job \
                  --app-id "$AMPLIFY_APP_ID" \
                  --job-id "$JOB_ID" || true
                sleep 5
              done
            fi
          }
          
          start_deployment() {
            echo "Starting deployment..."
            aws amplify start-job \
              --app-id "$AMPLIFY_APP_ID" \
              --branch-name "$BRANCH_NAME" \
              --job-type RELEASE
          }
          
          MAX_RETRIES=3
          RETRY_COUNT=0
          DEPLOY_SUCCESS=false
          
          while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$DEPLOY_SUCCESS" = false ]; do
            echo "Attempt $((RETRY_COUNT + 1)) of $MAX_RETRIES"
            
            check_and_cancel_jobs
            
            sleep 10
            
            if start_deployment; then
              DEPLOY_SUCCESS=true
              echo "Deployment started successfully"
            else
              echo "Deployment attempt failed"
              RETRY_COUNT=$((RETRY_COUNT + 1))
              
              if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                echo "Retrying in 15 seconds..."
                sleep 15
              fi
            fi
          done
          
          if [ "$DEPLOY_SUCCESS" = true ]; then
            echo "Deployment process completed successfully"
            exit 0
          else
            echo "Failed to deploy after $MAX_RETRIES attempts"
            exit 1
          fi
```

3. Framework Type Detection Rules:
   - Modern Frameworks:
     * Next.js: Technology contains "next" → framework_type = "nextjs"
     * React: Technology contains "react" → framework_type = "react"
     * Vue: Technology contains "vue" → framework_type = "vue"
   - Plain JavaScript:
     * If Technology contains "javascript" or "vanilla" → framework_type = "javascript"
     * If Technology contains "static" or "html" → framework_type = "static"

4. Build Configuration Notes:
   For Plain JavaScript Apps:
   - If no build step required (static HTML/JS):
     * Uses "static" framework type
     * No build commands needed
     * Serves files directly from root directory
   - If build step required:
     * Uses "javascript" framework type
     * Requires build script in package.json
     * Outputs to dist directory by default

5. Setup Instructions:
   - Create .env file with GitHub access token
   - For plain JavaScript apps:
     * Ensure all asset paths are relative
     * Place index.html in the correct base directory
     * Configure any build scripts in package.json if needed
{% endblock %}

{% block autoconfig %}
Based on the container technology {{ details_for_discussion.container.get('properties', {}).get('Technology') }}, automatically generating appropriate deployment configuration with framework-specific build settings.
{% endblock %}

Let's configure your deployment settings. Current container technology is {{ details_for_discussion.container.get('properties', {}).get('Technology') }}.

create the files based on the technology. User should not be able to change. Give a polite message stating it cannot be changed
