from fastapi import APIRouter, HTTPException,Depends
from app.services.supabase_oauth import supabase_service
from app.services.supabase_database_service import SupabaseDatabaseService
import secrets
from datetime import datetime
from app.utils.auth_utils import get_current_user
from typing import Optional 
from datetime import datetime
from fastapi.responses import HTMLResponse
from app.utils.hash import encrypt_data
router = APIRouter(prefix="/supabase", tags=["supabase"])



# Initialize the services
supabase_db_service = SupabaseDatabaseService()

@router.get("/connect-supabase/{project_id}")
async def connect_supabase(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """
    Single endpoint to initiate Supabase OAuth connection
    Returns auth URL instead of redirecting
    """
    try:
        user_id=current_user.get("cognito:username")  # Use 'sub' as we discussed earlier
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Processing OAuth for user_id: {user_id}, project_id: {project_id}")
        
        # Check if user already has Supa<PERSON> connected for this project
        existing_tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
        
        if existing_tokens:
            print(f"[DEBUG] Supabase already connected for this project")
            return {
                "success": True,
                "message": "Supabase already connected for this project",
                "status": "already_connected",
                "connected_at": existing_tokens.get("connected_at"),
                "auth_url": None
            }
        
        # Generate OAuth URL
        try:
            auth_url, state, code_verifier = supabase_service.get_authorization_url(user_id, project_id)
            print(f"[SUCCESS] Generated OAuth URL successfully")
        except Exception as e:
            print(f"[ERROR] Error in get_authorization_url: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to generate OAuth URL: {str(e)}")
        
        # Store OAuth state
        try:
            store_result = await supabase_db_service.store_oauth_state(state, code_verifier, user_id, project_id)
            if store_result.get("success"):
                print(f"[SUCCESS] Stored OAuth state successfully")
            else:
                raise Exception(store_result.get("error", "Unknown error"))
        except Exception as e:
            print(f"[ERROR] Error storing OAuth state: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to store OAuth state: {str(e)}")
        
        print(f"[DEBUG] AUTH_URL = {auth_url}")
        
        # Return auth URL in dictionary format
        return {
            "success": True,
            "message": "OAuth URL generated successfully",
            "status": "auth_url_generated",
            "auth_url": auth_url,
            "data": {
                "user_id": user_id,
                "project_id": project_id,
                "state": state
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in connect_supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to initiate OAuth: {str(e)}")

public_auth_router = APIRouter()

# @public_auth_router.get("/auth/supabase/callback")
# async def supabase_oauth_callback(
#     code: str,
#     state: str,
#     error: Optional[str] = None
# ):
#     """
#     Handle OAuth callback and redirect directly to frontend
#     """
    
#     # Base frontend URL
#     frontend_base_url = os.getenv('FRONTEND_URL', 'http://localhost:3000')
    
#     if error:
#         # Redirect to frontend with error
#         error_params = urllib.parse.urlencode({
#             'supabase_error': error,
#             'status': 'error'
#         })
#         return RedirectResponse(url=f"{frontend_base_url}/error?{error_params}")

#     try:
#         print(f"[DEBUG] Processing OAuth callback - code: {code[:10]}..., state: {state}")
        
#         # 1. Get and validate the stored state
#         oauth_data = await supabase_db_service.get_oauth_state(state)
        
#         if not oauth_data:
#             print(f"[ERROR] Invalid or expired OAuth state: {state}")
#             error_params = urllib.parse.urlencode({
#                 'supabase_error': 'Invalid or expired OAuth state',
#                 'status': 'error'
#             })
#             return RedirectResponse(url=f"{frontend_base_url}/error?{error_params}")
        
#         user_id = oauth_data["user_id"]
#         project_id = oauth_data["project_id"]
#         code_verifier = oauth_data["code_verifier"]
        
#         print(f"[DEBUG] OAuth state validated - user: {user_id}, project: {project_id}")
        
#         # 2. Exchange authorization code for tokens
#         tokens = await supabase_service.exchange_code_for_tokens(code, code_verifier)
        
#         if isinstance(tokens, dict) and "error" in tokens:
#             print(f"[ERROR] Token exchange failed: {tokens['error']}")
#             error_params = urllib.parse.urlencode({
#                 'supabase_error': f"Token exchange failed: {tokens['error']}",
#                 'status': 'error'
#             })
#             return RedirectResponse(url=f"{frontend_base_url}/error?{error_params}")
        
#         if not tokens.get("access_token"):
#             print(f"[ERROR] No access token in response: {tokens}")
#             error_params = urllib.parse.urlencode({
#                 'supabase_error': 'No access token received from Supabase',
#                 'status': 'error'
#             })
#             return RedirectResponse(url=f"{frontend_base_url}/error?{error_params}")
        
#         print(f"[SUCCESS] Tokens received - access_token: {tokens['access_token'][:20]}...")
        
#         # 3. Store credentials
#         store_result = await supabase_db_service.store_user_supabase_tokens(user_id, project_id, tokens)
        
#         if not store_result.get("success"):
#             print(f"[ERROR] Failed to store tokens: {store_result.get('error')}")
#             error_params = urllib.parse.urlencode({
#                 'supabase_error': f"Failed to store credentials: {store_result.get('error')}",
#                 'status': 'error'
#             })
#             return RedirectResponse(url=f"{frontend_base_url}/error?{error_params}")
        
#         # 4. Update project status
#         try:
#             from app.connection.establish_db_connection import get_node_db
#             node_db = get_node_db()
#             await node_db.update_node_by_id(
#                 project_id,
#                 {
#                     "supabase_connected": True,
#                     "supabase_connected_at": datetime.utcnow().isoformat()
#                 }
#             )
#             print(f"[SUCCESS] Project status updated for project: {project_id}")
#         except Exception as e:
#             print(f"[WARNING] Failed to update project status: {e}")
        
#         # 5. Redirect to frontend with success parameters
#         print(f"[SUCCESS] OAuth callback completed successfully")
        
#         success_params = urllib.parse.urlencode({
#             'supabase_connected': 'true',
#             'status': 'success',
#             'connected_at': datetime.utcnow().isoformat(),
#             'project_id': project_id
#         })
        
#         # Redirect to project page or dashboard
#         redirect_url = f"{frontend_base_url}/project/{project_id}?{success_params}"
#         return RedirectResponse(url=redirect_url)
        
#     except Exception as e:
#         print(f"[ERROR] Callback processing failed: {str(e)}")
#         import traceback
#         traceback.print_exc()
        
#         error_params = urllib.parse.urlencode({
#             'supabase_error': f"Callback processing failed: {str(e)}",
#             'status': 'error'
#         })
#         return RedirectResponse(url=f"{frontend_base_url}/error?{error_params}")


public_auth_router = APIRouter()
@public_auth_router.get("/auth/supabase/callback")
async def supabase_oauth_callback(
    code: str,
    state: str,
    error: Optional[str] = None
):
    """
    Handle OAuth callback and send message to parent window
    """
    
    if error:
        # Return error page that sends message to parent
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Supabase Connection Error</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>Connection Failed</h2>
                <p>Error: {error}</p>
                <p>This window will close automatically...</p>
            </div>
            <script>
                window.opener.postMessage({{
                    type: 'SUPABASE_OAUTH_ERROR',
                    error: '{error}'
                }}, window.location.origin);
                setTimeout(() => window.close(), 2000);
            </script>
        </body>
        </html>
        """)

    try:
        print(f"[DEBUG] Processing OAuth callback - code: {code[:10]}..., state: {state}")
        
        # 1. Get and validate the stored state
        oauth_data = await supabase_db_service.get_oauth_state(state)
        
        if not oauth_data:
            return HTMLResponse("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supabase Connection Error</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>Connection Failed</h2>
                    <p>Invalid or expired OAuth state</p>
                    <p>This window will close automatically...</p>
                </div>
                <script>
                    window.opener.postMessage({
                        type: 'SUPABASE_OAUTH_ERROR',
                        error: 'Invalid or expired OAuth state'
                    }, window.location.origin);
                    setTimeout(() => window.close(), 2000);
                </script>
            </body>
            </html>
            """)
        
        user_id = oauth_data["user_id"]
        project_id = oauth_data["project_id"]
        code_verifier = oauth_data["code_verifier"]
        
        # 2. Exchange authorization code for tokens
        tokens = await supabase_service.exchange_code_for_tokens(code, code_verifier)
        
        if isinstance(tokens, dict) and "error" in tokens:
            return HTMLResponse(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supabase Connection Error</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>Connection Failed</h2>
                    <p>Token exchange failed: {tokens['error']}</p>
                    <p>This window will close automatically...</p>
                </div>
                <script>
                    window.opener.postMessage({{
                        type: 'SUPABASE_OAUTH_ERROR',
                        error: 'Token exchange failed: {tokens["error"]}'
                    }}, window.location.origin);
                    setTimeout(() => window.close(), 2000);
                </script>
            </body>
            </html>
            """)
        
        # 3. Store credentials
        store_result = await supabase_db_service.store_user_supabase_tokens(user_id, project_id, tokens)
        
        if not store_result.get("success"):
            return HTMLResponse(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Supabase Connection Error</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>Connection Failed</h2>
                    <p>Failed to store credentials: {store_result.get('error')}</p>
                    <p>This window will close automatically...</p>
                </div>
                <script>
                    window.opener.postMessage({{
                        type: 'SUPABASE_OAUTH_ERROR',
                        error: 'Failed to store credentials: {store_result.get("error")}'
                    }}, window.location.origin);
                    setTimeout(() => window.close(), 2000);
                </script>
            </body>
            </html>
            """)
        
        # 4. Update project status
        try:
            from app.connection.establish_db_connection import get_node_db
            node_db = get_node_db()
            await node_db.update_node_by_id(
                project_id,
                {
                    "supabase_connected": True,
                    "supabase_connected_at": datetime.utcnow().isoformat()
                }
            )
        except Exception as e:
            print(f"[WARNING] Failed to update project status: {e}")
        
        # 5. Return success page that sends message to parent
        connected_at = datetime.utcnow().isoformat()
        
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Supabase Connected Successfully</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <div style="width: 64px; height: 64px; background: #10B981; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                    <svg width="32" height="32" fill="white" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h2 style="color: #10B981; margin-bottom: 10px;">Successfully Connected!</h2>
                <p style="color: #6B7280;">Your Supabase project is now connected.</p>
                <p style="color: #6B7280; font-size: 14px;">This window will close automatically...</p>
            </div>
            <script>
                window.opener.postMessage({{
                    type: 'SUPABASE_OAUTH_SUCCESS',
                    projectId: '{project_id}',
                    connectedAt: '{connected_at}',
                    status: 'connected'
                }}, window.location.origin);
                setTimeout(() => window.close(), 3000);
            </script>
        </body>
        </html>
        """)
        
    except Exception as e:
        print(f"[ERROR] Callback processing failed: {str(e)}")
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Supabase Connection Error</title>
        </head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>Connection Failed</h2>
                <p>Callback processing failed: {str(e)}</p>
                <p>This window will close automatically...</p>
            </div>
            <script>
                window.opener.postMessage({{
                    type: 'SUPABASE_OAUTH_ERROR',
                    error: 'Callback processing failed: {str(e)}'
                }}, window.location.origin);
                setTimeout(() => window.close(), 2000);
            </script>
        </body>
        </html>
        """)
        

# Add to your existing router file
import secrets
import string
from pydantic import BaseModel, Field
from app.services.supabase_management_service import SupabaseManagementService

# Initialize the management service
supabase_mgmt_service = SupabaseManagementService()

class CreateProjectRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="Project name")
    organization_id: str = Field(..., description="Organization ID")
    region: str = Field(default="us-east-1", description="AWS region")
    db_password: Optional[str] = Field(None, min_length=8, description="Database password")


@router.post("/supabase/projects")
async def create_supabase_project(
    project_id:str,
    request: CreateProjectRequest,
    current_user = Depends(get_current_user)
):
    """
    Create a new Supabase project
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Generate secure password if not provided
        if not request.db_password:
            request.db_password = ''.join(
                secrets.choice(string.ascii_letters + string.digits + "!@#$%^&*") 
                for _ in range(16)
            )
        
        print(f"[DEBUG] Creating Supabase project: {request.name} for user: {user_id}")
        
        # Create project via Supabase Management API
        result = await supabase_mgmt_service.create_project(
            user_id=user_id,
            project_id = project_id,
            name=request.name,
            organization_id=request.organization_id,
            db_password=request.db_password,
            region=request.region
        )
        
        if not result.get("success"):
            print(f"[ERROR] Project creation failed: {result.get('error')}")
            raise HTTPException(
                status_code=400, 
                detail=f"Failed to create project: {result.get('error')}"
            )
        
        project_data = result["data"]
        supabase_project_id = project_data.get("id")
        
        
        print(f"[SUCCESS] Project created successfully: {supabase_project_id}")
        
        return {
            "success": True,
            "message": "Project created successfully",
            "data": {
                "project_id": supabase_project_id,
                "name": request.name,
                "organization_id": request.organization_id,
                "region": request.region,
                "status": project_data.get("status"),
                "database_url": project_data.get("database", {}).get("host") if project_data.get("database") else f"db.{supabase_project_id}.supabase.co",
                "api_url": f"https://{supabase_project_id}.supabase.co",
                "dashboard_url": f"https://supabase.com/dashboard/project/{supabase_project_id}",  # Added dashboard URL
                "created_at": project_data.get("created_at"),
                "is_connected": False,
                "connected_at": None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error creating project: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")
#jcizylmvysvoomywxdgj

@router.get("/supabase/projects")
async def list_supabase_projects(
    project_id: str,
    organization_id: Optional[str] = None,
    include_keys: bool = True,
    current_user = Depends(get_current_user)
):
    """
    List all Supabase projects with optional API keys
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Listing Supabase projects for user: {user_id}")
        
        # Get projects from Supabase Management API
        result = await supabase_mgmt_service.list_projects(user_id=user_id,project_id=project_id,organization_id=organization_id)
        
        if not result.get("success"):
            print(f"[ERROR] Failed to fetch projects: {result.get('error')}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to fetch projects: {result.get('error')}"
            )
        
        projects = result["data"]
        enhanced_projects = []
        
        for project in projects:
            project_id_supabase = project.get("id")
            
            # Check if project is connected (has OAuth tokens)
            try:
                tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
                is_connected = bool(tokens)
                connected_at = tokens.get("connected_at") if tokens else None
            except:
                is_connected = False
                connected_at = None
            
            # Get API keys - try multiple approaches
            anon_key = None
            service_role_key = None
            
            if include_keys:
                try:
                    # Method 1: Try dedicated API keys endpoint
                    keys_result = await supabase_mgmt_service.get_project_api_keys(user_id,project_id,project_id_supabase)
                    if keys_result.get("success"):
                        keys_data = keys_result["data"]
                        if isinstance(keys_data, list):
                            for key in keys_data:
                                if key.get("name") == "anon":
                                    anon_key = key.get("api_key")
                                elif key.get("name") == "service_role":
                                    service_role_key = key.get("api_key")
                        else:
                            # Sometimes returned as dict
                            anon_key = keys_data.get("anon")
                            service_role_key = keys_data.get("service_role")
                    
                    # Method 2: If that fails, try project details endpoint
                    if not anon_key and not service_role_key:
                        details_result = await supabase_mgmt_service.get_project_details(user_id,project_id,project_id_supabase)
                        if details_result.get("success"):
                            project_details = details_result["data"]
                            anon_key = project_details.get("anon_key")
                            service_role_key = project_details.get("service_role_key")
                            
                            # Sometimes keys are nested
                            if not anon_key:
                                api_keys = project_details.get("api_keys", {})
                                anon_key = api_keys.get("anon")
                                service_role_key = api_keys.get("service_role")
                    
                except Exception as e:
                    print(f"[WARNING] Failed to fetch API keys for project {project_id_supabase}: {e}")
                    # If all else fails, extract from the original project data
                    anon_key = project.get("anon_key")
                    service_role_key = project.get("service_role_key")
            
            enhanced_project = {
                "id": project_id_supabase,
                "name": project.get("name"),
                "organization_id": project.get("organization_id"),
                "region": project.get("region"),
                "status": project.get("status"),
                "created_at": project.get("created_at"),
                "database_url": project.get("database", {}).get("host") if project.get("database") else f"db.{project_id}.supabase.co",
                "api_url": f"https://{project_id_supabase}.supabase.co",
                "anon_key": anon_key,
                "service_role_key": service_role_key,
                "is_connected": is_connected,
                "connected_at": connected_at,
                "dashboard_url": f"https://supabase.com/dashboard/project/{project_id_supabase}"
            }
            enhanced_projects.append(enhanced_project)
        
        print(f"[SUCCESS] Retrieved {len(enhanced_projects)} projects")
        
        return {
            "success": True,
            "message": f"Retrieved {len(enhanced_projects)} projects",
            "data": enhanced_projects,
            "count": len(enhanced_projects),
            "organization_id": organization_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error listing projects: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")
    
#esvsjgjjxjeomqheqcxe
@router.get("/supabase/projects/{project_id_supabase}")
async def get_supabase_project_details__(
    project_id: str,
    project_id_supabase: str,
    current_user = Depends(get_current_user)
):
    """
    Get detailed information for a specific Supabase project
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Getting project details for: {project_id_supabase}")
        
        # Get project details
        details_result = await supabase_mgmt_service.get_project_details(user_id,project_id,project_id_supabase)
        if not details_result.get("success"):
            raise HTTPException(
                status_code=404,
                detail=f"Project not found: {details_result.get('error')}"
            )
        
        project_data = details_result["data"]
        
        # Get API keys
        keys_result = await supabase_mgmt_service.get_project_api_keys(user_id,project_id,project_id_supabase)
        anon_key = None
        service_role_key = None
        
        if keys_result.get("success"):
            keys_data = keys_result["data"]
            for key in keys_data:
                if key.get("name") == "anon":
                    anon_key = key.get("api_key")
                elif key.get("name") == "service_role":
                    service_role_key = key.get("api_key")
        
        # Check connection status
        try:
            tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
            is_connected = bool(tokens)
            connected_at = tokens.get("connected_at") if tokens else None
        except:
            is_connected = False
            connected_at = None
        
        return {
            "success": True,
            "message": "Project details retrieved successfully",
            "data": {
                "id": project_id_supabase,
                "name": project_data.get("name"),
                "organization_id": project_data.get("organization_id"),
                "region": project_data.get("region"),
                "status": project_data.get("status"),
                "created_at": project_data.get("created_at"),
                "database_url": project_data.get("database", {}).get("host"),
                "api_url": f"https://{project_id_supabase}.supabase.co",
                "dashboard_url": f"https://supabase.com/dashboard/project/{project_id_supabase}",
                "anon_key": anon_key,
                "service_role_key": service_role_key,
                "is_connected": is_connected,
                "connected_at": connected_at,
                "database": project_data.get("database", {}),
                "settings": project_data.get("settings", {})
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Error getting project details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project details: {str(e)}")
    
@router.get("/supabase/organizations")
async def list_supabase_organizations(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """
    List user's Supabase organizations
    """
    try:
        user_id=current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        result = await supabase_mgmt_service.get_organizations(user_id,project_id)
        
        if not result.get("success"):
            raise HTTPException(
                status_code=400,
                detail=f"Failed to fetch organizations: {result.get('error')}"
            )
        
        return {
            "success": True,
            "message": "Organizations retrieved successfully",
            "data": result["data"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Error fetching organizations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch organizations: {str(e)}")

from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
class UpdateSupabaseDBRequest(BaseModel):
    project_id: str = Field(..., min_length=1, description="The main project ID")
    project_id_supabase: str = Field(..., min_length=1, description="The Supabase project ID")
    db_password: str = Field(..., min_length=8, description="Database password for Supabase project")

import urllib.parse
@router.post("/supabase/update_db")
async def update_db(
    request: UpdateSupabaseDBRequest,
    current_user = Depends(get_current_user)
):
    try:
        project_id = request.project_id
        db_password = request.db_password
        project_id_supabase = request.project_id_supabase
        # Extract and validate user ID
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Get project details
        project_response = await supabase_mgmt_service.get_supabase_project_details(
            project_id, project_id_supabase, user_id
        )
        
        # Validate response
        if not project_response.get("success"):
            raise HTTPException(
                status_code=400, 
                detail=project_response.get("message", "Failed to get project details")
            )
        
        project_details = project_response["data"]
        
        # Validate required fields
        required_fields = ['api_url', 'service_role_key', 'anon_key']
        if not all(project_details.get(field) for field in required_fields):
            raise HTTPException(
                status_code=400, 
                detail="Missing required project credentials"
            )
        encoded_password = urllib.parse.quote(db_password, safe='')
        # Prepare update data
        update_fields = {
            "supabase_project_id": project_id_supabase,
            "api_url": project_details['api_url'],
            # "db_url": encrypt_data(f"postgresql://postgres:{encoded_password}@db.{project_id_supabase}.supabase.co:5432/postgres"),
            "db_url": encrypt_data(f"postgresql://postgres.{project_id_supabase}:{encoded_password}@aws-0-ap-south-1.pooler.supabase.com:5432/postgres"),
            "service_role_key": encrypt_data(project_details['service_role_key']),
            "anon_key": encrypt_data(project_details['anon_key']),
            "updated_at": datetime.utcnow()
        }
        
        # Database update
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]
        
        filter_query = {"user_id": user_id, "project_id": project_id}
        
        # Update existing fields and set created_at only for new documents
        result = collection.update_one(
            filter_query,
            {
                "$set": update_fields,
                "$setOnInsert": {
                    "user_id": user_id,
                    "project_id": project_id,
                    "created_at": datetime.utcnow()
                }
            },
            upsert=True
        )
        
        return {
            "success": True,
            "message": "Database updated successfully",
            "is_new_record": result.upserted_id is not None,
            "matched_count": result.matched_count,
            "modified_count": result.modified_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail="Failed to update database"
        )

@router.delete("/supabase/disconnect/{project_id}")
async def disconnect_supabase(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """
    Disconnect Supabase from the project 
    """
    try:
        user_id = current_user.get("cognito:username")
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        print(f"[DEBUG] Disconnecting Supabase for user_id: {user_id}, project_id: {project_id}")
        
        # Check if user has Supabase connected for this project
        existing_tokens = await supabase_db_service.get_user_supabase_tokens(user_id, project_id)
        
        if not existing_tokens:
            print(f"[DEBUG] No Supabase connection found for this project")
            return {
                "success": True,
                "message": "No Supabase connection found for this project",
                "status": "not_connected",
                "project_id": project_id
            }
        
      
        delete__result = await supabase_db_service.disconnect_user_supabase(user_id, project_id)
        
        # If disconnect_user_supabase returns success: True, return response directly
        if delete__result.get("success"):
            print(f"[SUCCESS] OAuth tokens deleted successfully")
            
            disconnected_at = datetime.utcnow().isoformat()
            
            return {
                "success": True,
                "message": "Supabase disconnected successfully",
                "status": "disconnected",
                "project_id": project_id,
                "disconnected_at": disconnected_at,
                "data": {
                    "user_id": user_id,
                    "project_id": project_id,
                    "was_connected": True,
                    "disconnected_at": disconnected_at
                }
            }
        else:
            # If disconnect_user_supabase failed
            error_msg = delete__result.get("error", "Failed to delete OAuth tokens")
            print(f"[ERROR] Error deleting OAuth tokens: {error_msg}")
            raise HTTPException(status_code=500, detail=f"Failed to delete OAuth tokens: {error_msg}")
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in disconnect_supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to disconnect Supabase: {str(e)}")