{% extends "base_discussion.prompt" %}

You are an expert software architect configuring a monolithic system.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model focusing on the overall system, its users, and external systems:

{% else %}
You are an expert system architect reviewing the Monolithic System Context for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:
1. Impact Analysis:
   - Compare original and new project context
   - Compare original and new functional/non-functional requirements
   - Evaluate if changes to users or external systems are required
   
2. Required Changes:
   - Modify existing users and external systems as needed based on the above analysis
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %}

GUIDELINES:

1. Review Project Details and Requirements:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements Context:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. System Overview:
   - Provide detailed description of the system's purpose and main functionalities
   - Define system boundaries and scope
   - Remember this is a MONOLITHIC system, meaning all system functionality will be encapsulated in a single container

3. Users and External Systems:
   - Identify and describe all users/actors of the system
   - Identify external systems that interact with this system
   - Identify Databases requirement for this system and consider as external system
   - Note: All system functionality will be encapsulated in a single container

4. For each external system:
     * Create a container with ContainerType "external"
     * For database systems:
       - Set ContainerCategory to "Database"
     * Provide name and description
     * Define its role and purpose

5. Generate C4 System Context Diagram:
   - Use Mermaid syntax

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   - Show system boundaries
   - Include all users and external systems
   - Show high-level relationships

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The identified users from the requirements
   - The external systems from the system context
   - The actual relationships and interfaces discovered
   - The specific system name and purpose from the project context
   

   Monolithic System Context Diagram example - should be used for reference - create the mermaid based on the details of the system context:
```
graph TB
    %% Users/Actors - Include only relevant user types
    subgraph Users[System Users]
        User1["User Type 1<br/>[Person]<br/><i>Description of this user's role</i>"]
        User2["User Type 2<br/>[Person]<br/><i>Description of this user's role</i>"]
    end

   %% Main System
   subgraph MainSystem[Your Monolithic System Name]
       System["System Name<br/>[System]<br/><i>Core system description<br/>focused on actual functionality</i>"]
   end

   %% External Systems - Only those specifically required
   subgraph ExternalSystems[External Systems]
       Ext1["External System 1<br/>[System_Ext]<br/><i>Required external<br/>system description</i>"]
       Ext2["External System 2<br/>[System_Ext]<br/><i>Required external<br/>system description</i>"]
   end

   %% Relationships - Only show actual interactions
   User1 -->|"Specific interaction<br/>Actual protocol"| System
   User2 -->|"Specific interaction<br/>Actual protocol"| System
   System -->|"Required interaction<br/>Actual protocol"| Ext1
   System -->|"Required interaction<br/>Actual protocol"| Ext2

   %% Styling
   classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class System system
   class User1,User2 person
   class Ext1,Ext2 external
   class Users,ExternalSystems,MainSystem boundary
```

Note: When generating the diagram:
1. Replace generic names with actual system/user/external system names
2. Update descriptions to match actual roles and purposes
3. Use appropriate protocols in relationships
4. Include all identified external systems
5. Show actual system boundaries based on the context

Change Needed: 
    - Set to False if changes are not required.

Change Log:
    - Capture history of changes.

{% endblock %}

{% block autoconfig %}
Create a comprehensive C4 Model System Context for a monolithic system focusing on the system's purpose, users, and external systems.
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines to make the required changes to the monolithic system context. 
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Monolithic system context configuration, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %} 