import logging
import yaml
from datadog_api_client import ApiClient, Configuration
from datadog_api_client.v2.api.logs_api import LogsApi
from app.core.Settings import settings 
import os
from app.utils.project_utils import get_stage
import contextvars
import sys
import json
import threading
import queue
import getpass
import atexit
import uuid

# Context variables for discussion_id and task_id
discussion_id_var = contextvars.ContextVar('discussion_id', default='')
task_id_var = contextvars.ContextVar('task_id', default='')
document_id_var = contextvars.ContextVar('document_id', default='')


# Check if we should use threaded handlers
USE_THREADED_HANDLERS = not bool(os.getenv("TURN_OFF_LOGS_THREAD"))
#When FLAG is set, uses non-threaded handlers
#When FLAG is not set, uses threaded handlers

class DatadogFormatter(logging.Formatter):
    """
    Custom formatter that preserves extra fields in the log output
    """
    def format(self, record):
        # Format the basic message using the parent class
        msg = super().format(record)
        
        # Add any extra attributes as a JSON structure
        if hasattr(record, 'extra'):
            extra_data = getattr(record, 'extra')
            if extra_data:
                # Add the extra data as JSON
                msg = f"{msg} - {json.dumps(extra_data)}"
        
        return msg


class DatadogHandler(logging.Handler):
    """
    Simple synchronous Datadog handler (non-threaded)
    """
    def __init__(self):
        super().__init__()
        
        # Set up formatter
        self.setFormatter(DatadogFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        
        # Initialize the Datadog API client
        configuration = Configuration()
        configuration.api_key['apiKeyAuth'] = settings.DATADOG_API_KEY
        configuration.api_key['appKeyAuth'] = settings.DATADOG_APP_KEY
        configuration.server_variables["site"] = settings.DATADOG_SERVER
        self.api_instance = LogsApi(ApiClient(configuration))

    def emit(self, record):
        """
        Send log directly to Datadog (synchronous)
        """
        try:
            # Format the record
            log_entry = self.format(record)
            
            # Get context variables
            discussion_id = discussion_id_var.get()
            task_id = task_id_var.get() or os.environ.get("task_id")
            document_id = document_id_var.get()

             # Generate document_id if not already setAdd commentMore actions
            if not document_id:
                document_id = str(uuid.uuid4())
                document_id_var.set(document_id)
            
            # Map Python log levels to Datadog status
            level_mapping = {
                logging.DEBUG: "debug",
                logging.INFO: "info",
                logging.WARNING: "warn",
                logging.ERROR: "error",
                logging.CRITICAL: "error"
            }
            
            status = level_mapping.get(record.levelno, "info")
            tags = [f"env:{get_stage(settings)}", f"service:{record.name}", f"log_level:{status}"]
            
            # Add discussion and task IDs if available
            if discussion_id:
                tags.append(f"discussion_id:{discussion_id}")
                
            if task_id:
                tags.append(f"task_id:{task_id}")

            if document_id:
                tags.append(f"document_id:{document_id}")

            # Create the log entry for Datadog
            datadog_log = {
                "ddsource": "kavia_ai",
                "ddtags": ",".join(tags),
                "hostname": os.environ.get('HOSTNAME', 'unknown'),
                "message": log_entry,
                "service": "kavia_ai",
                "status": status,
                "level": status
            }
            
            # Send to Datadog synchronously
            self.api_instance.submit_log(body=[datadog_log])
            
        except Exception as e:
            # Fallback to console logging if anything fails
            sys.stderr.write(f"Failed to send log to Datadog: {str(e)}\n")
            sys.stderr.write(f"Original log message: {record.getMessage()}\n")
            sys.stderr.flush()


class ThreadedDatadogHandler(logging.Handler):
    """
    A handler that sends logs to Datadog in a separate thread
    """
    def __init__(self):
        super().__init__()
        
        # Important: Initialize the stopping flag BEFORE starting the thread
        self.stopping = False
        
        # Set up a formatter that preserves extra fields
        self.setFormatter(DatadogFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    
        # Initialize the Datadog API client
        configuration = Configuration()
        configuration.api_key['apiKeyAuth'] = settings.DATADOG_API_KEY
        configuration.api_key['appKeyAuth'] = settings.DATADOG_APP_KEY
        configuration.server_variables["site"] = settings.DATADOG_SERVER
        self.api_instance = LogsApi(ApiClient(configuration))
        
        # Create a queue for log messages
        self.log_queue = queue.Queue()
        
        # Create and start the worker thread
        self.worker_thread = threading.Thread(target=self._process_logs, name="DatadogLogWorker")
        self.worker_thread.daemon = True
        self.worker_thread.start()

    def _process_logs(self):
        """
        Worker thread that processes logs from the queue and sends them to Datadog
        """
        while not self.stopping:
            try:
                # Get a log entry from the queue with a timeout
                try:
                    log_entry = self.log_queue.get(block=True, timeout=0.5)
                except queue.Empty:
                    continue
                
                # Send the log to Datadog
                try:
                    self.api_instance.submit_log(body=[log_entry])
                except Exception as e:
                    # Log the error to stderr
                    sys.stderr.write(f"Failed to send log to Datadog: {str(e)}\n")
                    sys.stderr.write(f"Original log message: {log_entry['message']}\n")
                    sys.stderr.write(f"Tags: {log_entry['ddtags']}\n")
                    sys.stderr.flush()
                
                # Mark the task as done
                self.log_queue.task_done()
                
            except Exception as e:
                # Log any other errors
                sys.stderr.write(f"Error in Datadog log worker thread: {str(e)}\n")
                sys.stderr.flush()

    def emit(self, record):
        """
        Put the log record into the queue for the worker thread to process
        """
        try:
            # Format the record
            log_entry = self.format(record)
            
            # Get context variables
            discussion_id = discussion_id_var.get()
            task_id = task_id_var.get() or os.environ.get("task_id")
            
            # Map Python log levels to Datadog status
            level_mapping = {
                logging.DEBUG: "debug",
                logging.INFO: "info",
                logging.WARNING: "warn",
                logging.ERROR: "error",
                logging.CRITICAL: "error"
            }
            
            status = level_mapping.get(record.levelno, "info")
            tags = [f"env:{get_stage(settings)}", f"service:{record.name}", f"log_level:{status}"]
            
            # Add discussion and task IDs if available
            if discussion_id:
                tags.append(f"discussion_id:{discussion_id}")
                
            if task_id:
                tags.append(f"task_id:{task_id}")
            
            # Create the log entry for Datadog
            datadog_log = {
                "ddsource": "kavia_ai",
                "ddtags": ",".join(tags),
                "hostname": os.environ.get('HOSTNAME', 'unknown'),
                "message": log_entry,
                "service": "kavia_ai",
                "status": status,
                "level": status
            }
            
            # Add the log to the queue
            self.log_queue.put(datadog_log)
            
        except Exception as e:
            # Fallback to console logging if anything fails
            sys.stderr.write(f"Failed to queue log for Datadog: {str(e)}\n")
            sys.stderr.write(f"Original log message: {record.getMessage()}\n")
            sys.stderr.flush()

    def close(self):
        """
        Close the handler, ensuring all queued logs are processed
        """
        # Signal the worker thread to stop
        self.stopping = True
        
        # Wait for the queue to be empty
        if hasattr(self, 'log_queue'):
            try:
                self.log_queue.join(timeout=5.0)  # Wait up to 5 seconds
            except:
                pass
        
        super().close()


class DatadogFileHandler(logging.FileHandler):
    """
    Simple file handler with Datadog integration (non-threaded)
    """
    def __init__(self, filename, mode='a', encoding=None, delay=False):
        super().__init__(filename, mode, encoding, delay)
        self.datadog_handler = DatadogHandler()
        
        # Set up formatter
        formatter = DatadogFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.setFormatter(formatter)
        self.datadog_handler.setFormatter(formatter)

    def emit(self, record):
        """
        Send log to both file and Datadog synchronously
        """
        # Write to file first
        super().emit(record)
        # Then send to Datadog
        self.datadog_handler.emit(record)

    def close(self):
        """
        Close both handlers
        """
        self.datadog_handler.close()
        super().close()


class ThreadedFileHandler(logging.FileHandler):
    """
    A file handler that writes logs in a separate thread
    """
    def __init__(self, filename, mode='a', encoding=None, delay=False):
        # Initialize the parent class
        super().__init__(filename, mode, encoding, delay)
        
        # Important: Initialize the stopping flag BEFORE starting the thread
        self.stopping = False
        
        # Create a queue for log messages
        self.log_queue = queue.Queue()
        
        # Create and start the worker thread
        self.worker_thread = threading.Thread(
            target=self._process_logs, 
            name=f"FileLogWorker-{os.path.basename(filename)}"
        )
        self.worker_thread.daemon = True
        self.worker_thread.start()

    def _process_logs(self):
        """
        Worker thread that processes logs from the queue and writes them to the file
        """
        while not self.stopping:
            try:
                # Get a log message from the queue with a timeout
                try:
                    msg = self.log_queue.get(block=True, timeout=0.5)
                except queue.Empty:
                    continue
                
                # Write the log to the file
                try:
                    # Call the parent class's emit method directly to write to the file
                    self.stream.write(msg + self.terminator)
                    self.flush()
                except Exception as e:
                    # Log the error to stderr
                    sys.stderr.write(f"Failed to write log to file: {str(e)}\n")
                    sys.stderr.write(f"Original log message: {msg}\n")
                    sys.stderr.flush()
                
                # Mark the task as done
                self.log_queue.task_done()
                
            except Exception as e:
                # Log any other errors
                sys.stderr.write(f"Error in file log worker thread: {str(e)}\n")
                sys.stderr.flush()

    def emit(self, record):
        """
        Put the log record into the queue for the worker thread to process
        """
        if self.stream is None:
            self.stream = self._open()
            
        try:
            # Format the record
            msg = self.format(record)
            
            # Add the log to the queue
            self.log_queue.put(msg)
            
        except Exception as e:
            # Call handleError in case of formatting errors
            self.handleError(record)

    def close(self):
        """
        Close the handler, ensuring all queued logs are processed
        """
        # Signal the worker thread to stop
        self.stopping = True
        
        # Wait for the queue to be empty
        if hasattr(self, 'log_queue'):
            try:
                self.log_queue.join(timeout=5.0)  # Wait up to 5 seconds
            except:
                pass
        
        # Close the file
        if self.stream:
            self.flush()
            self.stream.close()
            self.stream = None


class ThreadedDatadogFileHandler(logging.Handler):
    """
    A handler that sends logs to both a file and Datadog in separate threads
    """
    def __init__(self, filename, mode='a', encoding=None, delay=False):
        super().__init__()
        
        # Create the file handler
        self.file_handler = ThreadedFileHandler(filename, mode, encoding, delay)
        
        # Create the Datadog handler
        self.datadog_handler = ThreadedDatadogHandler()
        
        formatter = DatadogFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.file_handler.setFormatter(formatter)
        self.datadog_handler.setFormatter(formatter)

    def emit(self, record):
        """
        Send the log to both handlers
        """
        # Send to both handlers
        self.file_handler.emit(record)
        self.datadog_handler.emit(record)

    def close(self):
        """
        Close both handlers
        """
        self.file_handler.close()
        self.datadog_handler.close()
        super().close()


def monkey_patch_file_handler():
    """
    Replace the standard FileHandler with our conditional version
    """
    original_file_handler = logging.FileHandler
    
    def patched_file_handler(*args, **kwargs):
        if USE_THREADED_HANDLERS:
            return ThreadedDatadogFileHandler(*args, **kwargs)
        else:
            return DatadogFileHandler(*args, **kwargs)
    
    logging.FileHandler = patched_file_handler


def setup_logging():
    """Set up the logging system"""
    if os.environ.get("BATCH_JOB_TRIGGER"):
        return
        
    try:
        # Debug information
        if USE_THREADED_HANDLERS:
            print("Using THREADED logging handlers")
        else:
            print("Using NON-THREADED logging handlers (FLAG is set)")
        
        # Get username for debugging
        username = getpass.getuser()
        print("Current Username from FAST-API : ", username)
        
        env_user = os.environ.get('USER') or os.environ.get('USERNAME')
        print("Username from environment variables:", env_user)
        
        # Method 3: Using pwd module (Unix/Linux only)
        try:
            import pwd
            pw_record = pwd.getpwuid(os.getuid())
            unix_user = pw_record.pw_name
            print("Username from pwd module:", unix_user)
        except (ImportError, AttributeError):
            print("pwd module not available on this system")
        
        try:
            import subprocess
            whoami_output = subprocess.check_output("whoami", shell=True).decode().strip()
            print("Username from whoami command:", whoami_output)
        except subprocess.SubprocessError:
            print("Could not run whoami command")
            
        # Find config file
        current_file_path = os.path.abspath(__file__)
        project_root = os.path.dirname(os.path.dirname(current_file_path))
        config_path = os.path.join(project_root, 'logging_config.yaml')

        print(config_path)
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f.read())

        # Apply our monkey patch to replace FileHandler
        monkey_patch_file_handler()
        
        # Ensure log directory exists
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up root logger
        logging.root.setLevel(logging.INFO)

        # Create formatter with the exact format in your example
        datadog_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # Configure loggers
        for logger_name, logger_config in config['loggers'].items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(logger_config['level'])
            logger.propagate = logger_config.get('propagate', False)

            # Create FileHandler for this logger (will be patched automatically)
            log_file = os.path.join(log_dir, f"{logger_name.split('.')[-1]}.log")
            
            # Use the patched FileHandler (automatically chooses threaded or non-threaded)
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            logger.addHandler(file_handler)
            
            # Add console handler with the same formatter
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(datadog_formatter)
            console_handler.setLevel(logging.INFO)
            logger.addHandler(console_handler)

        # Add handlers for key modules that need monitoring
        for module_name in ['app.discussions', 'app.utils.kg_build', 'app.utils.kg_inspect', 'app.telemetry']:
            if module_name not in config['loggers']:
                logger = logging.getLogger(module_name)
                logger.setLevel(logging.INFO)
                logger.propagate = False
                
                log_file = os.path.join(log_dir, f"{module_name.split('.')[-1]}.log")
                
                # Use the patched FileHandler (automatically chooses threaded or non-threaded)
                file_handler = logging.FileHandler(log_file)
                file_handler.setLevel(logging.INFO)
                logger.addHandler(file_handler)
                
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(datadog_formatter)
                console_handler.setLevel(logging.INFO)
                logger.addHandler(console_handler)

        if USE_THREADED_HANDLERS:
            print("Threaded logging setup completed - logs will be sent to Datadog in separate threads")
        else:
            print("Non-threaded logging setup completed - logs will be sent to Datadog synchronously")
        
        # Initialize and log document ID for this sessionAdd commentMore actions
        doc_id = get_document_id()
        setup_logger = logging.getLogger(__name__)
        setup_logger.info(f"=== LOGGING SESSION STARTED === Document ID: {doc_id} ===")
        setup_logger.info(f"Search Datadog logs with: document_id:{doc_id}")

    except Exception as e:
        print(f"Error setting up custom logging: {str(e)}")
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


def get_logger(name):
    """Get a logger by name"""
    return logging.getLogger(name)


def set_discussion_id(id):
    """Set the discussion ID in the context variable"""
    discussion_id_var.set(id)


def set_task_id(id):
    """Set the task ID in the context variable"""
    task_id_var.set(id)


def _shutdown_logging():
    """Ensure all logs are flushed before the program exits"""
    logging.shutdown()

def set_document_id(id=None):
    """Set document ID. If no ID is provided, generate a new UUID."""
    if id is None:
        id = str(uuid.uuid4())
    document_id_var.set(id)
    
    # Log the document ID so it's visible in logs
    logger = logging.getLogger(__name__)
    logger.info(f"Document ID set: {id}")
    
    return id

def get_document_id():
    """Get current document ID, generate one if not set."""
    document_id = document_id_var.get()
    if not document_id:
        document_id = set_document_id()
    return document_id

def log_document_id():
    """Explicitly log the current document ID for visibility."""
    document_id = get_document_id()
    logger = logging.getLogger(__name__)
    logger.info(f"Current Document ID: {document_id} - Use this ID to search logs in Datadog")
    return document_id

atexit.register(_shutdown_logging)