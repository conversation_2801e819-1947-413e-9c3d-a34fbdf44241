import time
from kubernetes import client, config
from kubernetes.client.rest import ApiException
from typing import Optional, Set
import logging

logger = logging.getLogger(__name__)
CONFIG_MAP = {
    "dev": {
        "service_account": "duploservices-kavia-dev-edit-user",
        "namespace": "duploservices-kavia-dev",
        "ingress_name": "ingressservice1",
        "codegen_service_deployment": "codegenservicedeployment4",

    },
    "qa": {
        "service_account": "duploservices-kavia-qa-edit-user",
        "namespace": "duploservices-kavia-qa",
        "ingress_name": "ingressserviceqa",
        "codegen_service_deployment": "codegenservicedeploymentqa"
    },
    "prod": {
        "service_account": "duploservices-kavia-prod-edit-user",
        "namespace": "duploservices-kavia-prod",
        "ingress_name": "ingressserviceprod"
    },
    "template": {
        "service_account": "duploservices-kavia-{{STAGE}}-edit-user",
        "namespace": "duploservices-kavia-{{STAGE}}",
        "ingress_name": "ingressservice{{STAGE}}"
    }
}

class K8JobManager:
    def __init__(self, namespace: str = "duploservices-kavia-dev"):
        """Initialize K8JobManager with specified namespace"""
        self.namespace = namespace
        try:
            # Try loading in-cluster config first
            try:
                config.load_incluster_config()
            except config.ConfigException:
                # Fall back to local kubeconfig
                config.load_kube_config()
            
            self.batch_v1 = client.BatchV1Api()
            self.networking_v1 = client.NetworkingV1Api()

        except Exception as e:
            logger.error(f"Failed to initialize Kubernetes client: {str(e)}")
            raise
    
    def get_ingress_hosts(self, ingress_name: str) -> Set[str]:
        """
        Get the set of hosts from an ingress resource.
        
        Args:
            ingress_name (str): Name of the ingress resource
            
        Returns:
            Set[str]: Set of host names
        """
        try:
            ingress = self.networking_v1.read_namespaced_ingress(
                name=ingress_name, 
                namespace=self.namespace
            )
            return {rule.host for rule in ingress.spec.rules if rule.host} if ingress.spec.rules else set()
        except ApiException as e:
            logger.warning(f"Failed to read ingress {ingress_name}: {str(e)}")
            return set()

    def wait_for_job_completion(self, job_name: str) -> bool:
        """
        Wait for a job to complete and delete it upon success.
        
        Args:
            job_name (str): Name of the job to monitor
            
        Returns:
            bool: True if job completed successfully, False otherwise
        """
        try:
            while True:
                job = self.batch_v1.read_namespaced_job(
                    name=job_name, 
                    namespace=self.namespace
                )
                if job.status.succeeded:
                    logger.info(f"Job {job_name} completed successfully, deleting...")
                    self.batch_v1.delete_namespaced_job(
                        name=job_name,
                        namespace=self.namespace,
                        body=client.V1DeleteOptions(
                            propagation_policy='Foreground',
                            grace_period_seconds=0
                        )
                    )
                    logger.info(f"Deleted job: {job_name}")
                    return True
                time.sleep(5)
        except ApiException as e:
            logger.error(f"Error monitoring job {job_name}: {str(e)}")
            return False
        
    def create_project_container_job(
        self,
        project_id: str,
        env_name: str,
        action: str
    ) -> Optional[str]:
        """
        Create a Kubernetes job to manage project containers.
        If a job with the same name already exists, just return the job name.
        
        Args:
            project_id (str): Project ID
            env_name (str): Environment name
            action (str): Action to perform (create/delete)
            
        Returns:
            Optional[str]: Job name if successful, None if failed
        """
        config_map = CONFIG_MAP[env_name]
        self.namespace = config_map['namespace']
        print(f"using this config_map: {config_map}")
        try:
            # Validate inputs
            if not project_id or not env_name or action not in ["create", "delete"]:
                raise ValueError("Invalid input parameters")

            job_name = f"codegen-{project_id}-{env_name}-create"
            
            if env_name == "dev":
                hostname = f"vscode-internal-{project_id}-{env_name}.cloud.kavia.ai"
            elif env_name == "qa":
                hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"

            # Check if the job with the same name already exists
            try:
                existing_job = self.batch_v1.read_namespaced_job(
                    name=job_name,
                    namespace=self.namespace
                )
                # If we get here, the job exists - return its name without creating new one
                if existing_job and existing_job.metadata and existing_job.metadata.name:
                    print("Skipping the job creation...")
                    logger.info(f"Job {job_name} already exists, returning job name without creating a new one")
                    return existing_job.metadata.name
            except ApiException as e:
                # If it's not a 404 (Not Found) error, something went wrong with the API call
                if e.status != 404:
                    logger.error(f"Error checking for existing job: {str(e)}")
                    return None
                # 404 means job doesn't exist - we'll create it below
                logger.info(f"Job {job_name} doesn't exist, proceeding with creation")
                
            # Job doesn't exist, continue with creation

            # Define kubectl command based on action
            if action == "create":
                servicename = f"internal-{project_id}-{env_name}"
                kubectl_command = f"""echo "Creating deployment for {job_name}..."
                # sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
                # sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
                # s/{{{{ENV_NAME}}}}/{env_name}/g; \
                # s/{{{{hostname}}}}/{hostname}/g; \
                # s/{{{{servicename}}}}/{servicename}/g" \
                # /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
                # sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
                #     s/{{{{ENV_NAME}}}}/{env_name}/g" \
                #     /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
                #sleep 99999
                # echo "Applying PVC..."
                # kubectl apply -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
                # echo "Applying deployment..."
                # kubectl apply -f /tmp/codegenservice-{project_id}-{env_name}.yaml
                # echo "Applying ingress..."
                # kubectl apply -f /tmp/ingress-patch-{project_id}-{env_name}.yaml
                
                #!/bin/sh

                echo " Starting ingress update script..."

                CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
                POD_NAME="nginx-c96774b8d-d98nb"

                echo " Namespace: $NAMESPACE"
                echo " Environment: $ENV_NAME"

                # Step 1: Get the oldest available pod and its app label
                echo " Finding oldest pod with label 'pod-status=available'..."
                POD_APP=`kubectl get pods -n "$NAMESPACE" -l pod-status=available \\
                --sort-by=.metadata.creationTimestamp \\
                -o jsonpath="{{.items[0].metadata.name}} {{.items[0].metadata.labels.app}}"`

                POD=`echo "$POD_APP" | awk '{{print $1}}'`
                APP=`echo "$POD_APP" | awk '{{print $2}}'`

                echo " Using pod: $POD"
                echo " App label: $APP"

                SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.dev-vscode.cloud.kavia.ai"
                PROXY_port1="http://internal-${{APP}}:3000"

                PROXY="http://internal-${{APP}}:8080"

                echo " SERVER_NAME to be added: $SERVER_NAME"
                echo " PROXY to be routed: $PROXY"

                sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; s|{{{{PROXY}}}}|${{PROXY}}|g; s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"
            
                echo " Created local config: $CONFIG_FILE"

    
                # Step 2: Copy file into pod
                DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
                kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
                echo " Copied config into pod: $POD_NAME"
                kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
                echo " Reloaded nginx in pod: $POD_NAME"
                echo " Labeling pod '$POD' as used..."
                kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
                """
                
            else:  # delete
                kubectl_command = f"""
                echo "Deleting deployment for {job_name}..."
                sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
                sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
                s/{{{{ENV_NAME}}}}/{env_name}/g; \
                s/{{{{hostname}}}}/{hostname}/g; \
                s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
                /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
                sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
                    s/{{{{ENV_NAME}}}}/{env_name}/g" \
                    /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
                #sleep 99999
                kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
                kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
                kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {self.namespace} --ignore-not-found=true
                echo "Deleting previous job: {job_name}..."
                kubectl delete job {job_name} -n {self.namespace} --ignore-not-found=true
                echo "Cleanup completed!"
                """

            # Define job manifest
            job_manifest = {
                "apiVersion": "batch/v1",
                "kind": "Job",
                "metadata": {
                    "name": job_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": self.namespace,
                    },
                },
                "spec": {
                    "parallelism": 1,
                    "completions": 1,
                    "backoffLimit": 6,
                    "template": {
                        "metadata": {
                            "labels": {
                                "app": job_name,
                                "owner": "duploservices",
                                "tenantname": self.namespace,
                            },
                        },
                        "spec": {
                            "serviceAccountName": config_map["service_account"],
                            "restartPolicy": "Never",
                            "containers": [
                                {
                                    "name": "duplocloudcodegen",
                                    "image": "bitnami/kubectl:latest",
                                    "command": ["/bin/sh", "-c"],
                                    "args": [kubectl_command],
                                    "env": [
                                        {"name": "ACTION", "value": action},
                                        {"name": "PROJECT_ID", "value": project_id},
                                        {"name": "ENV_NAME", "value": env_name},
                                    ],
                                    "volumeMounts": [
                                        {"name": config_map["codegen_service_deployment"], "mountPath": "/app"},
                                        {"name": config_map["ingress_name"], "mountPath": "/app/ingress"},
                                        {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                        {"name": "nginx", "mountPath": "/app/nginx"},
                                    ],
                                }
                            ],
                            "volumes": [
                                {
                                    "name": config_map["codegen_service_deployment"],
                                    "configMap": {"name": config_map["codegen_service_deployment"]},
                                },
                                {
                                    "name": config_map["ingress_name"],
                                    "configMap": {"name": config_map["ingress_name"]},
                                },
                                {
                                    "name": "codegenpvc",
                                    "configMap": {"name": "codegenpvc"},
                                },
                                {
                                    "name": "nginx",
                                    "configMap": {"name": "nginx"}
                                }
                            ],
                        },
                    },
                },
            }
                        
            if action == "delete":
                self.batch_v1.delete_namespaced_job(
                    name=job_name,
                    namespace=self.namespace,
                    body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
                )
                print(f"Deleted existing job: {job_name}")
            else:
                # Create new job
                response = self.batch_v1.create_namespaced_job(
                    namespace=self.namespace,
                    body=job_manifest
                )
                logger.info(f"Created job: {response.metadata.name}")
                return job_name

        except Exception as e:
            logger.error(f"Failed to create K8s job: {str(e)}")
            return None
    
    def delete_project_container(self, project_id: str, env_name: str) -> bool:
        """
        Delete a project's container and clean up associated resources.
        
        Args:
            project_id (str): Project ID
            env_name (str): Environment name
            
        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            # Validate inputs
            if not project_id or not env_name:
                raise ValueError("Project ID and environment name must be provided")

            previous_create_job = f"codegen-{project_id}-{env_name}-delete"
            job_name = f"codegen-{project_id}-{env_name}"
            ingress_name = "codegenservice"
            config_map = CONFIG_MAP[env_name]
            self.namespace = config_map['namespace']
            
            # Capture existing ingress hosts
            try:
                ingress = self.networking_v1.read_namespaced_ingress(
                    name=ingress_name, 
                    namespace=self.namespace
                )
                existing_hosts = {rule.host for rule in ingress.spec.rules if rule.host} if ingress.spec.rules else set()
            except ApiException as e:
                logger.warning(f"Failed to read existing ingress: {str(e)}")
                existing_hosts = set()

            # Define kubectl deletion command
            kubectl_command = f"""
                echo "Deleting deployment for {job_name}..."
                sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
                sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/ingress/ingress-service.json > /tmp/ingress-patch-{project_id}-{env_name}.json
                
                kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true
                kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {self.namespace} --ignore-not-found=true

                echo "Updating ingress to remove host for {project_id}-{env_name}..."
                kubectl patch ingress codegenservice -n {self.namespace} --type=json --patch-file /tmp/ingress-patch-{project_id}-{env_name}.json --dry-run=client -o json | \
                jq 'del(.spec.rules[] | select(.host == "vscode-internal-{project_id}-{env_name}.cloud.kavia.ai"))' | kubectl apply -f -
                
                echo "Deleting previous job: {previous_create_job}..."
                kubectl delete job {previous_create_job} -n {self.namespace} --ignore-not-found=true
                
                echo "Cleanup completed!"
            """

            # Create deletion job manifest
            job_manifest = {
                "apiVersion": "batch/v1",
                "kind": "Job",
                "metadata": {
                    "name": job_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": self.namespace,
                    },
                },
                "spec": {
                    "parallelism": 1,
                    "completions": 1,
                    "backoffLimit": 6,
                    "template": {
                        "metadata": {
                            "labels": {
                                "app": job_name,
                                "owner": "duploservices",
                                "tenantname": self.namespace,
                            },
                        },
                        "spec": {
                            "serviceAccountName": "duploservices-kavia-dev-edit-user",
                            "restartPolicy": "Never",
                            "containers": [
                                {
                                    "name": "duplocloudcodegen",
                                    "image": "bitnami/kubectl:latest",
                                    "command": ["/bin/sh", "-c"],
                                    "args": [kubectl_command],
                                    "env": [
                                        {"name": "ACTION", "value": "delete"},
                                        {"name": "PROJECT_ID", "value": project_id},
                                        {"name": "ENV_NAME", "value": env_name},
                                    ],
                                    "volumeMounts": [
                                        {"name": "codegenservicedeployment4", "mountPath": "/app"},
                                        {"name": "ingressservice", "mountPath": "/app/ingress"},
                                    ],
                                }
                            ],
                            "volumes": [
                                {
                                    "name": "codegenservicedeployment4",
                                    "configMap": {"name": "codegenservicedeployment4"},
                                },
                                {
                                    "name": "ingressservice",
                                    "configMap": {"name": "ingressservice"},
                                },
                            ],
                        },
                    },
                },
            }

            # Create and execute the deletion job
            response = self.batch_v1.create_namespaced_job(
                namespace=self.namespace,
                body=job_manifest
            )
            
            logger.info(f"Created deletion job: {response.metadata.name}")

            # Wait for job completion
            if self.wait_for_job_completion(job_name):
                # Get updated ingress hosts after deletion
                updated_hosts = self.get_ingress_hosts(ingress_name)
                removed_hosts = existing_hosts - updated_hosts
                
                logger.info("Removed ingress hosts:")
                for host in removed_hosts:
                    logger.info(f"  - {host}")
                
                logger.info("Remaining ingress hosts:")
                for host in updated_hosts:
                    logger.info(f"  - {host}")
                
                return True
            return False


        except Exception as e:
            logger.error(f"Failed to delete project container: {str(e)}")
            return False