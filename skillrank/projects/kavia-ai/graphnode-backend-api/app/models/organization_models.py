from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from app.connection.establish_db_connection import get_mongo_db
from bson import ObjectId
from pymongo.collection import Collection
import uuid
from enum import Enum
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME

DB_NAME = KAVIA_ROOT_DB_NAME

def generate_prefixed_uuid(prefix: str) -> str:
    # Generate a short hash using first 6 chars of uuid4 hex
    # This gives 16^6 = 16,777,216 possible values
    short_hash = hex(hash(str(uuid.uuid4())))[-8:]
    return f"{prefix}-{short_hash}" if len(prefix.strip()) > 0 else short_hash

def generate_user_id() -> str:
    return generate_prefixed_uuid("")

def generate_group_id() -> str:
    return generate_prefixed_uuid("grp")

def generate_org_id() -> str:
    try:
        print("Getting tenant_credentials collection...")
        collection = get_mongo_db(db_name=DB_NAME, collection_name="tenant_credentials").db.tenant_credentials
        
        print("Finding highest existing organization ID...")
        # Find the highest existing ID
        highest_org = collection.find_one(
            {"tenant_id": {"$regex": "^T[0-9]{4}$"}},
            sort=[("tenant_id", -1)]
        )
        print(f"Highest organization ID found: {highest_org}")
        
        if highest_org:
            print(f"Found highest existing ID: {highest_org['tenant_id']}")
            # Extract number from existing highest ID and increment
            current_num = int(highest_org["tenant_id"][1:])
            next_num = current_num + 1
        else:
            print("No existing IDs found, starting with 1")
            # Start with 1 if no existing IDs
            next_num = 1
            
        if next_num > 9999:
            print("Error: Organization ID limit reached")
            raise Exception("Organization ID limit reached")
            
        # Format new ID with leading zeros
        new_id = f"T{str(next_num).zfill(4)}"
        print(f"Generated new organization ID: {new_id}")
        return new_id
        
    except Exception as e:
        print(f"Error generating organization ID: {str(e)}")
        raise Exception(f"Failed to generate organization ID: {str(e)}")


def generate_permission_id() -> str:
    return generate_prefixed_uuid("prm")

def generate_plan_id() -> str:
    return generate_prefixed_uuid("pln")

class Configurations(BaseModel):
    max_users: int = Field(default=2, ge=1)
    role_customization: bool = False
    api_access: bool = False
    github_integration: bool = False
    jira_integration: bool = False
    custom_reports: bool = False
    export_capabilities: bool = False

    class Config:
        validate_assignment = True

class LLMModel(str, Enum):
    gpt_4o_mini = "gpt-4o-mini"
    gpt_4o = "gpt-4o"
    gpt_4_turbo = "gpt-4-turbo"
    gpt_3dot5 = "gpt-3.5"
    claude_3_5_sonnet = "claude-3.5-sonnet"

class ModuleSettings(BaseModel):
    name: str
    display_name: str
    selected_model: LLMModel
    available_models: List[LLMModel]
    
class Settings(BaseModel):
    showfunctioncalling: bool = Field(default=False)
    enable_log_download_pod_crud : bool = Field(default=False)
    discussion: ModuleSettings = Field(
        default_factory=lambda: ModuleSettings(
            name="discussion",
            display_name="Discussion",
            selected_model=LLMModel.claude_3_5_sonnet,
            available_models=[LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.gpt_3dot5]
        )
    )
    code_generation: ModuleSettings = Field(
        default_factory=lambda: ModuleSettings(
            name="code_generation",
            display_name="Code Generation",
            selected_model=LLMModel.claude_3_5_sonnet,
            available_models=[LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.claude_3_5_sonnet]
        )
    )
    code_inspection: ModuleSettings = Field(
        default_factory=lambda: ModuleSettings(
            name="code_inspection",
            display_name="Code Inspection",
            selected_model=LLMModel.claude_3_5_sonnet,
            available_models=[LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.claude_3_5_sonnet]
        )
    )
    conversational_chat: ModuleSettings = Field(
        default_factory=lambda: ModuleSettings(
            name="conversational_chat",
            display_name="Conversational Chat",
            selected_model=LLMModel.claude_3_5_sonnet,
            available_models=[LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.claude_3_5_sonnet]
        )
    )

    class Config:
        validate_assignment = True

class Permissions(BaseModel):
    id: str = Field(default_factory=generate_permission_id, alias="_id")
    permissions: Dict[str, Dict[str, bool]]
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        populate_by_name = True

    @staticmethod
    def get_collection() -> Collection:
        """Get MongoDB collection with consistent connection"""
        db_connection = get_mongo_db(db_name=DB_NAME, collection_name="tenant_permissions")
        return db_connection.db.permissions

    @classmethod
    async def create(cls, permission_data: Dict[str, Dict[str, bool]]) -> Optional[str]:
        try:
            permission_dict = {
                "_id": generate_permission_id(),  # Use the new prefixed ID
                "permissions": permission_data,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            collection = cls.get_collection()
            result = collection.insert_one(permission_dict)
            
            if not result.inserted_id:
                raise Exception("Failed to insert permission")
            
            created_doc = collection.find_one({"_id": result.inserted_id})
            if created_doc:
                created_doc['id'] = created_doc.pop('_id')
                print(f"Created document: {created_doc}")
            
            return result.inserted_id
        except Exception as e:
            raise Exception(f"Failed to create permission: {str(e)}")

    @classmethod
    async def get(cls, permission_id: str) -> Dict[str, Any]:
        try:
            collection = cls.get_collection()
            raw_data = collection.find_one({"_id": permission_id})
            if raw_data:
                permission_with_defaults = cls(**raw_data)
                return permission_with_defaults.model_dump()
            return {}
        except Exception as e:
            raise Exception(f"Failed to get permission: {str(e)}")

    @classmethod
    async def get_all(cls) -> List[Dict]:
        try:
            collection = cls.get_collection()
            permissions = list(collection.find())
            return [{**p, 'id': str(p.pop('_id'))} for p in permissions]
        except Exception as e:
            raise Exception(f"Failed to get all permissions: {str(e)}")

    @classmethod
    async def update(cls, permission_id: str, permission_data: Dict) -> bool:
        try:
            collection = cls.get_collection()
            update_data = {
                "$set": {
                    **permission_data,
                    "updated_at": datetime.utcnow()
                }
            }
            result = collection.update_one(
                {"_id": permission_id},
                update_data
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to update permission: {str(e)}")

    @classmethod
    async def delete(cls, permission_id: str) -> bool:
        try:
            collection = cls.get_collection()
            permission = collection.find_one({"_id": permission_id})
            if not permission:
                return False

            # Update any groups using this permission
            groups_collection = Group.get_collection()
            groups_collection.update_many(
                {"permission_id": permission_id},
                {"$set": {"permission_id": None}}
            )

            result = collection.delete_one({"_id": permission_id})
            return result.deleted_count > 0
        except Exception as e:
            raise Exception(f"Failed to delete permission: {str(e)}")

class User(BaseModel):
    id: str = Field(default_factory=generate_user_id, alias="_id")
    name: str
    email: str
    contact_number: str
    department: str
    organization_id: str
    is_admin: bool = False
    free_user: bool = True
    has_accepted_terms: bool = Field(default=False, description="Indicates if the user has accepted the terms and conditions")
    accepted_terms_at: Optional[datetime] = Field(default=None, description="Timestamp when the user accepted the terms and conditions")
    group_ids: List[str] = Field(default_factory=list)
    status: str = Field(default="active", pattern="^(active|inactive|deleted)$")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        populate_by_name = True

    @staticmethod
    def get_collection() -> Collection:
        """Get MongoDB collection with consistent connection"""
        db_connection = get_mongo_db(db_name=DB_NAME, collection_name="tenant_users")
        return db_connection.db.users

    @staticmethod
    def get_related_collections() -> Dict[str, Collection]:
        """Get related MongoDB collections"""
        return {
            'groups': Group.get_collection()
        }

    @classmethod
    async def create(cls, user_data: Dict) -> Optional[str]:
        try:
            user_obj = cls(**user_data)
            user_dict = user_obj.dict(exclude_none=True, by_alias=True)
            user_dict['created_at'] = datetime.utcnow()
            user_dict['updated_at'] = datetime.utcnow()
            
            collection = cls.get_collection()
            result = collection.insert_one(user_dict)
            
            if result.inserted_id:
                created_doc = collection.find_one({"_id": result.inserted_id})
                if created_doc:
                    created_doc['id'] = str(created_doc.pop('_id'))
                    print(f"Created user: {created_doc}")
                    
            return str(result.inserted_id) if result else None
        except Exception as e:
            raise Exception(f"Failed to create user: {str(e)}")

    @classmethod
    async def get(cls, user_id: str) -> Dict[str, Any]:
        try:
            collection = cls.get_collection()
            raw_data = collection.find_one({"_id": user_id})
            if raw_data:
                user_with_defaults = cls(**raw_data)
                return user_with_defaults.model_dump()
            return {}
        except Exception as e:
            raise Exception(f"Failed to get user: {str(e)}")

    @classmethod
    async def get_all(cls) -> List[Dict]:
        try:
            collection = cls.get_collection()
            users = list(collection.find())
            return [{**u, 'id': str(u.pop('_id'))} for u in users]
        except Exception as e:
            raise Exception(f"Failed to get all users: {str(e)}")

    @classmethod
    async def update(cls, user_id: str, user_data: Dict) -> bool:
        try:
            collection = cls.get_collection()
            update_data = {
                "$set": {
                    **user_data,
                    "updated_at": datetime.utcnow()
                }
            }
            result = collection.update_one(
                {"_id": user_id},
                update_data
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to update user: {str(e)}")

    @classmethod
    async def delete(cls, user_id: str) -> bool:
        try:
            collection = cls.get_collection()
            # Instead of deleting, update status to "deleted"
            result = collection.update_one(
                {"_id": user_id},
                {
                    "$set": {
                        "status": "deleted",
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to delete user: {str(e)}")

class Group(BaseModel):
    id: str = Field(default_factory=generate_group_id, alias="_id")
    name: str
    description: str = ""
    group_type: str
    permission_id: str 
    access_level: str = "Viewer"
    created_by: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    status: str = Field(default="active", pattern="^(active|inactive|deleted)$")
    user_ids: List[str] = Field(default_factory=list)

    class Config:
        populate_by_name = True

    @staticmethod
    def get_collection() -> Collection:
        """Get MongoDB collection with consistent connection"""
        db_connection = get_mongo_db(db_name=DB_NAME, collection_name="tenant_groups")
        return db_connection.db.groups

    @staticmethod
    def get_related_collections() -> Dict[str, Collection]:
        """Get related MongoDB collections"""
        return {
            'users': User.get_collection(),
            'organizations': Organization.get_collection()
        }

    @classmethod
    async def create(cls, group_data: Dict) -> Optional[str]:
        try:
            group_obj = cls(**group_data)
            group_dict = group_obj.dict(exclude_none=True, by_alias=True)
            group_dict['created_at'] = datetime.utcnow()
            group_dict['updated_at'] = datetime.utcnow()
            
            collection = cls.get_collection()
            result = collection.insert_one(group_dict)
            
            if result.inserted_id:
                created_doc = collection.find_one({"_id": result.inserted_id})
                if created_doc:
                    created_doc['id'] = str(created_doc.pop('_id'))
                    print(f"Created group: {created_doc}")
            
            return str(result.inserted_id) if result else None
        except Exception as e:
            raise Exception(f"Failed to create group: {str(e)}")

    @classmethod
    async def get(cls, group_id: str) -> Dict[str, Any]:
        try:
            collection = cls.get_collection()
            raw_data = collection.find_one({"_id": group_id})
            if raw_data:
                group_with_defaults = cls(**raw_data)
                return group_with_defaults.model_dump()
            return {}
        except Exception as e:
            raise Exception(f"Failed to get group: {str(e)}")

    @classmethod
    async def get_all(cls) -> List[Dict]:
        try:
            collection = cls.get_collection()
            groups = list(collection.find())
            return [{**g, 'id': str(g.pop('_id'))} for g in groups]
        except Exception as e:
            raise Exception(f"Failed to get all groups: {str(e)}")

    @classmethod
    async def get_batch(cls, group_ids: List[str]) -> List[Dict]:
        """Get multiple groups by their IDs"""
        try:
            collection = cls.get_collection()
            groups = list(collection.find({"_id": {"$in": group_ids}}))
            return [{**g, 'id': str(g.pop('_id'))} for g in groups]
        except Exception as e:
            raise Exception(f"Failed to get groups batch: {str(e)}")

    @classmethod
    async def update(cls, group_id: str, group_data: Dict) -> bool:
        try:
            collection = cls.get_collection()
            update_data = {
                "$set": {
                    **group_data,
                    "updated_at": datetime.utcnow()
                }
            }
            result = collection.update_one(
                {"_id": group_id},
                update_data
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to update group: {str(e)}")

    @classmethod
    async def delete(cls, group_id: str) -> bool:
        try:
            collection = cls.get_collection()
            # Instead of deleting, update status to "deleted"
            result = collection.update_one(
                {"_id": group_id},
                {
                    "$set": {
                        "status": "deleted",
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to delete group: {str(e)}")

    async def get_total_members(self) -> int:
        return len(self.user_ids)

    async def get_active_members(self) -> int:
        try:
            collection = User.get_collection()
            active_count = collection.count_documents({
                "_id": {"$in": [ObjectId(uid) for uid in self.user_ids]},
                "status": "active"
            })
            return active_count
        except Exception as e:
            raise Exception(f"Failed to get active members count: {str(e)}")
        
    @classmethod
    async def add_user(cls, group_id: str, user_id: str) -> bool:
        try:
            collection = cls.get_collection()
            # Check if user already exists in group
            group = collection.find_one({"_id": group_id})
            if group and user_id in group.get("user_ids", []):
                raise Exception("User is already a member of this group")
            result = collection.update_one({"_id": group_id}, {"$push": {"user_ids": user_id}})
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to add user to group: {str(e)}")

    @classmethod
    async def get_with_users(cls, group_id: str) -> Optional[Dict]:
        """Get group with complete user details"""
        try:
            collection = cls.get_collection()
            users_collection = User.get_collection()
            
            # Get the group first
            group = collection.find_one({"_id": group_id})
            if not group:
                return None
                
            # Transform _id to id
            group['id'] = str(group.pop('_id'))
            
            # Get all users data for the user_ids in the group
            if group.get('user_ids'):
                users = list(users_collection.find({"_id": {"$in": group['user_ids']}}))
                # Transform user data
                group['users'] = [{
                    **user,
                    'id': str(user.pop('_id'))
                } for user in users]
            else:
                group['users'] = []
                
            return group
        except Exception as e:
            raise Exception(f"Failed to get group with users: {str(e)}")

    @classmethod
    async def get_batch_with_users(cls, group_ids: List[str], latest: bool = True) -> List[Dict]:
        """Get multiple groups with their user details"""
        try:
            collection = cls.get_collection()
            users_collection = User.get_collection()
            
            # Add sort parameter based on latest flag
            sort_order = -1 if latest else 1
            groups = list(collection.find({"_id": {"$in": group_ids}}).sort("created_at", sort_order))
            
            # Get all unique user IDs from all groups
            all_user_ids = set()
            for group in groups:
                all_user_ids.update(group.get('user_ids', []))
            
            # Fetch all users data in one query
            users = list(users_collection.find({"_id": {"$in": list(all_user_ids)}}).sort("created_at", sort_order))
            users_dict = {str(user['_id']): {**user, 'id': str(user.pop('_id'))} 
                         for user in users}
            
            # Transform groups and add user details
            result = []
            for group in groups:
                group_data = {
                    **group,
                    'id': str(group.pop('_id')),
                    'users': [users_dict[user_id] 
                             for user_id in group.get('user_ids', [])
                             if user_id in users_dict]
                }
                result.append(group_data)
                
            return result
        except Exception as e:
            raise Exception(f"Failed to get groups batch with users: {str(e)}")

    @classmethod
    async def get_with_permissions(cls, group_id: str) -> Optional[Dict]:
        """Get group with its permission details"""
        try:
            collection = cls.get_collection()
            permissions_collection = Permissions.get_collection()
            
            # Get the group first
            group = collection.find_one({"_id": group_id})
            if not group:
                return None
                
            # Transform _id to id
            group['id'] = str(group.pop('_id'))
            
            # Get permission details if permission_id exists
            if group.get('permission_id'):
                permission = permissions_collection.find_one({"_id": group['permission_id']})
                if permission:
                    permission['id'] = str(permission.pop('_id'))
                    group['permission'] = permission
            
            return group
        except Exception as e:
            raise Exception(f"Failed to get group with permissions: {str(e)}")

    @classmethod
    async def get_batch_with_permissions(cls, group_ids: List[str]) -> List[Dict]:
        """Get multiple groups with their permission details"""
        try:
            collection = cls.get_collection()
            permissions_collection = Permissions.get_collection()
            
            # Get all groups
            groups = list(collection.find({"_id": {"$in": group_ids}}))
            
            # Get all unique permission IDs
            permission_ids = set(group['permission_id'] for group in groups if group.get('permission_id'))
            
            # Fetch all permissions in one query
            permissions = list(permissions_collection.find({"_id": {"$in": list(permission_ids)}}))
            permissions_dict = {str(perm['_id']): {**perm, 'id': str(perm.pop('_id'))} 
                              for perm in permissions}
            
            # Transform groups and add permission details
            result = []
            for group in groups:
                group_data = {
                    **group,
                    'id': str(group.pop('_id'))
                }
                
                if group.get('permission_id') and group['permission_id'] in permissions_dict:
                    group_data['permission'] = permissions_dict[group['permission_id']]
                
                result.append(group_data)
                
            return result
        except Exception as e:
            raise Exception(f"Failed to get groups batch with permissions: {str(e)}")

    @classmethod
    async def update_permission(cls, group_id: str, permission_id: str) -> bool:
        """Update group's permission"""
        try:
            collection = cls.get_collection()
            
            # Verify permission exists
            permissions_collection = Permissions.get_collection()
            permission = permissions_collection.find_one({"_id": permission_id})
            if not permission:
                raise Exception("Permission not found")
            
            result = collection.update_one(
                {"_id": group_id},
                {
                    "$set": {
                        "permission_id": permission_id,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to update group permission: {str(e)}")

    @classmethod
    async def get_with_users_and_permissions(cls, group_id: str) -> Optional[Dict]:
        """Get group with both user and permission details"""
        try:
            collection = cls.get_collection()
            users_collection = User.get_collection()
            permissions_collection = Permissions.get_collection()
            
            # Get the group first
            group = collection.find_one({"_id": group_id})
            if not group:
                return None
                
            # Transform _id to id
            group['id'] = str(group.pop('_id'))
            
            # Get users data
            if group.get('user_ids'):
                users = list(users_collection.find({"_id": {"$in": group['user_ids']}}))
                group['users'] = [{**user, 'id': str(user.pop('_id'))} for user in users]
            else:
                group['users'] = []
            
            # Get permission data
            if group.get('permission_id'):
                permission = permissions_collection.find_one({"_id": group['permission_id']})
                if permission:
                    permission['id'] = str(permission.pop('_id'))
                    group['permission'] = permission
            
            return group
        except Exception as e:
            raise Exception(f"Failed to get group with users and permissions: {str(e)}")

class Organization(BaseModel):
    id: str = Field( alias="_id")
    name: str
    business_email: str
    industrial_type: str
    company_size: str
    domain: str
    image: Optional[str] = None
    plan_id: str
    admin_id: str
    configurations: Configurations = Field(default_factory=Configurations)
    group_ids: List[str] = Field(default_factory=list)
    settings: Settings = Field(default_factory=Settings)
    credits: int = Field(default=1000) 
    opentopublic: bool = Field(default=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    status: str = Field(default="inactive", pattern="^(active|inactive|deleted)$")

    class Config:
        populate_by_name = True

    @staticmethod
    def get_collection() -> Collection:
        """Get MongoDB collection with consistent connection"""
        db_connection = get_mongo_db(db_name=DB_NAME, collection_name="tenant_organizations")
        return db_connection.db.organizations

    @staticmethod
    def get_related_collections() -> Dict[str, Collection]:
        """Get related MongoDB collections"""
        return {
            'users': User.get_collection(),
            'groups': Group.get_collection(),
            'plans': Plan.get_collection()
        }

    @classmethod
    async def get_total_count(cls) -> int:
        try:
            collection = cls.get_collection()
            return collection.count_documents({})
        except Exception as e:
            raise Exception(f"Failed to get organization count: {str(e)}")
        
    @classmethod
    async def create(cls, org_data: Dict) -> Optional[str]:
        try:
            org_obj = cls(**org_data)
            org_dict = org_obj.dict(exclude_none=True, by_alias=True)
            org_dict['created_at'] = datetime.utcnow()
            org_dict['updated_at'] = datetime.utcnow()
            
            collection = cls.get_collection()
            result = collection.insert_one(org_dict)
            
            if result.inserted_id:
                created_doc = collection.find_one({"_id": result.inserted_id})
                if created_doc:
                    created_doc['id'] = str(created_doc.pop('_id'))
                    print(f"Created organization: {created_doc}")
            
            return str(result.inserted_id) if result else None
        except Exception as e:
            raise Exception(f"Failed to create organization: {str(e)}")
        
    @classmethod
    async def defineCosts(cls, cost_data: Dict) -> Optional[str]:
        try:
            mongo_handler = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
            await mongo_handler.insert(cost_data, mongo_handler.db)
        except Exception as e:
            raise Exception(f"Failed to initialize costs")

    @classmethod
    async def get(cls, org_id: str) -> Dict[str, Any]:
        try:
            collection = cls.get_collection()
            raw_data = collection.find_one({"_id": org_id})
            if raw_data:
                org_with_defaults = cls(**raw_data)
                return org_with_defaults.model_dump()
            return {}
        except Exception as e:
            raise Exception(f"Failed to get organization: {str(e)}")
        
    @classmethod
    def get_sync(cls, org_id: str) -> Dict[str, Any]:
        try:
            collection = cls.get_collection()
            raw_data = collection.find_one({"_id": org_id})
            if raw_data:
                org_with_defaults = cls(**raw_data)
                return org_with_defaults.model_dump()
            return {}
        except Exception as e:
            raise Exception(f"Failed to get organization: {str(e)}")

    @classmethod
    async def get_all(cls) -> List[Dict]:
        try:
            collection = cls.get_collection()
            orgs = list(collection.find())
            return [{**o, 'id': str(o.pop('_id'))} for o in orgs]
        except Exception as e:
            raise Exception(f"Failed to get all organizations: {str(e)}")
        
    @classmethod
    async def get_with_admin(cls, latest: bool = True) -> List[Dict]:
        try:
            collection = cls.get_collection()
            users_collection = cls.get_related_collections()['users']
            
            # Add sort parameter based on latest flag
            sort_order = -1 if latest else 1
            orgs = list(collection.find().sort("created_at", sort_order))
            result = []
            
            for org in orgs:
                # Get admin details
                admin = users_collection.find_one({"_id": org["admin_id"]})
                if admin:
                    admin['id'] = str(admin.pop('_id'))
                    org_dict = {**org, 'id': str(org.pop('_id')), 'admin': admin}
                    result.append(org_dict)
            
            return result
        except Exception as e:
            raise Exception(f"Failed to get organizations with admin details: {str(e)}")
        
    @classmethod
    async def get_with_admin_cost(cls, latest: bool = True, filter_prefix: Optional[str] = None, exclude_prefix: Optional[str] = None) -> List[Dict]:
        try:
            collection = cls.get_collection()
            users_collection = cls.get_related_collections()['users']
            llm_cost_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs").db.llm_costs
            
            # Add sort parameter based on latest flag
            sort_order = -1 if latest else 1
            
            # Build query filter
            query_filter = {}
            if filter_prefix:
                query_filter["_id"] = {"$regex": f"^{filter_prefix}"}
            elif exclude_prefix:
                query_filter["_id"] = {"$not": {"$regex": f"^{exclude_prefix}"}}
            
            orgs = list(collection.find(query_filter).sort("created_at", sort_order))
            result = []
            
            for org in orgs:
                # Skip organizations without admin_id
                if "admin_id" not in org:
                    continue

                # Get admin details
                admin = users_collection.find_one({"_id": org["admin_id"]})

                #Get llm costs
                cost = llm_cost_collection.find_one({"organization_id": org["_id"]})
                if cost:
                    org_cost = float(cost['organization_cost'].replace('$', ''))
                    current_cost = float(cost.get('cost', '0.00').replace('$', ''))
                else:
                    org_cost = 0.00
                    current_cost = 0.00

                if admin:
                    admin['id'] = str(admin.pop('_id'))
                    org_dict = {**org, 'id': str(org.pop('_id')), 'admin': admin , 'cost': org_cost,'current_cost': current_cost}
                    result.append(org_dict)
            
            return result
        except Exception as e:
            raise Exception(f"Failed to get organizations with admin details: {str(e)}")
        
    @classmethod
    async def get_users_by_organization_id(cls, organization_id: str) -> List[Dict]:
        """
        Retrieve users array from llm_costs collection for a specific organization_id
        and enrich each user with their current plan name.
        
        Args:
            cls: The class instance
            organization_id: The organization ID to filter by
            
        Returns:
            List of user dictionaries from the llm_costs collection with plan names
        """
        try:
            # Get the llm_costs collection
            llm_cost_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs").db.llm_costs
            
            
            # Find the document with the specified organization_id
            cost_doc = llm_cost_collection.find_one({"organization_id": organization_id})
            
            if not cost_doc:
                return []
            
            # Extract the users array from the document
            users = cost_doc.get("users", [])
            
            # Fetch all products from Stripe using existing endpoint
            from app.routes.products_route import get_products  # Import your existing function
            products = await get_products()
            
            # Create a mapping of price_id to product name for faster lookups
            price_to_product_map = {}
            price_to_amount_map = {}
            for product in products:
                price_to_product_map[product.price_id] = product.product_name
                price_to_amount_map[product.price_id] = product.credits if hasattr(product, 'credits') else 0
            
            # Add free plan to the mapping
            price_to_product_map["free_plan"] = "Free Plan"
            price_to_amount_map["free_plan"] = 0
            
            # Retrieve plan names for each user
            for user in users:
                current_plan_id = user.get("current_plan")
                if current_plan_id:
                    # Look up the plan name in our mapping
                    plan_name = price_to_product_map.get(current_plan_id, "Unknown Plan")
                    user["current_plan_name"] = plan_name
                    credits = price_to_amount_map.get(current_plan_id, 0)
                    user["credits"] = 50000 if credits == 0 else credits
                else:
                    user["current_plan_name"] = "No Plan"
            
            return users
        except Exception as e:
            raise Exception(f"Failed to get users for organization {organization_id}: {str(e)}")
    @classmethod
    async def update(cls, org_id: str, org_data: Dict) -> bool:
        try:
            print("Received org_data:" , org_data)
            collection = cls.get_collection()
            update_data = {
                "$set": {
                    **org_data,
                    "updated_at": datetime.utcnow()
                }
            }
            result = collection.update_one(
                {"_id": org_id},
                update_data
            )
            
            if result.modified_count > 0:
                updated_doc = collection.find_one({"_id": org_id})
                if updated_doc:
                    updated_doc['id'] = str(updated_doc.pop('_id'))
                    print(f"Updated organization: {updated_doc}")
            
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to update organization: {str(e)}")

    @classmethod
    async def delete(cls, org_id: str) -> bool:
        try:
            collection = cls.get_collection()
            related_collections = cls.get_related_collections()
            
            # Get the organization first
            org = collection.find_one({"_id": org_id})
            if not org:
                print(f"No organization found with ID: {org_id}")
                return False

            # Mark all users belonging to this organization as deleted
            related_collections['users'].update_many(
                {"organization_id": org_id},
                {
                    "$set": {
                        "status": "deleted",
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            # Mark all groups belonging to this organization as deleted
            if org.get('group_ids'):
                related_collections['groups'].update_many(
                    {"_id": {"$in": org['group_ids']}},
                    {
                        "$set": {
                            "status": "deleted",
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
            
            # Mark the organization as deleted
            result = collection.update_one(
                {"_id": org_id},
                {
                    "$set": {
                        "status": "deleted",
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            deleted = result.modified_count > 0
            
            if deleted:
                print(f"Successfully marked organization with ID: {org_id} as deleted")
            
            return deleted
        except Exception as e:
            raise Exception(f"Failed to delete organization: {str(e)}")

    @classmethod
    async def get_by_domain(cls, domain: str) -> Optional[Dict]:
        """Get organization by domain"""
        try:
            collection = cls.get_collection()
            org = collection.find_one({"domain": domain})
            if org:
                org['id'] = str(org.pop('_id'))
            return org
        except Exception as e:
            raise Exception(f"Failed to get organization by domain: {str(e)}")

    @classmethod
    async def get_members(cls, org_id: str) -> Dict[str, int]:
        """Get organization member counts"""
        try:
            related_collections = cls.get_related_collections()
            
            total_users = related_collections['users'].count_documents({
                "organization_id": org_id
            })
            
            active_users = related_collections['users'].count_documents({
                "organization_id": org_id,
                "status": "active"
            })
            
            total_groups = related_collections['groups'].count_documents({
                "_id": {"$in": cls.get(org_id).get('group_ids', [])}
            })
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "total_groups": total_groups
            }
        except Exception as e:
            raise Exception(f"Failed to get organization members: {str(e)}")

    @classmethod
    async def add_group(cls, org_id: str, group_id: str) -> bool:
        """Add a group to the organization"""
        try:
            collection = cls.get_collection()
            result = collection.update_one(
                {"_id": org_id},
                {
                    "$addToSet": {"group_ids": group_id},
                    "$set": {"updated_at": datetime.utcnow()}
                }
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to add group to organization: {str(e)}")

    @classmethod
    async def remove_group(cls, org_id: str, group_id: str) -> bool:
        """Remove a group from the organization"""
        try:
            collection = cls.get_collection()
            result = collection.update_one(
                {"_id": org_id},
                {
                    "$pull": {"group_ids": group_id},
                    "$set": {"updated_at": datetime.utcnow()}
                }
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to remove group from organization: {str(e)}")

    @classmethod
    async def get_users(cls, org_id: str, include_inactive: bool = False, latest: bool = True) -> List[Dict]:
        """
        Get all users belonging to the organization
        
        Args:
            org_id (str): Organization ID
            include_inactive (bool): Whether to include inactive users (default: False)
            latest (bool): Sort by latest first (default: True)
        
        Returns:
            List[Dict]: List of user documents
        """
        try:
            users_collection = User.get_collection()
            
            # Base query filter
            query_filter = {"organization_id": org_id}
            
            # Add status filter if we don't want inactive users
            if not include_inactive:
                query_filter["status"] = "active"

            # Add sort parameter based on latest flag
            sort_order = -1 if latest else 1
            users = list(users_collection.find(query_filter).sort("created_at", sort_order))
            
            # Transform user data
            transformed_users = []
            for user in users:
                # Transform _id to id
                user_data = {
                    **user,
                    'id': str(user.pop('_id'))
                }
                
                # If user has group_ids, get group details
                if user.get('group_ids'):
                    groups = list(Group.get_collection().find({
                        "_id": {"$in": user['group_ids']}
                    }))
                    user_data['groups'] = [{
                        **group,
                        'id': str(group.pop('_id'))
                    } for group in groups]
                else:
                    user_data['groups'] = []
                
                transformed_users.append(user_data)
            
            return transformed_users
        except Exception as e:
            raise Exception(f"Failed to get organization users: {str(e)}")
    @classmethod
    async def get_all_users_with_referral_data(cls, latest: bool = True) -> List[Dict]:
        """
        Get all users across all organizations that have both referral_code and referral_stats
        
        Args:
            latest (bool): Sort by latest first (default: True)
        
        Returns:
            List[Dict]: List of user documents with referral data from all organizations
        """
        try:
            users_collection = User.get_collection()
            organizations_collection = cls.get_collection()
            
            # Query filter for users with referral data
            query_filter = {
                "referral_code": {"$exists": True, "$ne": None},
                "referral_stats": {"$exists": True, "$ne": None},
                "status": "active"  # Only active users
            }
            
            # Add sort parameter based on latest flag
            sort_order = -1 if latest else 1
            users = list(users_collection.find(query_filter).sort("created_at", sort_order))
            
            # Get all unique organization IDs
            org_ids = list(set(user.get('organization_id') for user in users if user.get('organization_id')))
            
            # Fetch organization details in one query
            organizations = list(organizations_collection.find({"_id": {"$in": org_ids}}))
            org_dict = {org['_id']: org for org in organizations}
            
            # Transform user data and add organization info
            transformed_users = []
            for user in users:
                user_data = {
                    **user,
                    'id': str(user.pop('_id'))
                }
                
                # Add organization information
                org_id = user.get('organization_id')
                if org_id and org_id in org_dict:
                    user_data['organization'] = {
                        'id': org_dict[org_id]['_id'],
                        'name': org_dict[org_id].get('name', 'Unknown'),
                        'domain': org_dict[org_id].get('domain', ''),
                    }
                
                # If user has group_ids, get group details
                if user.get('group_ids'):
                    groups = list(Group.get_collection().find({
                        "_id": {"$in": user['group_ids']}
                    }))
                    user_data['groups'] = [{
                        **group,
                        'id': str(group.pop('_id'))
                    } for group in groups]
                else:
                    user_data['groups'] = []
                
                transformed_users.append(user_data)
            
            return transformed_users
            
        except Exception as e:
            raise Exception(f"Failed to get all users with referral data: {str(e)}")

    @classmethod
    async def get_users_by_group(cls, org_id: str, group_id: str, include_inactive: bool = False, latest: bool = True) -> List[Dict]:
        """
        Get all users belonging to a specific group in the organization
        
        Args:
            org_id (str): Organization ID
            group_id (str): Group ID
            include_inactive (bool): Whether to include inactive users (default: False)
            latest (bool): Sort by latest first (default: True)
        
        Returns:
            List[Dict]: List of user documents
        """
        try:
            users_collection = User.get_collection()
            
            # Build query filter
            query_filter = {
                "organization_id": org_id,
                "group_ids": group_id
            }
            
            # Add status filter if we don't want inactive users
            if not include_inactive:
                query_filter["status"] = "active"
            
            # Add sort parameter based on latest flag
            sort_order = -1 if latest else 1
            users = list(users_collection.find(query_filter).sort("created_at", sort_order))
            
            # Transform user data
            return [{
                **user,
                'id': str(user.pop('_id'))
            } for user in users]
        except Exception as e:
            raise Exception(f"Failed to get organization users by group: {str(e)}")

    @classmethod
    async def get_users_summary(cls, org_id: str) -> Dict:
        """
        Get summary of users in the organization
        
        Args:
            org_id (str): Organization ID
        
        Returns:
            Dict: Summary containing total users, active users, and users by group
        """
        try:
            users_collection = User.get_collection()
            
            # Get total users count
            total_users = users_collection.count_documents({
                "organization_id": org_id
            })
            
            # Get active users count
            active_users = users_collection.count_documents({
                "organization_id": org_id,
                "status": "active"
            })
            
            # Get users by group
            org = await cls.get(org_id)
            users_by_group = {}
            
            if org and org.get('group_ids'):
                for group_id in org['group_ids']:
                    group_users_count = users_collection.count_documents({
                        "organization_id": org_id,
                        "group_ids": group_id
                    })
                    users_by_group[group_id] = group_users_count
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "users_by_group": users_by_group
            }
        except Exception as e:
            raise Exception(f"Failed to get organization users summary: {str(e)}")

class BillingInterval(str, Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"

class Plan(BaseModel):
    id: str = Field(default_factory=generate_plan_id, alias="_id")
    name: str
    price: int
    interval: BillingInterval
    features: Dict[str, Union[str, bool]]
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    status: str = Field(default="active", pattern="^(active|inactive|deleted)$")

    class Config:
        populate_by_name = True

    @staticmethod
    def get_collection() -> Collection:
        """Get MongoDB collection with consistent connection"""
        db_connection = get_mongo_db(db_name=DB_NAME, collection_name="tenant_plans")
        return db_connection.db.plans

    @classmethod
    async def create(cls, plan_data: Dict) -> Optional[str]:
        try:
            plan_dict = {
                "_id": generate_plan_id(),
                **plan_data,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            collection = cls.get_collection()
            result = collection.insert_one(plan_dict)
            
            if result.inserted_id:
                created_doc = collection.find_one({"_id": result.inserted_id})
                if created_doc:
                    created_doc['id'] = created_doc.pop('_id')
                    print(f"Created plan: {created_doc}")
                    
            return result.inserted_id if result else None
        except Exception as e:
            raise Exception(f"Failed to create plan: {str(e)}")

    @classmethod
    async def get(cls, plan_id: str) -> Dict[str, Any]:
        try:
            collection = cls.get_collection()
            raw_data = collection.find_one({"_id": plan_id})
            if raw_data:
                plan_with_defaults = cls(**raw_data)
                return plan_with_defaults.model_dump()
            return {}
        except Exception as e:
            raise Exception(f"Failed to get plan: {str(e)}")

    @classmethod
    async def get_all(cls) -> List[Dict]:
        try:
            collection = cls.get_collection()
            plans = list(collection.find())
            return [{**p, 'id': p.pop('_id')} for p in plans]
        except Exception as e:
            raise Exception(f"Failed to get all plans: {str(e)}")

    @classmethod
    async def update(cls, plan_id: str, plan_data: Dict) -> bool:
        try:
            collection = cls.get_collection()
            update_data = {
                "$set": {
                    **plan_data,
                    "updated_at": datetime.utcnow()
                }
            }
            result = collection.update_one(
                {"_id": plan_id},
                update_data
            )
            
            if result.modified_count > 0:
                updated_doc = collection.find_one({"_id": plan_id})
                if updated_doc:
                    updated_doc['id'] = updated_doc.pop('_id')
                    print(f"Updated plan: {updated_doc}")
            
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to update plan: {str(e)}")

    @classmethod
    async def delete(cls, plan_id: str) -> bool:
        try:
            collection = cls.get_collection()
            
            # Check if plan exists
            plan = collection.find_one({"_id": plan_id})
            if not plan:
                return False

            # Check if any active organizations are using this plan
            orgs_collection = Organization.get_collection()
            org_using_plan = orgs_collection.find_one({
                "plan_id": plan_id,
                "status": "active"
            })
            if org_using_plan:
                raise Exception("Cannot delete plan as it is being used by active organizations")

            # Mark the plan as deleted
            result = collection.update_one(
                {"_id": plan_id},
                {
                    "$set": {
                        "status": "deleted",
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            raise Exception(f"Failed to delete plan: {str(e)}")

    @classmethod
    async def get_by_interval(cls, interval: BillingInterval) -> List[Dict]:
        """Get all plans for a specific billing interval"""
        try:
            collection = cls.get_collection()
            plans = list(collection.find({"interval": interval}))
            return [{**p, 'id': p.pop('_id')} for p in plans]
        except Exception as e:
            raise Exception(f"Failed to get plans by interval: {str(e)}")

    @classmethod
    async def get_by_price_range(cls, min_price: int, max_price: int) -> List[Dict]:
        """Get all plans within a price range"""
        try:
            collection = cls.get_collection()
            plans = list(collection.find({
                "price": {
                    "$gte": min_price,
                    "$lte": max_price
                }
            }))
            return [{**p, 'id': p.pop('_id')} for p in plans]
        except Exception as e:
            raise Exception(f"Failed to get plans by price range: {str(e)}")

async def initialize_default_plans():
    """Initialize default plans in the database"""
    default_plans = [
        {
            "_id": generate_plan_id(),
            "name": "Plan Package",
            "price": 99,
            "interval": BillingInterval.MONTHLY,
            "features": {
                "workflow_templates": "up to 10",
                "workflow_management": "basic",
                "security": "standard",
                "workflow_analysis": False,
                "custom_workflow": False,
                "integration": False,
                "support": False,
                "developer_platform": False
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": generate_plan_id(),
            "name": "Inspect Package",
            "price": 299,
            "interval": BillingInterval.MONTHLY,
            "features": {
                "workflow_templates": "unlimited",
                "workflow_management": "advanced",
                "security": "enhanced",
                "workflow_analysis": True,
                "custom_workflow": False,
                "integration": False,
                "support": "business-hours",
                "developer_platform": False
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": generate_plan_id(),
            "name": "Build",
            "price": 2999,
            "interval": BillingInterval.MONTHLY,
            "features": {
                "workflow_templates": "unlimited",
                "workflow_management": "advanced",
                "security": "enhanced",
                "workflow_analysis": True,
                "custom_workflow": True,
                "integration": "full",
                "support": "24x7",
                "developer_platform": True
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": generate_plan_id(),
            "name": "Plan Package",
            "price": 999,
            "interval": BillingInterval.YEARLY,
            "features": {
                "workflow_templates": "up to 10",
                "workflow_management": "basic",
                "security": "standard",
                "workflow_analysis": False,
                "custom_workflow": False,
                "integration": False,
                "support": False,
                "developer_platform": False
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": generate_plan_id(),
            "name": "Inspect Package",
            "price": 2999,
            "interval": BillingInterval.YEARLY,
            "features": {
                "workflow_templates": "unlimited",
                "workflow_management": "advanced",
                "security": "enhanced",
                "workflow_analysis": True,
                "custom_workflow": False,
                "integration": False,
                "support": "business-hours",
                "developer_platform": False
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": generate_plan_id(),
            "name": "Build",
            "price": 29999,
            "interval": BillingInterval.YEARLY,
            "features": {
                "workflow_templates": "unlimited",
                "workflow_management": "advanced",
                "security": "enhanced",
                "workflow_analysis": True,
                "custom_workflow": True,
                "integration": "full",
                "support": "24x7",
                "developer_platform": True
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    ]

    try:
        collection = Plan.get_collection()
        
        # First, check if plans already exist
        existing_plans = await Plan.get_all()
        if existing_plans:
            print("Plans already initialized, skipping...")
            return
        
        # Insert all plans
        for plan in default_plans:
            result = collection.update_one(
                {"_id": plan["_id"]},
                {"$set": plan},
                upsert=True
            )
            if result.upserted_id:
                print(f"Created plan: {plan['name']} ({plan['interval']})")
            elif result.modified_count > 0:
                print(f"Updated plan: {plan['name']} ({plan['interval']})")
                
        print("Successfully initialized all default plans")
        
    except Exception as e:
        print(f"Error initializing plans: {str(e)}")
        raise

    