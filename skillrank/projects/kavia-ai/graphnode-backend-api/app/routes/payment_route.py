import stripe
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional,List
import os
import logging
import subprocess 
import json
from pydantic import BaseModel, Field
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME, get_tenant_id
from app.utils.auth_utils import get_current_user
from enum import Enum
import secrets
from app.utils.datetime_utils import generate_timestamp, from_isoformat, to_utc
from app.core.Settings import settings
from datetime import timedelta
from app.models.product_model import ProductListResponse
from app.utils.product_utils import format_price, free_plan, plan_to_filter
import boto3
from app.connection.establish_db_connection import get_tenant_id
from app.routes.authentication_route import get_tenant_credentials

STRIPE_PUBLISHABLE_KEY = settings.STRIPE_PUBLISHABLE_KEY
STRIPE_SECRET_KEY = settings.STRIPE_SECRET_KEY

stripe.api_key = STRIPE_SECRET_KEY

SUCCESS_URL = "http://************:3000/payment/success"
CANCEL_URL = "http://************:3000/payment/cancel"




def generate_internal_secret_token():
    return secrets.token_hex(16)

class PaymentStatus(Enum):
    PENDING = 0
    INITIATED = 1
    COMPLETED = 2
    FAILED = 3


payment_session_template = {
    "session_id": None,
    "internal_status": PaymentStatus.PENDING.value,
    "price_id": None,
    "webhook_payment_status": PaymentStatus.PENDING.value,
    "tenant_id": None,
    "user_id": None,
    "internal_secret_token": None,
    "created_at": None,
    "updated_at": None,
    "webhook_confirmed": False,
    "webhook_event": None

}



active_subscription_template = {
    "subscription_id": None,
    "tenant_id": None,
    "user_id": None,
    "status": None,
    "created_at": None,
    "updated_at": None,
    "payment_session_id": None,
    "expires_at": None,
}


KAVIA_ROOT_DB = get_mongo_db(KAVIA_ROOT_DB_NAME).db

COLLECTION_NAME = "payment_sessions"
ACTIVE_SUBSCRIPTIONS_COLLECTION_NAME = "active_subscriptions"
ORGANIZATION_COLLECTION_NAME  = "organizations"
LLM_COST_COLLECTION_NAME = "llm_costs"
SHOW_NAME = "payment"
router = APIRouter(
    prefix=f"/{SHOW_NAME}",
    tags=[SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

def getCredits(product):
    credits = None
    if product.metadata:
        # Try multiple possible key names (case insensitive)
        possible_keys = ['Credits', 'credits', 'CREDITS', 'Credit', 'credit']
        
        for key in possible_keys:
            if key in product.metadata:
                try:
                    # Remove commas and convert to int
                    credits_str = str(product.metadata[key]).replace(',', '').strip()
                    credits = int(credits_str)
                    break
                except (ValueError, TypeError) as e:
                    print(f"Invalid credits value for {product.id} with key '{key}': {product.metadata[key]} - {e}")
        
    # If still not found, check if any key contains the word "credit"
    if credits is None:
        for key, value in product.metadata.items():
            if 'credit' in key.lower():
                try:
                    # Remove commas and convert to int
                    credits_str = str(value).replace(',', '').strip()
                    credits = int(credits_str)
                    break
                except (ValueError, TypeError):
                    continue
    
    if credits is None: 
        credits = 50000 #Credits for free plan

    return credits
        


@router.get("/get-products")
async def get_products() -> List[ProductListResponse]:
    #with pricing
    products = stripe.Product.list(
        active=True,
        expand=["data.default_price"]
    )

    filtered_products = [product for product in products if product.name in plan_to_filter["names"]]
    product_list_response = [ProductListResponse(
        product_id=product.id,
        product_name=product.name,
        product_description=product.description,
        price_id=product.default_price.id,
        currency=product.default_price.currency,
        credits=getCredits(product),
        price=format_price(product.default_price.unit_amount_decimal),
        is_recurring=product.default_price.recurring.interval is not None,
        recurring_interval=product.default_price.recurring.interval,
        recurring_interval_count=product.default_price.recurring.interval_count
    ) for product in filtered_products]

    return [free_plan] + product_list_response

class CreateCheckoutSessionRequest(BaseModel):
    price_id: str
    success_url: str = Field(default=SUCCESS_URL)
    cancel_url: str = Field(default=CANCEL_URL)


def check_if_subscribed(user_id: str, tenant_id: str):
    active_subscription = KAVIA_ROOT_DB[ACTIVE_SUBSCRIPTIONS_COLLECTION_NAME].find_one({"user_id": user_id, "tenant_id": tenant_id},sort=[("created_at", -1)])
    if active_subscription:
        condition = active_subscription["status"] == PaymentStatus.COMPLETED.value and active_subscription["expires_at"] > generate_timestamp()
        if condition:
            return True
    return False

@router.get("/check-subscription-status")
async def check_subscription_status(current_user: dict = Depends(get_current_user)):
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    # _tenant_id is the tenant id for the b2c user pool client id
    _tenant_id = settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default_") or tenant_id.startswith("default-") else tenant_id
    if check_if_subscribed(user_id, tenant_id):
        creds = await get_tenant_credentials(_tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        response = client.admin_update_user_attributes(
            UserPoolId=creds['user_pool_id'],
            Username=user_id,
            UserAttributes=[
                {
                    'Name': 'custom:free_user',
                    'Value': str(False).lower()  # Convert to lowercase 'true' or 'false'
                }
            ]
        )
        return JSONResponse(content={"is_subscribed": True})
    else:
        return JSONResponse(content={"is_subscribed": False})

def add_params_to_url(url: str, params: dict) -> str:
    return url + "?" + "&".join([f"{key}={value}" for key, value in params.items()])


@router.post("/create-checkout-session")
async def create_checkout_session(request: CreateCheckoutSessionRequest, current_user: dict = Depends(get_current_user)):
    try:
        user_id = current_user.get("cognito:username")
        tenant_id = get_tenant_id()
        user_email = current_user.get("email").lower().strip()  # Normalize email

        print(f"Processing checkout for user: {user_id}, tenant: {tenant_id}, email: {user_email}")

        if not user_id or not tenant_id or not user_email:
            print(f"Missing required data - user_id: {user_id}, tenant_id: {tenant_id}, email: {user_email}")
            raise HTTPException(status_code=400, detail="User ID, email, or Tenant ID not found")
        
        # Check for existing customer
        print(f"Searching for existing Stripe customer with email: {user_email}")
        customers = stripe.Customer.list(email=user_email, limit=1)
        
        if customers.data:
            customer_id = customers.data[0].id
            print(f"Found existing customer: {customer_id}")
        else:
            print(f"No existing customer found for email: {user_email}. Creating new customer...")
            # Create new customer only if none exists
            customer = stripe.Customer.create(
                email=user_email,
                name=current_user.get("name", ""),
                metadata={
                    "tenant_id": tenant_id,
                    "user_id": user_id
                }
            )
            customer_id = customer.id
            print(f"Created new Stripe customer with ID: {customer_id}")

        # Rest of your function remains the same
        payment_session_template_to_insert = payment_session_template.copy()
        payment_session_template_to_insert["internal_secret_token"] = generate_internal_secret_token()
        print(f"Generated internal secret token: {payment_session_template_to_insert['internal_secret_token']}")
        
        payment_session_template_to_insert["created_at"] = generate_timestamp()
        payment_session_template_to_insert["updated_at"] = generate_timestamp()
        
        print(f"Creating checkout session with customer_id: {customer_id}, price_id: {request.price_id}")
        session = stripe.checkout.Session.create(
            customer=customer_id,  # Use the customer ID
            line_items=[
                {
                    "price": request.price_id,
                    "quantity": 1
                }
            ],
            mode="subscription",
            success_url=add_params_to_url(request.success_url, {"internal_secret_token": payment_session_template_to_insert['internal_secret_token']}),
            cancel_url=add_params_to_url(request.cancel_url, {"internal_secret_token": payment_session_template_to_insert['internal_secret_token']}),
            metadata={
                "tenant_id": tenant_id,
                "user_id": user_id
            }
        )
        print(f"Stripe checkout session created with ID: {session.id}")
        
        # Update your database
        payment_session_template_to_insert["session_id"] = session.id
        payment_session_template_to_insert["tenant_id"] = tenant_id
        payment_session_template_to_insert["user_id"] = user_id
        payment_session_template_to_insert["price_id"] = request.price_id
        payment_session_template_to_insert["internal_status"] = PaymentStatus.INITIATED.value
        payment_session_template_to_insert["stripe_customer_id"] = customer_id  # Save the customer ID

        print(f"Saving payment session to database: {payment_session_template_to_insert}")
        KAVIA_ROOT_DB[COLLECTION_NAME].update_one(
            {"session_id": session.id},
            {"$set": payment_session_template_to_insert},
            upsert=True
        )
        print(f"Payment session saved successfully")
        
        return JSONResponse(content={"session": session})
    
    except Exception as e:
        print(f"Error in create-checkout-session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
    
class SuccessRequest(BaseModel):
    internal_secret_token: str

@router.post("/success")
async def success(request: SuccessRequest, current_user: dict = Depends(get_current_user)):
    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()
    print("request for success session", request)
    internal_secret_token = request.internal_secret_token
    print(internal_secret_token)
    session = KAVIA_ROOT_DB[COLLECTION_NAME].find_one({"internal_secret_token": internal_secret_token})
    print("session",session)

    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session["user_id"] != user_id or session["tenant_id"] != tenant_id:
        raise HTTPException(status_code=403, detail="User or tenant does not match")
    
    KAVIA_ROOT_DB[COLLECTION_NAME].update_one(
        {"session_id": session["session_id"]},
        {"$set": {"internal_status": PaymentStatus.COMPLETED.value, "updated_at": generate_timestamp()}}
    )
    success_response = {
        'success': True,
        'message': 'Payment successful',
        'session_id': session["session_id"]
    }

    active_subscription_template_to_insert = active_subscription_template.copy()
    active_subscription_template_to_insert["created_at"] = generate_timestamp()
    active_subscription_template_to_insert["updated_at"] = generate_timestamp()
    active_subscription_template_to_insert["subscription_id"] = session["session_id"]
    active_subscription_template_to_insert["tenant_id"] = session["tenant_id"]
    active_subscription_template_to_insert["user_id"] = session["user_id"]
    active_subscription_template_to_insert["status"] = PaymentStatus.COMPLETED.value
    active_subscription_template_to_insert["payment_session_id"] = session["session_id"]
    active_subscription_template_to_insert["price_id"] = session["price_id"]
    active_subscription_template_to_insert["expires_at"] = (from_isoformat(session["created_at"]) + timedelta(days=30)).isoformat()


    KAVIA_ROOT_DB[ACTIVE_SUBSCRIPTIONS_COLLECTION_NAME].update_one(
        {"subscription_id": session["session_id"]},
        {"$set": active_subscription_template_to_insert},
        upsert=True
    )
    llm_cost_doc =KAVIA_ROOT_DB[LLM_COST_COLLECTION_NAME].find_one(
       {"organization_id": session["tenant_id"]},
    )
    
    try:
        price_id = session["price_id"]
        # Get the price object which contains the product ID
        price = stripe.Price.retrieve(price_id)
        # Get the product details using the product ID from the price object
        product = stripe.Product.retrieve(price.product)
        
        # Update the active subscription with product details
        product_details = {
            "product_id": product.id,
            "product_name": product.name,
            "product_description": product.description if hasattr(product, 'description') else "",
            "product_metadata": product.metadata,
            "price_details": {
                "currency": price.currency,
                "credits": getCredits(product),
                "recurring": price.recurring
            }
        }
        
        KAVIA_ROOT_DB[ORGANIZATION_COLLECTION_NAME].update_one(
            {"_id": tenant_id},
            {"$set": {"credits": product_details["price_details"]["credits"]}}
        )
        # Find and update the specific user in the organization document
        if llm_cost_doc:
            user_found = False
            for user in llm_cost_doc.get("users", []):
                if user.get("user_id") == session["user_id"]:
                    user_found = True

                    # Preserve current plan in history with projects (same as QA script)
                    current_plan = user.get("current_plan")
                    if current_plan and current_plan != session["price_id"]:
                        from datetime import datetime
                        old_plan = {
                            "plan_id": current_plan,
                            "cost": user.get("cost", "0.000000"),
                            "projects": user.get("projects", []),  # PRESERVE PROJECTS IN HISTORY
                            "upgraded_at": datetime.utcnow().isoformat()
                        }

                        if "plans_history" not in user:
                            user["plans_history"] = []
                        user["plans_history"].append(old_plan)

                    # Update user's current plan and reset cost + projects
                    user["current_plan"] = session["price_id"]
                    user["cost"] = "$0.000"
                    user["user_cost"] = "$0.000"
                    user["projects"] = []  # RESET PROJECTS FOR NEW PLAN
                    break

            if not user_found:
                logging.warning(f"User {session['user_id']} not found in LLM costs document for organization {session['tenant_id']}")
        
        KAVIA_ROOT_DB[LLM_COST_COLLECTION_NAME].update_one(
           {"organization_id": session["tenant_id"]},
            {"$set": llm_cost_doc},
            upsert=True
        )   
       
        
    except Exception as e:
        logging.error(f"Error fetching product details from Stripe: {str(e)}")
        # Continue with the process even if stripe fetch fails
      

    return JSONResponse(content=success_response)

@router.post("/cancel")
async def cancel(request: SuccessRequest, current_user: dict = Depends(get_current_user)):
    internal_secret_token = request.internal_secret_token
    print(internal_secret_token)

    user_id = current_user.get("cognito:username")
    tenant_id = get_tenant_id()

    session = KAVIA_ROOT_DB[COLLECTION_NAME].find_one({"internal_secret_token": internal_secret_token})
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session["user_id"] != user_id or session["tenant_id"] != tenant_id:
        raise HTTPException(status_code=403, detail="User or tenant does not match")
    
    KAVIA_ROOT_DB[COLLECTION_NAME].update_one(
        {"session_id": session["session_id"]},
        {"$set": {"internal_status": PaymentStatus.FAILED.value, "updated_at": generate_timestamp()}}
    )
    cancel_response = {
        'success': True,
        'message': 'Payment canceled',
        'session_id': session["session_id"]
    }
    return JSONResponse(content=cancel_response)

@router.get("/latest-subscription/{user_id}")
async def get_latest_subscription(user_id: str):
    try:
        # Find the latest subscription document for the tenant
        latest_subscription = KAVIA_ROOT_DB[ACTIVE_SUBSCRIPTIONS_COLLECTION_NAME].find_one(
            {"user_id": user_id},
            sort=[("created_at", -1)]  # Sort by created_at in descending order
        )

        if not latest_subscription:
            raise HTTPException(
                status_code=404,
                detail=f"No subscription found for tenant {user_id}"
            )

        # Convert ObjectId to string for JSON serialization
        latest_subscription["_id"] = str(latest_subscription["_id"])
        try:
            # First get the organization_id from the subscription or determine it
            organization_id = latest_subscription.get("organization_id", "b2c")  # Default to "b2c" if not present
            
            llm_cost_doc = KAVIA_ROOT_DB[LLM_COST_COLLECTION_NAME].find_one(
                {"organization_id": organization_id}
            )
            
            if llm_cost_doc:
                # Find the specific user in the users array
                user_cost_data = None
                for user in llm_cost_doc.get("users", []):
                    if user.get("user_id") == user_id:
                        user_cost_data = user
                        break
                
                if user_cost_data:
                    # Get the cost value (remove $ sign and convert to float for comparison)
                    llm_cost_str = user_cost_data.get("cost", "0")
                    llm_cost = float(llm_cost_str) if isinstance(llm_cost_str, str) else float(llm_cost_str)
                    
                    latest_subscription["current_cost"] = llm_cost
                        
        except Exception as e:
            # Log the error but continue without cost update
            print(f"Error fetching cost data from llm_costs: {str(e)}")
        
        # Fetch product details based on price_id
        try:
            from app.routes.products_route import get_products
            # First, get all products from Stripe
            products = await get_products()
            
            # Find the matching product for the price_id in the subscription
            subscription_price_id = latest_subscription.get("price_id")
            product_details = None
            
            # Handle the free plan special case
            if subscription_price_id == "free_plan":
                product_details = {
                    "product_name": "Free",
                    "price": "0",
                    "credits": 50000,
                    "currency": "USD",
                    "recurring_interval": "month",
                    "recurring_interval_count": 1
                }
            else:
                # Find matching product in Stripe products
                for product in products:
                    if product.price_id == subscription_price_id:
                        product_details = {
                            "product_name": product.product_name,
                            "price": product.price,
                            "credits": product.credits,
                            "currency": product.currency,
                            "recurring_interval": product.recurring_interval,
                            "recurring_interval_count": product.recurring_interval_count
                        }
                        break
            
            # Add product details to the response
            if product_details:
                latest_subscription.update(product_details)
            
        except Exception as e:
            # Log the error but continue to return the subscription without product details
            print(f"Error fetching product details: {str(e)}")
            
        return JSONResponse(content=latest_subscription)
    except HTTPException: 
        raise 
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/stripe/billing-portal/{email}")
async def get_billing_portal(email: str):
    try:
        print(f"Getting billing portal for email: {email}")
        
        # Normalize email
        normalized_email = email.lower().strip()
        print(f"Normalized email: {normalized_email}")
        
        # Search for the customer by email
        print(f"Searching for Stripe customer with email: {normalized_email}")
        customers = stripe.Customer.list(email=normalized_email, limit=10)
        
        print(f"Found {len(customers.data)} customers with this email")
        
        # Check if we found a customer with this email
        if not customers.data:
            print(f"No customer found with email: {normalized_email}")
            raise HTTPException(status_code=404, detail=f"No customer found with email: {normalized_email}")
        
        # Get the first customer with this email
        customer_id = customers.data[0].id
        print(f"Using first customer found: {customer_id}")
        
        # Create a billing portal session with this customer ID
        print(f"Creating billing portal session for customer: {customer_id}")
        session = stripe.billing_portal.Session.create(
            customer=customer_id,
            # return_url="https://yourapplication.com/account",  # URL to redirect after leaving portal
        )
        
        print(f"Billing portal URL generated: {session.url}")
        
        # Return the URL to redirect to
        return {"url": session.url, "customer_id": customer_id}
    
    except stripe.error.StripeError as e:
        print(f"Stripe error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        print(f"General error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/stripe/check-customer-invoices/{email}")
async def check_customer_invoices(email: str):
    try:
        print(f"Checking invoices for email: {email}")
        
        # Normalize email
        normalized_email = email.lower().strip()
        
        # Find the customer
        customers = stripe.Customer.list(email=normalized_email, limit=1)
        if not customers.data:
            return {"status": "error", "message": f"No customer found with email: {normalized_email}"}
        
        customer_id = customers.data[0].id
        
        # Check for invoices
        invoices = stripe.Invoice.list(customer=customer_id, limit=10)
        
        return {
            "customer_id": customer_id,
            "has_invoices": len(invoices.data) > 0,
            "invoice_count": len(invoices.data)
        }
        
    except stripe.error.StripeError as e:
        return {"status": "error", "message": str(e)}

@router.get("/api/stripe/check-multiple-customers/{email}")
async def check_multiple_customers(email: str):
    try:
        print(f"Checking multiple customers for email: {email}")
        
        # Normalize email
        normalized_email = email.lower().strip()
        
        # Find all customers with this email
        customers = stripe.Customer.list(email=normalized_email)
        
        return {
            "email": normalized_email,
            "customer_count": len(customers.data),
            "customers": [
                {
                    "id": customer.id,
                    "name": customer.name,
                    "created": customer.created
                } for customer in customers.data
            ]
        }
        
    except stripe.error.StripeError as e:
        return {"status": "error", "message": str(e)}