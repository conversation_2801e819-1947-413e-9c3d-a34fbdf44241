from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from app.utils.datetime_utils import generate_timestamp

class ContainerConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Container")
        self.template_name = None  # Will be set after retrieving architecture pattern
        self.function_schema_type = "ContainerConfiguration"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        #self.model_name = 'gpt-4o'
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def async_initialize(self):
        await super().async_initialize()
        

    async def retrieve_info(self):
        """Retrieve container information"""
        await super().retrieve_info()

         # Get project node and architecture pattern
        project_node = self.retrieved_info['root_node']
        self.retrieved_info['project_details'] = {
            'description': project_node['properties'].get('Description', ''),
            'scope': project_node['properties'].get('Scope', ''),
            'architecture_strategy': project_node['properties'].get('ArchitectureStrategy', ''),
            'additional_details': project_node['properties'].get('AdditionalDetails', {}),
            'team_composition': project_node['properties'].get('TeamComposition', '')
        }
        architecture_pattern = project_node['properties'].get('ArchitecturePattern', 'adaptive').lower()
        self.retrieved_info['architecture_pattern'] = architecture_pattern
        
        # Set template based on container type
        container_type = self.node.get('properties', {}).get('ContainerType', 'internal')
        # Get system context information
        system_context = await self.db.get_parent_node(self.node_id)
        if container_type == 'external':
            self.template_name = "external_container_configuration.prompt"   

        else:        
            # Set appropriate template based on architecture pattern
            if architecture_pattern in ['monolithic-application', 'monolithic-service', 'multi-container-single-component']:
                self.template_name = "monolithic_container_configuration.prompt"
                self.update_logger.info(f"Using monolithic container template for {architecture_pattern} pattern")
            elif architecture_pattern == 'multi-container-service':
                self.template_name = "multicontainer_service_container_configuration.prompt"
                self.update_logger.info(f"Using multi-container service container template for multi-container-service pattern")
            else:
                self.template_name = "container_configuration.prompt"
                self.update_logger.info("Using standard container template")
            

            self.retrieved_info['architecture_pattern'] = architecture_pattern

            
            if system_context:
                self.retrieved_info['system_context'] = {
                    'id': system_context['id'],
                    'users': system_context['properties'].get('Users', []),
                    'external_systems': system_context['properties'].get('ExternalSystems', []),
                    'title': system_context['properties'].get('Title', ''),
                    'description': system_context['properties'].get('Description', '')
                }

            # Get architectural requirements
            # Retrieve architectural requirements
            architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
            if architecture_root:
                arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
                if arch_req_nodes:
                    functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                    nonfunctional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "NonFunctionalRequirement")
                    
                    self.retrieved_info['functional_requirements'] = functional_reqs
                    self.retrieved_info['architectural_requirements'] = nonfunctional_reqs
            
            # Get existing components within this container
            existing_components = await self.db.get_child_nodes(self.node_id, "Component")
            self.retrieved_info['existing_components'] = existing_components

            # Get interface information
            interface_nodes = await self.db.get_child_nodes(self.node_id, "Interface")
            if interface_nodes:
                interface_node = interface_nodes[0]
                self.retrieved_info['incoming_interfaces'] = json.loads(
                    interface_node['properties'].get('incoming_interfaces', '[]')
                )
            else:
                self.retrieved_info['incoming_interfaces'] = []

            # Get other containers in the system for potential interfaces
            if system_context:
                other_containers = await self.db.get_child_nodes(system_context['id'], "Container")
                self.retrieved_info['other_containers'] = [
                    container for container in other_containers 
                    if container['id'] != self.node_id
                ]

            # Get container's basic information
            self.retrieved_info['container'] = {
                'repository_name': self.node['properties'].get('Repository_Name'),
                'title': self.node['properties'].get('Title', '')
            }

            external_containers = [
                container for container in other_containers
                if container['properties'].get('ContainerType') == 'external'
            ]
            
            # Add external containers to retrieved info with formatted details
            self.retrieved_info['external_containers'] = [
                {
                    'id': container['id'],
                    'title': container['properties'].get('Title', ''),
                    'description': container['properties'].get('Description', ''),
                    'technology': container['properties'].get('Technology', '')
                }
                for container in external_containers
            ]                  
            self.update_logger.info(f"- External Containers Count: {len(self.retrieved_info.get('external_containers', []))}")
            # Get existing USES relationships
            existing_uses = await self.db.get_relationships_involving_node(
                self.node_id, 
                "USES"
            )
            self.retrieved_info['existing_uses'] = existing_uses
            self.update_logger.info(f"- Existing USES Relationships: {len(self.retrieved_info.get('existing_uses', []))}")
            # Add debug logging
            self.update_logger.info(f"Retrieved info for container {self.node_id}:")
            self.update_logger.info(f"- System Context: {self.retrieved_info.get('system_context', {}).get('id')}")
            self.update_logger.info(f"- Components Count: {len(self.retrieved_info.get('existing_components', []))}")
            self.update_logger.info(f"- Interfaces Count: {len(self.retrieved_info.get('incoming_interfaces', []))}")
            self.update_logger.info(f"- Other Containers Count: {len(self.retrieved_info.get('other_containers', []))}")

 
           
        self.project_id = project_node['id']
        existing_session = await get_or_create_session(self.current_user, self.project_id)
        self.doc_session_id = existing_session.get("session_id")

        # Retrieve existing external systems and users

        # Retrieve existing containers
        containers = await self.db.get_child_nodes(system_context['id'], "Container")
        self.retrieved_info['containers'] = containers

        # Retrieve existing external systems and users
        current_container = self.retrieved_info['current_node']
        
        self.retrieved_info['current_container'] = current_container
        self.retrieved_info['system_context'] = system_context


        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_node:
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }
        
        # Retrieve existing containers
        components = await self.db.get_child_nodes(self.node_id, "Component")

        container_node = await self.db.get_node_by_id(self.node_id)

        container_component_details = []
        container_component_details.append({
            'container': container_node['properties'] if project_node else {},
            'components': components
        })


         # Structure current background info with complete node information
        # Structure current background info with complete node information
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'system_context': {
                'system_context': self.retrieved_info['system_context']
            },
            'requirements_context': {
                'functional_requirements': architectural_reqs['functional_requirements'],
                'architectural_requirements': architectural_reqs['architectural_requirements'],
                'parent_node': arch_req_node[0] if arch_req_node else None  # Store parent node for context
            }
        }

        # Handle reconfiguration
        if self.is_reconfig():
            self.model_name = 'gpt-4.1'
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info
    
    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
    
    def get_modifications_from_llm_output(self):
        if self.node.get('properties', {}).get('ContainerType') == 'external':
            # External container only needs Interface child node type
            self.modifications['child_node_types'] = ['Interface']
        else:
            self.modifications['child_node_types'] = ['Architecture','Component','Interface','Database']
            
            # Extract new relationships from LLM output if any
            self.modifications['new_relationships'] = self.modifications.get('new_relationships', [])
            
        return self.modifications

    async def merge_captured_items(self):

        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    if self.node.get('properties', {}).get('ContainerType') == 'external':
                        # Let base class handle node creation
                        await super().merge_captured_items()
                    else:
                        await super().merge_captured_items()
                        # Get interfaces from modifications
                        relationships = self.modifications.get('new_relationships', [])
                        created_nodes = self.modifications.get('created_nodes', [])
                        
                        if relationships and not isinstance(relationships, str):
                            # Process new relationships
                            for relationship in relationships:
                                if relationship.get('type') == 'USES':
                                    await self.db.create_relationship(
                                        self.node_id,  # Design node as consumer
                                        relationship['target'],  # External container
                                        'USES',
                                        {
                                            'name': relationship.get('name'),
                                            'type': relationship.get('type'),
                                            'description': relationship.get('description'),
                                            'technology': relationship.get('technology')
                                        }
                                    )
                                    self.update_logger.info(f"Created USES relationship from {relationship['source']} to {relationship['target']}")
                                elif relationship.get('type') == 'interfacesWith':
                                    # Track components and their interfaces
                                    components_to_update = set()
                                    interface_mappings = {}

                                    # Use the current relationship as the interface
                                    source_id = self.get_node_id(relationship['source'], created_nodes)
                                    target_id = self.get_node_id(relationship['target'], created_nodes)
                                    
                                    if source_id and target_id:
                                        # Add target to components that need interface nodes
                                        components_to_update.add(target_id)
                                        if target_id not in interface_mappings:
                                            interface_mappings[target_id] = []
                                        interface_mappings[target_id].append({
                                            'source_id': source_id,
                                            'interface': relationship  # Using the relationship as interface
                                        })

                                    # Second pass: Process each component's interfaces
                                    for component_id in components_to_update:
                                        # Get or create interface node
                                        interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
                                        if not interface_nodes:
                                            node = await self.db.get_node_by_id(component_id)
                                            interface_node = await self.db.create_node(
                                                ["Interface"],
                                                {
                                                    "Title": f"Interfaces for {node['properties'].get('Title', 'Component')}",
                                                    "incoming_interfaces": "[]"
                                                },
                                                component_id
                                            )
                                        else:
                                            interface_node = interface_nodes[0]

                                        # Update the interfaces
                                        existing_interfaces = json.loads(
                                            interface_node['properties'].get('incoming_interfaces', '[]')
                                        )
                                        
                                        # Add new interfaces
                                        for interface_info in interface_mappings[component_id]:
                                            source_id = interface_info['source_id']
                                            interface = interface_info['interface']
                                            
                                            new_interface = {
                                                'name': interface['name'],
                                                'type': interface['type'],
                                                'description': interface.get('description', interface['name']),
                                                'interfaceType': interface.get('interfaceType','Unknown'),
                                                'source_component_id': source_id
                                            }
                                            existing_interfaces.append(new_interface)

                                        # Update the interface node
                                        await self.db.update_node_by_id(
                                            interface_node['id'],
                                            {"incoming_interfaces": json.dumps(existing_interfaces)}
                                        )

                        #Remove the ID field.
                        for node in created_nodes:
                            try:
                                # Using Neo4j REMOVE clause to delete the property
                                query = """
                                MATCH (n)
                                WHERE ID(n) = $node_id
                                REMOVE n.ID
                                """
                                await self.db.async_run(query, node_id=node['id'])
                                self.update_logger.info(f"Removed ID field from node {node['id']}")
                            except Exception as e:
                                self.update_logger.error(f"Failed to remove ID from node {node['id']}: {str(e)}")

                        # Handle IMPLEMENTS relationships
                        await self.handle_implements_relationships()

                        # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
                if self.node.get('properties', {}).get('ContainerType') == 'external':
                        # Let base class handle node creation
                        await super().merge_captured_items()
                else:
                    await super().merge_captured_items()
                    # Get interfaces from modifications
                    relationships = self.modifications.get('new_relationships', [])
                    created_nodes = self.modifications.get('created_nodes', [])
                    
                    if relationships:
                        # Process new relationships
                        for relationship in relationships:
                            if relationship.get('type') == 'USES':
                                await self.db.create_relationship(
                                    self.node_id,  # Design node as consumer
                                    relationship['target'],  # External container
                                    'USES',
                                    {
                                        'name': relationship.get('name'),
                                        'type': relationship.get('type'),
                                        'description': relationship.get('description'),
                                        'technology': relationship.get('technology')
                                    }
                                )
                                self.update_logger.info(f"Created USES relationship from {relationship['source']} to {relationship['target']}")
                            elif relationship.get('type') == 'interfacesWith':
                                # Track components and their interfaces
                                components_to_update = set()
                                interface_mappings = {}

                                # Use the current relationship as the interface
                                source_id = self.get_node_id(relationship['source'], created_nodes)
                                target_id = self.get_node_id(relationship['target'], created_nodes)
                                
                                if source_id and target_id:
                                    # Add target to components that need interface nodes
                                    components_to_update.add(target_id)
                                    if target_id not in interface_mappings:
                                        interface_mappings[target_id] = []
                                    interface_mappings[target_id].append({
                                        'source_id': source_id,
                                        'interface': relationship  # Using the relationship as interface
                                    })

                                # Second pass: Process each component's interfaces
                                for component_id in components_to_update:
                                    # Get or create interface node
                                    interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
                                    if not interface_nodes:
                                        node = await self.db.get_node_by_id(component_id)
                                        interface_node = await self.db.create_node(
                                            ["Interface"],
                                            {
                                                "Title": f"Interfaces for {node['properties'].get('Title', 'Component')}",
                                                "incoming_interfaces": "[]"
                                            },
                                            component_id
                                        )
                                    else:
                                        interface_node = interface_nodes[0]

                                    # Update the interfaces
                                    existing_interfaces = json.loads(
                                        interface_node['properties'].get('incoming_interfaces', '[]')
                                    )
                                    
                                    # Add new interfaces
                                    for interface_info in interface_mappings[component_id]:
                                        source_id = interface_info['source_id']
                                        interface = interface_info['interface']
                                        
                                        new_interface = {
                                            'name': interface['name'],
                                            'type': interface['type'],
                                            'description': interface.get('description', interface['name']),
                                            'interfaceType': interface.get('interfaceType','Unknown'),
                                            'source_component_id': source_id
                                        }
                                        existing_interfaces.append(new_interface)

                                    # Update the interface node
                                    await self.db.update_node_by_id(
                                        interface_node['id'],
                                        {"incoming_interfaces": json.dumps(existing_interfaces)}
                                    )

                        #Remove the ID field.
                        for node in created_nodes:
                            try:
                                # Using Neo4j REMOVE clause to delete the property
                                query = """
                                MATCH (n)
                                WHERE ID(n) = $node_id
                                REMOVE n.ID
                                """
                                await self.db.async_run(query, node_id=node['id'])
                                self.update_logger.info(f"Removed ID field from node {node['id']}")
                            except Exception as e:
                                self.update_logger.error(f"Failed to remove ID from node {node['id']}: {str(e)}")

                        # Handle IMPLEMENTS relationships
                        await self.handle_implements_relationships()

                save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'initial_config',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                await self.version_manager.save_node_info(save_data)

        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise

    async def handle_implements_relationships(self):
        """
        Handles the creation of IMPLEMENTS relationships between a Component/Container and Requirements.
        Expects ImplementedRequirements to be an array of integers representing requirement node IDs.
        """
        try:
            node = await self.db.get_node_by_id(self.node_id)
            implemented_req_ids = node['properties'].get('ImplementedRequirementIDs', [])
            # Check if the value is a string and unwrap it
            if isinstance(implemented_req_ids, str):
                # Parse the string into a Python list
                implemented_req_ids = json.loads(implemented_req_ids)
                
            # Process each requirement ID
            for req_id in implemented_req_ids:
                requirement = await self.db.get_node_by_id(req_id)
                if requirement:
                    await self.db.create_relationship(
                        self.node_id, 
                        requirement['id'],
                        "IMPLEMENTS",
                        {
                            "Type": requirement['labels'][0],
                            "Description": f"Implements requirement"
                        }
                    )
                    self.update_logger.info(f"Created IMPLEMENTS relationship for requirement {req_id}")
                    
        except Exception as e:
            self.update_logger.error(f"Error processing requirements relationships: {str(e)}")
    # Fix 1: Modify get_node_id to be synchronous for simple cases
    def get_node_id(self, node_reference, created_nodes):
        """Synchronous node ID resolution for created and existing nodes"""
        if '-' in node_reference:
            prefix, num = node_reference.split('-', 1)
            if prefix == 'NEW':
                # Check created_nodes
                for node in created_nodes:
                    if node['properties'].get('ID') == node_reference:
                        return node['id']
                return None
            elif prefix == 'EXISTING':
                try:
                    return int(num)
                except ValueError:
                    return None
        return None

    async def get_architectural_requirements(self, root_node_id):
        """
        Override the base method to return actual node objects instead of string properties.
        
        :param root_node_id: ID of the root architecture node
        :return: Dictionary containing requirement node objects
        """
        result = await self.db.get_child_nodes(root_node_id, "ArchitecturalRequirement")
        if not result:
            return {
                'architectural_requirements': None,
                'functional_requirements': None
            }

        arch_req_node = result[0]
        # Get full requirement nodes
        functional_reqs = await self.db.get_child_nodes(arch_req_node['id'], "FunctionalRequirement")
        nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node['id'], "NonFunctionalRequirement")
        
        return {
            'functional_requirements': functional_reqs,
            'architectural_requirements': nonfunctional_reqs
        }
    
DiscussionFactory.register('configuration', ContainerConfiguration, "Container")