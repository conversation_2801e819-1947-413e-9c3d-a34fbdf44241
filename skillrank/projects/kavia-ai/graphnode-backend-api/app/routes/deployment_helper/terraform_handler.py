from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
import os
import logging
import subprocess
import json
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
from github.Repository import Repository
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.auth_utils import get_current_user
import os
import logging
import yaml
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import tempfile
import shutil
import asyncio
from botocore.exceptions import ClientError
import boto3
from app.routes.deployment_helper.terraform_state_handler import deploy_with_workspace

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

        
        
async def deploy_infrastructure_handler(
    project_id: int,
    container_id: int,
    infrastructure_path: str,
    terraform_files: Dict[str, str],
    reuse_workspace: bool = True
) -> Dict[str, Any]:
    """
    Deploy infrastructure using Terraform workspace management with reuse option.
    
    Args:
        project_id: Project ID
        container_id: Container ID
        infrastructure_path: Path to infrastructure files
        terraform_files: Dictionary of terraform files to deploy
        reuse_workspace: Whether to reuse existing workspace if available
        
    Returns:
        Dict containing terraform outputs
    """
    try:
        # Deploy using workspace management
        outputs = await deploy_with_workspace(
            project_id=project_id,
            container_id=container_id,
            infrastructure_path=infrastructure_path,
            terraform_files=terraform_files,
            reuse_existing=reuse_workspace
        )
        
        return outputs
        
    except Exception as e:
        logger.error(f"Error in infrastructure deployment: {str(e)}")
        raise
