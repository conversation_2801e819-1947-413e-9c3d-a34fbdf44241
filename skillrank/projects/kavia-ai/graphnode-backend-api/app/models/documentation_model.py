# app/models/documentation_model.py
from pydantic import BaseModel
from typing import Optional, Dict


class CreateDocumentationRequest(BaseModel):
    project_id: int
    documentation_type: str = "SAD"
    interface_id: Optional[int] = None

class CreateSection(BaseModel):
    project_id: int
    section_name: str
    interface_id: Optional[int] = None  

class SectionOrder(BaseModel):
    order: int

class SectionReorder(BaseModel):
    project_id: int
    section_orders: Dict[int, SectionOrder]
    interface_id: Optional[int] = None  