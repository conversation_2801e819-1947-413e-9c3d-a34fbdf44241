import json
from typing import Dict, List, Any, Optional, Type, TypeVar, cast
from .models import *

T = TypeVar('T')


class FigmaParser:
    """
    A comprehensive parser for Figma UI component data with strong typing and validation.
    """

    def __init__(self):
        self.node_map: Dict[str, Node] = {}
        self.style_map: Dict[str, Dict[str, Any]] = {}
        self.font_set: set[str] = set()
        self.image_refs: set[str] = set()

    def parse(self, data: Dict[str, Any]) -> FigmaDocument:
        """
        Main parsing method for Figma data.
        Args:
            data: Raw Figma JSON data
        Returns:
            Parsed and structured Figma document
        """
        if 'work_items' not in data:
            raise ValueError("Invalid Figma data structure: 'work_items' not found")

        work_items = data['work_items']

        return FigmaDocument(
            metadata=self._extract_metadata(work_items),
            components=self._parse_components(work_items.get('figma_components', [])),
            styles=self._extract_styles(),
            assets=self._extract_assets(),
            interactions=self._extract_interactions()
        )

    def _extract_metadata(self, work_items: Dict[str, Any]) -> Dict[str, str]:
        """Extract basic metadata from work items."""
        return {
            'component_name': str(work_items.get('component_name', '')),
            'description': str(work_items.get('description', '')),
            'repository_name': str(work_items.get('repository_name', '')),
            'root_folder': str(work_items.get('root_folder', ''))
        }

    def _parse_components(self, components: List[Dict[str, Any]]) -> List[Component]:
        """Parse all components from the Figma data."""
        parsed_components: List[Component] = []
        for component in components:
            parsed_component = self._parse_component(component)
            self.node_map[parsed_component.id] = parsed_component.hierarchy
            parsed_components.append(parsed_component)
        return parsed_components

    def _parse_component(self, component: Dict[str, Any]) -> Component:
        """Parse individual component data."""
        json_data = component.get('json_data', {})
        return Component(
            id=str(component.get('frame_id', '')),
            file_key=str(component.get('file_key', '')),
            hierarchy=self._build_node_hierarchy(json_data),
            styles=self._extract_component_styles(json_data),
            layout=self._extract_layout(json_data),
            constraints=self._extract_constraints(json_data)
        )

    def _parse_enum(self, enum_class: Type[T], value: str, default: T) -> T:
        """Safely parse enum values with a default."""
        try:
            return enum_class(value)
        except (ValueError, KeyError):
            return default

    def _parse_color(self, color_data: Dict[str, float]) -> Color:
        """Parse color data into Color object."""
        return Color(
            r=float(color_data.get('r', 0)),
            g=float(color_data.get('g', 0)),
            b=float(color_data.get('b', 0)),
            a=float(color_data.get('a', 1))
        )

    def _parse_paint(self, paint_data: Dict[str, Any]) -> Paint:
        """Parse paint data into Paint object."""
        paint_type = self._parse_enum(PaintType, paint_data.get('type', ''), PaintType.SOLID)
        blend_mode = self._parse_enum(BlendMode, paint_data.get('blendMode', ''), BlendMode.NORMAL)

        color = None
        if 'color' in paint_data:
            color = self._parse_color(paint_data['color'])

        gradient_stops = None
        if 'gradientStops' in paint_data:
            gradient_stops = [
                GradientStop(
                    position=float(stop.get('position', 0)),
                    color=self._parse_color(stop.get('color', {}))
                )
                for stop in paint_data['gradientStops']
            ]

        return Paint(
            type=paint_type,
            color=color,
            gradient_stops=gradient_stops,
            blend_mode=blend_mode,
            opacity=float(paint_data.get('opacity', 1.0))
        )

    def _parse_effect(self, effect_data: Dict[str, Any]) -> Effect:
        """Parse effect data into Effect object."""
        effect_type = self._parse_enum(EffectType, effect_data.get('type', ''), EffectType.DROP_SHADOW)

        color = None
        if 'color' in effect_data:
            color = self._parse_color(effect_data['color'])

        offset = None
        if 'offset' in effect_data:
            offset = Vector(
                x=float(effect_data['offset'].get('x', 0)),
                y=float(effect_data['offset'].get('y', 0))
            )

        return Effect(
            type=effect_type,
            color=color,
            offset=offset,
            radius=float(effect_data.get('radius', 0)),
            visible=bool(effect_data.get('visible', True)),
            spread=float(effect_data.get('spread', 0))
        )

    def _build_node_hierarchy(self, node_data: Dict[str, Any]) -> Node:
        """Build hierarchical structure of nodes."""
        node = Node(
            id=str(node_data.get('id', '')),
            name=str(node_data.get('name', '')),
            type=str(node_data.get('type', '')),
            visible=bool(node_data.get('visible', True)),
            properties=self._extract_node_properties(node_data),
            children=[],
            fills=[self._parse_paint(fill) for fill in node_data.get('fills', [])],
            strokes=[self._parse_paint(stroke) for stroke in node_data.get('strokes', [])],
            effects=[self._parse_effect(effect) for effect in node_data.get('effects', [])],
            layout=self._extract_layout(node_data),
            constraints=self._extract_constraints(node_data),
            text_style=self._parse_text_style(node_data.get('style', {})),
            characters=str(node_data.get('characters', '')),
            interactions=self._parse_interactions(node_data.get('interactions', []))
        )

        if 'children' in node_data and isinstance(node_data['children'], list):
            node.children = [
                self._build_node_hierarchy(child)
                for child in node_data['children']
            ]

        return node

    def _parse_text_style(self, style_data: Dict[str, Any]) -> Optional[TextStyle]:
        """Parse text style data into TextStyle object."""
        if not style_data:
            return None

        font_family = str(style_data.get('fontFamily', ''))
        if font_family:
            self.font_set.add(font_family)

        return TextStyle(
            font_family=font_family,
            font_size=float(style_data.get('fontSize', 0)),
            font_weight=int(style_data.get('fontWeight', 400)),
            letter_spacing=float(style_data.get('letterSpacing', 0)),
            line_height_px=float(style_data.get('lineHeightPx', 0)),
            text_align_horizontal=str(style_data.get('textAlignHorizontal', 'LEFT')),
            text_align_vertical=str(style_data.get('textAlignVertical', 'TOP')),
            text_decoration=style_data.get('textDecoration'),
            text_transform=style_data.get('textTransform'),
            paragraph_spacing=style_data.get('paragraphSpacing'),
            paragraph_indent=style_data.get('paragraphIndent')
        )

    def _extract_node_properties(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Extract all properties from a node."""
        return {
            'fills': [self._parse_paint(fill) for fill in node.get('fills', [])],
            'strokes': [self._parse_paint(stroke) for stroke in node.get('strokes', [])],
            'effects': [self._parse_effect(effect) for effect in node.get('effects', [])],
            'bounds': node.get('absoluteBoundingBox'),
            'constraints': node.get('constraints'),
            'layoutMode': node.get('layoutMode'),
            'text': node.get('characters'),
            'textStyle': self._parse_text_style(node.get('style', {})),
            'interactions': self._parse_interactions(node.get('interactions', []))
        }

    def _parse_interactions(self, interactions_data: Optional[List[Dict[str, Any]]]) -> List[Interaction]:
        """Parse interactions data into Interaction objects."""
        if interactions_data is None:
            return []

        return [
            Interaction(
                trigger=cast(Dict[str, str], interaction.get('trigger', {})),
                actions=[
                    Action(
                        type=str(action.get('type', '')),
                        destination_id=action.get('destinationId'),
                        navigation=action.get('navigation'),
                        transition=action.get('transition')
                    )
                    for action in interaction.get('actions', [])
                ]
            )
            for interaction in interactions_data
        ]

    def _extract_layout(self, node: Dict[str, Any]) -> Layout:
        """Extract layout information from a node."""
        layout = Layout()

        if 'layoutMode' in node:
            layout.mode = str(node['layoutMode'])
            layout.padding = Padding(
                top=float(node.get('paddingTop', 0)),
                right=float(node.get('paddingRight', 0)),
                bottom=float(node.get('paddingBottom', 0)),
                left=float(node.get('paddingLeft', 0))
            )
            layout.spacing = float(node.get('itemSpacing', 0))
            layout.alignment = {
                'primary': str(node.get('primaryAxisAlignItems', '')),
                'counter': str(node.get('counterAxisAlignItems', ''))
            }

        if 'absoluteBoundingBox' in node:
            box = node['absoluteBoundingBox']
            layout.position = Bounds(
                x=float(box.get('x', 0)),
                y=float(box.get('y', 0)),
                width=float(box.get('width', 0)),
                height=float(box.get('height', 0))
            )

        return layout

    def _extract_constraints(self, node: Dict[str, Any]) -> Optional[Constraints]:
        """Extract layout constraints from a node."""
        constraints_data = node.get('constraints')
        if not constraints_data:
            return None

        return Constraints(
            horizontal=str(constraints_data.get('horizontal', 'LEFT')),
            vertical=str(constraints_data.get('vertical', 'TOP'))
        )

    def _extract_component_styles(self, node: Dict[str, Any]) -> Dict[str, List[Union[Paint, Effect]]]:
        """Extract all styles from a component."""
        return {
            'fills': [self._parse_paint(fill) for fill in node.get('fills', [])],
            'strokes': [self._parse_paint(stroke) for stroke in node.get('strokes', [])],
            'effects': [self._parse_effect(effect) for effect in node.get('effects', [])]
        }

    def _extract_styles(self) -> Dict[str, List[Union[Paint, Effect, TextStyle]]]:
        """Extract all styles used in the document."""
        styles: Dict[str, List[Union[Paint, Effect, TextStyle]]] = {
            'fills': [],
            'effects': [],
            'text': []
        }

        # Collect styles from all nodes
        for node in self.node_map.values():
            if node.fills:
                styles['fills'].extend(node.fills)
            if node.effects:
                styles['effects'].extend(node.effects)
            if node.text_style:
                styles['text'].append(node.text_style)

        # Remove duplicates while preserving order
        for key in styles:
            seen = set()
            styles[key] = [x for x in styles[key] if not (str(x) in seen or seen.add(str(x)))]

        return styles

    def _extract_assets(self) -> Dict[str, List[str]]:
        """Extract all assets (images, fonts, etc.) used in the document."""
        return {
            'images': sorted(list(self.image_refs)),
            'fonts': sorted(list(self.font_set))
        }

    def _extract_interactions(self) -> List[Dict[str, Any]]:
        """Extract all interactions from the document."""
        interactions = []
        for node_id, node in self.node_map.items():
            if node.interactions:
                interactions.append({
                    'nodeId': node_id,
                    'nodeName': node.name,
                    'interactions': [
                        {
                            'trigger': interaction.trigger,
                            'actions': [
                                {
                                    'type': action.type,
                                    'destinationId': action.destination_id,
                                    'navigation': action.navigation,
                                    'transition': action.transition
                                }
                                for action in interaction.actions
                            ]
                        }
                        for interaction in node.interactions
                    ]
                })
        return interactions

    def _collect_styles_from_node(self, node: Node) -> None:
        """Recursively collect styles from a node and its children."""
        # Collect fills
        for fill in node.fills:
            style_id = str(id(fill))
            if style_id not in self.style_map.get('fills', {}):
                if 'fills' not in self.style_map:
                    self.style_map['fills'] = {}
                self.style_map['fills'][style_id] = fill

        # Collect effects
        for effect in node.effects:
            style_id = str(id(effect))
            if style_id not in self.style_map.get('effects', {}):
                if 'effects' not in self.style_map:
                    self.style_map['effects'] = {}
                self.style_map['effects'][style_id] = effect

        # Collect text styles
        if node.text_style:
            style_id = str(id(node.text_style))
            if style_id not in self.style_map.get('text', {}):
                if 'text' not in self.style_map:
                    self.style_map['text'] = {}
                self.style_map['text'][style_id] = node.text_style

        # Recursively process children
        for child in node.children:
            self._collect_styles_from_node(child)

    def _collect_image_references(self, node: Node) -> None:
        """Recursively collect image references from a node and its children."""
        for fill in node.fills:
            if fill.type == PaintType.IMAGE and isinstance(fill.color, str):
                self.image_refs.add(fill.color)

        for child in node.children:
            self._collect_image_references(child)



def parse_figma_json(json_data) -> FigmaDocument:
    try:
        parser = FigmaParser()

        return parser.parse(json_data)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON data: {str(e)}")
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        raise Exception(f"Error parsing Figma data: {str(e)}")


def parse_figma_file(json_data: str) -> FigmaDocument:
    """
    Parse a Figma file from JSON string.
    Args:
        json_data: JSON string containing Figma file data
    Returns:
        Parsed Figma document
    """
    try:
        data = json.loads(json_data)
        parser = FigmaParser()
        return parser.parse(data)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON data: {str(e)}")
    except Exception as e:
        raise Exception(f"Error parsing Figma data: {str(e)}")
