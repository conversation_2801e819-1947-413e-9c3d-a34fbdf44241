import asyncio
from fastapi import HTT<PERSON>Exception
from jinja2 import Environment, FileSystemLoader
import json
from app.agents.agent import Agent
from app.models.node_model import ConfigureNodeRequest
from app.discussions.discussion_util import  conduct_discussion,create_root_node_properties
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.node_utils import get_node_type
from app.connection.establish_db_connection import get_node_db
from app.connection.llm_init import get_llm_interface
from app.discussions.discussion import Discussion
from app.discussions.types.architecture_design_details import ArchitectureDesignDetails
from app.telemetry.logger_config import get_logger ,set_task_id
from app.core.task_framework import TaskStatus


class UserStoryManagerAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.llm_interface = get_llm_interface()
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.architecture_root_id = None
        self.update_logger_agent = get_logger(__name__)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        self.queue: asyncio.Queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            self.update_logger_agent.info(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
            if work_item_dict.entry == "configure_userstory":
                await self.configure_userstory(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id)
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found for agent {self.name}")
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)

    async def run(self):
        while True:
            try:
                self.current_work_item = await self.queue.get()
                await self.process_work_item(self.current_work_item)
            except Exception as e:
                self.update_logger_agent.error(f"Error in run method: {str(e)}", exc_info=True)
            finally:
                self.queue.task_done()

    async def check_parent_configurations(self, node_id):
        """
        Check if all parent nodes up to the project node are properly configured.
        
        Args:
            node_id: ID of the node to check
            
        Returns:
            tuple: (is_valid, message) where:
                - is_valid is a boolean indicating if all parents are configured
                - message is a string containing error details or None if valid
        """
        try:
            # Get the current node
            node = await self.db.get_node_by_id(node_id)
            if not node:
                return False, f"Node {node_id} not found"
                
            # Initialize list to track unconfigured parents
            unconfigured_parents = []
            
            # Start with the immediate parent
            current_id = node_id
            
            # Check all parents up to project node
            while current_id:
                parent_node = await self.db.get_parent_node(current_id)
                
                # If no parent found, we've reached the top of the hierarchy
                if not parent_node:
                    break
                    
                # Check if parent is configured
                parent_state = parent_node.get('properties', {}).get('configuration_state')
                if parent_state != 'configured':
                    parent_type = Discussion.get_specific_node_type(parent_node['labels'])
                    parent_title = parent_node.get('properties', {}).get('Title', f"Node {parent_node['id']}")
                    unconfigured_parents.append(f"{parent_type}: {parent_title}")
                    
                # Move up to next parent
                current_id = parent_node['id']
                
                # Stop if we reach a Project node (top of hierarchy)
                if 'Project' in parent_node['labels']:
                    break
            
            # Determine result
            if unconfigured_parents:
                return False, f"The following parent nodes must be configured first: {', '.join(unconfigured_parents)}"
            
            return True, None
            
        except Exception as e:
            self.update_logger_agent.error(f"Error checking parent configurations: {str(e)}", exc_info=True)
            return False, f"Error checking parent configurations: {str(e)}"

    async def get_epics_for_project(self, project_id):
        """Configure containers in C4 model"""
        try:
            # Get system context
            requirementroot = await self.db.get_child_nodes(project_id, "RequirementRoot")
            if not requirementroot:
                raise ValueError("RequirementRoot not found")
            
            requirementroot = requirementroot[0]
            
            # Get existing containers
            epics = await self.db.get_child_nodes(requirementroot['id'], "Epic")

            return epics

        except Exception as e:
            self.update_logger_agent.error(
                f"Error getting epics for project {project_id}: {str(e)}", 
                exc_info=True
            )
            return []

        
    async def configure_userstory(self, node_id, node_type, project_id):
        if not self.supervisor.partial_autoconfig:
            # If individual configuration is enabled, configure a single user story
            await self.configure_single_userstory(node_id, node_type)
        else:
            # Configure multiple user stories under the project
            await self.configure_multiple_user_stories(project_id, node_type)

    async def configure_multiple_user_stories(self, project_id, node_type):
        """
        Configure user stories by first getting epics and then configuring user stories for each epic
        
        Args:
            project_id (str): The ID of the project
            node_type (str): The type of the node
        """
        try:
            self.node_id = project_id
            self.node_type = node_type
            
            # First get all epics
            epics = await self.get_epics_for_project(project_id)
            
            self.update_logger_agent.info(
                f"Found {len(epics)} epics in project {project_id}"
            )
            
            total_stories_configured = 0
            
            # Process each epic
            for epic in epics:
                try:
                    epic_id = epic['id']
                    epic_title = epic['properties'].get('title', 'Untitled Epic')
                    
                    self.update_logger_agent.info(
                        f"Processing user stories for epic: {epic_title} ({epic_id})"
                    )
                    
                    # Get user stories for this epic
                    user_stories = await self.db.get_child_nodes(epic_id, "Epic")

                    # Add parent configuration check
                    # parents_configured, message = await self.check_parent_configurations(user_stories[0]['id'])
                    # if not parents_configured:
                    #     self.update_logger_agent.warning(f"Cannot process work item: {message}")
                    #     # Notify supervisor that this task cannot proceed
                    #     await self.supervisor.notify_complete(self.current_work_item, success=False, error_message=message)
                    #     return
                    
                    self.update_logger_agent.info(
                        f"Found {len(user_stories)} user stories in epic {epic_title}"
                    )
                    
                    # Configure each user story in this epic
                    for story in user_stories:
                        try:
                            story_id = story['id']
                            story_title = story['properties'].get('title', 'Untitled Story')
                            
                            self.update_logger_agent.info(
                                f"Configuring user story: {story_title} ({story_id}) in epic {epic_title}"
                            )
                            
                            await self.configure_node(
                                node_id=story_id,
                                node_type="UserStory",
                                root_node_type="Project",
                                configuration_type='configuration'
                            )
                        
                            
                        except Exception as story_error:
                            self.update_logger_agent.error(
                                f"Error configuring user story {story_id}: {str(story_error)}", 
                                exc_info=True
                            )
                            await self.update_story_status(
                                story_id, 
                                "ConfigurationFailed",
                                epic_id=epic_id
                            )
                            continue
                            
                except Exception as epic_error:
                    self.update_logger_agent.error(
                        f"Error processing epic {epic_id}: {str(epic_error)}", 
                        exc_info=True
                    )
                    continue
            
            self.update_logger_agent.info(
                f"Completed configuration of {total_stories_configured} user stories across {len(epics)} epics"
            )
            
            # Update work item status
            self.current_work_item.node_id = self.node_id
            self.current_work_item.node_type = node_type
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error in configure_user_stories: {str(e)}", exc_info=True)
            raise

    async def configure_single_userstory(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Configuring user story for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(node_id, node_type="UserStory", root_node_type="Project", configuration_type='configuration')
            
            await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring user story: {str(e)}", exc_info=True)

    

    async def generate_test_cases(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Generating test cases for user story: {node_id}")
            await self.configure_node(node_id, node_type="UserStory", root_node_type="Project", configuration_type='testcase_generation')
        except Exception as e:
            self.update_logger_agent.error(f"Error generating test cases: {str(e)}", exc_info=True)


    async def configure_node(self, node_id, node_type, root_node_type, configuration_type):
        """
        Configure or reconfigure a requirement root node. This method handles both initial configuration 
        and subsequent reconfigurations based on system changes.
        
        Args:
            node_id: The ID of the node to configure
            node_type: The type of the node (RequirementRoot in this case)
            root_node_type: The type of the root node (typically Project)
            configuration_type: The type of configuration to perform
        """
        try:
            # Log the start of configuration process
            self.update_logger_agent.info(f"Starting configuration process for requirement root node: {node_id}")

            # Get or create task configuration from MongoDB
            # This maintains the state and progress of the configuration task
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            
            if not task_configuration:
                # Initialize new task configuration if none exists
                task_configuration = {
                    'task_id': self.task_id, 
                    'queue_messages': [], 
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            # Extract task tracking information
            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']

            # Retrieve the node to be configured
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, 
                                                        {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            # Update configuration status tracking
            # This helps in monitoring the progress of configuration
            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'

            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)
            # Get the specific node type from the node's labels
            # This ensures we're using the correct type for the discussion
            node_type = Discussion.get_specific_node_type(node['labels'])
            
            # Initiate the configuration discussion
            # This will handle both initial configuration and reconfiguration
            # The discussion class will manage version comparison and change detection
            await conduct_discussion(
                node_id=node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=configuration_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )

            # Update the configuration status to reflect completion
            self.configuration_status[str(node_id)][configuration_type] = 'configured'
            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)
            # Log successful completion
            self.update_logger_agent.info(f"Successfully completed configuration for node {node_id}")
            
            # Retrieve and return the updated node
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            # Log any errors that occur during configuration
            self.update_logger_agent.error(f"Error in configure_node for node {node_id}: {str(e)}", 
                                        exc_info=True)
            raise