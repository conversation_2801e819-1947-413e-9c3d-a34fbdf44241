# app/telemetry/location_tracking_middleware.py
import time
import json
from datetime import datetime, timezone
from fastapi import Request
from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode
from starlette.middleware.base import BaseHTTPMiddleware
from app.telemetry.logger_config import get_logger
from ..models.tracking_model import APITrackingLog
from app.services.enhanced_tracking_service import enhanced_tracking_service
from app.connection.establish_db_connection import get_mongo_db, MongoDBHandler
import uuid
import jwt

logger = get_logger(__name__)
tracer = trace.get_tracer(__name__)

class LocationTrackingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        request_id = str(uuid.uuid4())
        
        with tracer.start_as_current_span("api_request_with_location") as span:
            try:
                response = await call_next(request)
                
                # Skip tracking for certain paths
                if self._should_skip_tracking(request):
                    return response
                
                # Extract information after auth middleware
                user_info = getattr(request.state, 'user', {})
                tenant_id = getattr(request.state, 'tenant_id', None)
                
                process_time = time.time() - start_time
                current_time = datetime.now(timezone.utc)
                current_db_context = None
                try:
                    mongo_client = get_mongo_db()
                    if hasattr(mongo_client, 'name'):
                        current_db_context = mongo_client.name
                except:
                    pass
                # Extract tenant information
                final_tenant_id = tenant_id or self._extract_jwt_tenant(request) or request.headers.get("selected_tenant_id") or "unknown"
                
                # Get client IP (handle proxy headers)
                client_ip = self._get_client_ip(request)
                
                # Parse user agent
                device_type, browser = self._parse_user_agent(request.headers.get("user-agent", ""))
                
                # Determine response type
                response_type = "success" if response.status_code < 400 else "error"
                
                # Create tracking document
                tracking_log = APITrackingLog(
                    request_id=request_id,
                    created_at=current_time,
                    date_partition=current_time.strftime("%Y-%m-%d"),
                    hour_partition=current_time.hour,
                    
                    # Tenant & User
                    tenant_id=final_tenant_id,
                    tenant_name=self._get_tenant_name(final_tenant_id),
                    user_id=self._safe_get(user_info, "sub", "anonymous"),
                    user_email=self._safe_get(user_info, "email", ""),
                    username=self._safe_get(user_info, "cognito:username", ""),
                    user_role=self._get_user_role(user_info),
                    
                    # Request details
                    method=request.method,
                    endpoint=request.url.path,
                    full_url=str(request.url),
                    status_code=response.status_code,
                    duration_ms=round(process_time * 1000, 2),
                    
                    # Response info
                    response_size_bytes=int(response.headers.get("content-length", 0)),
                    response_type=response_type,
                    
                    # Client info (IP will be used for geolocation in service)
                    client_ip=client_ip,
                    user_agent=request.headers.get("user-agent", ""),
                    device_type=device_type,
                    browser=browser,
                    
                    # Auth info
                    auth_method="bearer" if request.headers.get("Authorization") else "none",
                    has_valid_token=bool(user_info and user_info.get("sub")),
                    
                    # Business context
                    resource_type=self._extract_resource_info(request.url.path),
                    action_type=self._determine_action_type(request.method, request.url.path),
                    
                    # Additional data
                    custom_headers={
                        "is_public_selected": request.headers.get("is_public_selected", ""),
                        "selected_tenant_id": request.headers.get("selected_tenant_id", ""),
                        "bypass_credit_check": request.headers.get("Bypass-Credit-Check", ""),
                    },
                    query_params=dict(request.query_params) if request.query_params else {},
                    
                    # Tags for categorization
                    tags=self._generate_tags(request, response, user_info)
                )
                
                # Store in MongoDB with location tracking (async)
                await enhanced_tracking_service.store_tracking_data(tracking_log)
                
                # Print tracking info for non-OPTIONS requests
                if request.method != "OPTIONS":
                    self._print_tracking_info(tracking_log)
                
                return response
                
            except Exception as e:
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR, str(e)))
                logger.error(f"Error in location tracking middleware: {str(e)}")
                return await call_next(request) if 'response' not in locals() else response

    def _get_client_ip(self, request: Request) -> str:
        """Extract real client IP considering proxy headers"""
        # Check common proxy headers in order of preference
        proxy_headers = [
            "CF-Connecting-IP",      # Cloudflare
            "X-Forwarded-For",       # Standard proxy header
            "X-Real-IP",             # Nginx proxy
            "X-Client-IP",           # Alternative header
            "X-Forwarded",           # Variant
            "Forwarded-For",         # Variant
            "Forwarded"              # RFC 7239
        ]
        
        for header in proxy_headers:
            ip = request.headers.get(header)
            if ip:
                # X-Forwarded-For can contain multiple IPs, take the first one
                if "," in ip:
                    ip = ip.split(",")[0].strip()
                # Validate IP format (basic check)
                if self._is_valid_ip(ip):
                    return ip
        
        # Fallback to direct client IP
        return self._safe_get(request.client, 'host', 'unknown') if request.client else "unknown"
    
    def _is_valid_ip(self, ip: str) -> bool:
        """Basic IP validation"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False

    def _should_skip_tracking(self, request: Request) -> bool:
        """Determine if we should skip tracking for this request"""
        skip_paths = ["/api/health", "/docs", "/redoc", "/openapi.json", "/favicon.ico"]
        return any(request.url.path.startswith(path) for path in skip_paths)

    def _safe_get(self, obj, key, default=""):
        """Safely get value from object"""
        try:
            return obj.get(key, default) if isinstance(obj, dict) else getattr(obj, key, default)
        except (AttributeError, KeyError, TypeError):
            return default

    def _extract_jwt_tenant(self, request: Request) -> str:
        """Extract tenant ID from JWT token"""
        auth_header = request.headers.get("Authorization", "")
        if auth_header:
            try:
                token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else auth_header
                decoded = jwt.decode(token, options={"verify_signature": False})
                return decoded.get('custom:tenant_id')
            except:
                pass
        return None

    def _parse_user_agent(self, ua_string: str) -> tuple:
        """Parse user agent string to determine device type and browser"""
        ua_lower = ua_string.lower()
        
        # Device type
        if 'mobile' in ua_lower or 'android' in ua_lower or 'iphone' in ua_lower:
            device_type = 'mobile'
        elif 'tablet' in ua_lower or 'ipad' in ua_lower:
            device_type = 'tablet'
        else:
            device_type = 'desktop'
        
        # Browser
        if 'chrome' in ua_lower:
            browser = 'chrome'
        elif 'firefox' in ua_lower:
            browser = 'firefox'
        elif 'safari' in ua_lower:
            browser = 'safari'
        elif 'edge' in ua_lower:
            browser = 'edge'
        else:
            browser = 'other'
        
        return device_type, browser

    def _get_tenant_name(self, tenant_id: str) -> str:
        """Get human-readable tenant name"""
        tenant_names = {
            "b2c": "B2C Platform",
            "enterprise_001": "TechCorp Inc",
            "startup_123": "InnovateLab",
            "agency_456": "Digital Agency Pro"
        }
        return tenant_names.get(tenant_id, tenant_id)

    def _get_user_role(self, user_info: dict) -> str:
        """Determine user role from user info"""
        if user_info.get("custom:is_admin") == "true":
            return "admin"
        elif user_info.get("custom:free_user") == "true":
            return "free_user"
        else:
            return "user"

    def _determine_action_type(self, method: str, path: str) -> str:
        """Determine the action type based on HTTP method and path"""
        if method == 'GET':
            return 'list' if not path.split('/')[-1].replace('-', '').isalnum() else 'read'
        elif method == 'POST':
            return 'create'
        elif method in ['PUT', 'PATCH']:
            return 'update'
        elif method == 'DELETE':
            return 'delete'
        return 'unknown'

    def _extract_resource_info(self, path: str) -> str:
        """Extract resource type from API path"""
        parts = path.strip('/').split('/')
        if len(parts) >= 2 and parts[0] == 'api':
            return parts[1]  # users, projects, tasks, etc.
        return 'unknown'

    def _generate_tags(self, request: Request, response, user_info: dict) -> list:
        """Generate tags for categorization"""
        tags = []
        
        # Add method tag
        tags.append(f"method_{request.method.lower()}")
        
        # Add status category
        if response.status_code < 300:
            tags.append("success")
        elif response.status_code < 400:
            tags.append("redirect")
        elif response.status_code < 500:
            tags.append("client_error")
        else:
            tags.append("server_error")
        
        # Add resource type
        if "/api/users" in request.url.path:
            tags.append("user_management")
        elif "/api/projects" in request.url.path:
            tags.append("project_management")
        elif "/api/tasks" in request.url.path:
            tags.append("task_management")
        elif "/api/auth" in request.url.path:
            tags.append("authentication")
        
        # Add user type
        if user_info.get("custom:is_admin") == "true":
            tags.append("admin_user")
        else:
            tags.append("regular_user")
            
        return tags

    def _print_tracking_info(self, log: APITrackingLog):
        """Print formatted tracking information"""
        print("=" * 100)
        print(f"🚀 API TRACKING WITH LOCATION - {log.method} {log.endpoint}")
        print("=" * 100)
        print(f"📍 REQUEST: {log.method} {log.endpoint}")
        print(f"⏱️  DURATION: {log.duration_ms}ms")
        print(f"📊 STATUS: {log.status_code} ({log.response_type})")
        print(f"👤 USER: {log.user_email} ({log.user_role})")
        print(f"🏢 TENANT: {log.tenant_id} ({log.tenant_name})")
        print(f"🌐 CLIENT: {log.client_ip} ({log.device_type}/{log.browser})")
        print(f"📍 LOCATION: {log.country}, {log.region}, {log.city}")
        print(f"🏷️  TAGS: {', '.join(log.tags)}")
        print("=" * 100)