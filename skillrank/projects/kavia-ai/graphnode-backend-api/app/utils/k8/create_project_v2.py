import json
from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from app.utils.datetime_utils import generate_timestamp
from app.utils.aws.ssm_loader import load_ssm_dev_param, load_ssm_qa_param, load_ssm_pre_prod_param
from app.core.kubernetes_monitor import get_environment_and_namespace
from app.core.Settings import settings

def create_project_for_beta(project_id):
    project_id = str(project_id).lower()
    env_name = 'beta'
    action = 'create'
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            # Fall back to local kubeconfig
            try:
                config.load_kube_config()
            except config.ConfigException:
                # Fall back to kubeconfig from SSM
                kubeconfig = load_ssm_qa_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        raise
    
    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()
    core_v1 = client.CoreV1Api()

    # Define the job name and namespace
    job_name = f"codegen-{project_id}-{env_name}-{action}"
    namespace = "duploservices-kavia-beta"
    previous_create_job = f"codegen-{project_id}-{env_name}-create"
    hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
    
    # Function to extract pod ID from logs
    def extract_pod_id_from_logs(logs):
        # Pattern to match pod ID in format like "5824-dev-7d54b64dff-q6sjv"
        pod_pattern = r"Using pod: (\d+)-beta-[a-z0-9]+-[a-z0-9]+"
        match = re.search(pod_pattern, logs)
        
        if match:
            return match.group(1)  # Return just the numeric ID part
        return None

    # Function to check if a job exists
    def check_job_exists(job_name, namespace):
        try:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            return True, job
        except ApiException as e:
            if e.status == 404:
                return False, None
            raise e

    # Function to get logs from a job
    def get_job_logs(job_name, namespace):
        try:
            # Get pods associated with the job
            pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                label_selector=f"job-name={job_name}"
            )
            
            code_gen_pod_id = None
            job_logs = ""
            
            # Get logs from each pod and extract pod ID
            for pod in pods.items:
                pod_name = pod.metadata.name
                try:
                    logs = core_v1.read_namespaced_pod_log(
                        name=pod_name,
                        namespace=namespace
                    )
                    job_logs += logs
                    print(f"Logs from pod {pod_name}:")
                    print(logs)
                    
                    # Extract the pod ID from logs
                    temp_id = extract_pod_id_from_logs(logs)
                    if temp_id:
                        code_gen_pod_id = temp_id
                        print(f"Extracted code_gen_pod_id = {code_gen_pod_id}")
                    
                except Exception as e:
                    print(f"Error retrieving logs for pod {pod_name}: {e}")
            
            return code_gen_pod_id, job_logs
        except ApiException as e:
            print(f"Error getting job logs: {e}")
            return None, ""

    # Function to wait for job completion
    def wait_for_job_completion(job_name, namespace):
        while True:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            if job.status.succeeded:
                code_gen_pod_id, _ = get_job_logs(job_name, namespace)
                return code_gen_pod_id
            time.sleep(5)

    # Define the command based on the action
    if action == "create":
        servicename = f"internal-{project_id}-{env_name}"
        kubectl_command = f"""
        # Redis connection details
            REDIS_HOST="rediscodegen"
            REDIS_PORT=6379
            LOCK_KEY_PREFIX="pod_lock_"
            LOCK_VALUE="locked"
            LOCK_TIMEOUT=10  # seconds

            # Kubernetes namespace and label
            NAMESPACE="duploservices-kavia-beta"
            LABEL_SELECTOR="pod-status=available"
            STATUSES="Running ContainerCreating Pending"  # Correct syntax for /bin/sh

            acquire_lock() {{
                pod_name=$1
                lock_key="$LOCK_KEY_PREFIX${{pod_name}}"
                echo "Running: redis-cli -h $REDIS_HOST -p $REDIS_PORT SETNX $lock_key $LOCK_VALUE"

                # Capture Redis return value
                result=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" SETNX "$lock_key" "$LOCK_VALUE")

                if [ "$result" -eq 1 ]; then
                    # Optional: set expiration
                    # redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" EXPIRE "$lock_key" "$LOCK_TIMEOUT"
                    echo "Lock set"
                    return 0
                else
                    echo "Lock already exists"
                    return 1
                fi
            }}

            release_lock() {{
                pod_name=$1
                lock_key="$LOCK_KEY_PREFIX$pod_name"

                # Delete the lock (DEL command)
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" DEL "$lock_key" > /dev/null
            }}

        
            # Define statuses
            STATUSES="Running ContainerCreating Pending"
            for STATUS in $STATUSES; do
                POD_APP=$(kubectl get pods -n "$NAMESPACE" -l pod-status=available --sort-by=.metadata.creationTimestamp \
                -o jsonpath="{{range .items[?(@.status.phase=='${{STATUS}}')]}}{{.metadata.name}} {{.metadata.labels.app}}\\\\n{{end}}" | head -n 1 ) 

                if [ -n "$POD_APP" ]; then

                    # Extract pod name and app label from the selected pod
                    POD=$(echo "$POD_APP" | head -n1 | awk '{{print $1}}')
                    APP=$(echo "$POD_APP" | head -n1 | awk '{{print $2}}')

                    echo " Using pod: $POD"
                    echo " App label: $APP"
                    echo " Environment: $ENV_NAME"

                    if acquire_lock "$POD"; then
                        echo "Lock acquired: $POD"
                        # servicename = f"internal-{project_id}-{env_name}"
                        echo "Creating deployment for {job_name}..."
                        echo " Labeling pod '$POD' as used..."
                        kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
                        kubectl label deployment "$APP" -n "$NAMESPACE" pod-status=used --overwrite
            
                        echo " Starting ingress update script..."
                        CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
                        POD_NAME="nginx-0"
                        echo $NAMESPACE

                        SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.beta.cloud.kavia.ai"
                        PROXY_port1="http://internal-clusterip-${{APP}}:3000"
                        PROXY_port2="http://internal-clusterip-${{APP}}:3001"
                        PROXY_port3="http://internal-clusterip-${{APP}}:3002"
                        PROXY_port4="http://internal-clusterip-${{APP}}:3003"
                        PROXY_port5="http://internal-clusterip-${{APP}}:3004"
                        PROXY_port6="http://internal-clusterip-${{APP}}:3005"
                        PROXY_port7="http://internal-clusterip-${{APP}}:3006"
                        PROXY_port8="http://internal-clusterip-${{APP}}:3007"
                        PROXY_port9="http://internal-clusterip-${{APP}}:3008"
                        PROXY_port10="http://internal-clusterip-${{APP}}:3009"
                        PROXY_port11="http://internal-clusterip-${{APP}}:3010"
                        PROXY_port12="http://internal-clusterip-${{APP}}:5000"
                        PROXY_port13="http://internal-clusterip-${{APP}}:5001"
                        PROXY_port14="http://internal-clusterip-${{APP}}:5002"
                        PROXY_port15="http://internal-clusterip-${{APP}}:5003"
                        PROXY_port16="http://internal-clusterip-${{APP}}:5004"
                        PROXY_port17="http://internal-clusterip-${{APP}}:5005"
                        PROXY_port18="http://internal-clusterip-${{APP}}:5006"
                        PROXY_port19="http://internal-clusterip-${{APP}}:5007"
                        PROXY_port20="http://internal-clusterip-${{APP}}:5008"
                        PROXY_port21="http://internal-clusterip-${{APP}}:5009"
                        PROXY_port22="http://internal-clusterip-${{APP}}:5010"
                        PROXY_port23="http://internal-clusterip-${{APP}}:8000"
                        PROXY_port24="http://internal-clusterip-${{APP}}:8001"
                        PROXY_port25="http://internal-clusterip-${{APP}}:8002"
                        PROXY_port26="http://internal-clusterip-${{APP}}:8003"
                        PROXY_port27="http://internal-clusterip-${{APP}}:8004"
                        PROXY_port28="http://internal-clusterip-${{APP}}:8005"
                        PROXY_port29="http://internal-clusterip-${{APP}}:8006"
                        PROXY_port30="http://internal-clusterip-${{APP}}:8007"
                        PROXY_port31="http://internal-clusterip-${{APP}}:8008"
                        PROXY_port32="http://internal-clusterip-${{APP}}:8009"
                        PROXY_port33="http://internal-clusterip-${{APP}}:8010"
                        PROXY="http://internal-${{APP}}:8080"
                        echo " SERVER_NAME to be added: $SERVER_NAME"
                        echo " PROXY to be routed: $PROXY"
                        sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \
                        s|{{{{PROXY}}}}|${{PROXY}}|g; \
                        s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \
                        s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \
                        s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \
                        s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \
                        s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \
                        s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \
                        s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \
                        s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \
                        s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \
                        s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \
                        s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \
                        s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \
                        s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \
                        s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \
                        s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \
                        s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \
                        s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \
                        s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \
                        s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \
                        s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \
                        s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \
                        s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \
                        s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \
                        s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \
                        s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \
                        s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \
                        s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \
                        s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \
                        s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \
                        s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \
                        s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \
                        s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \
                        s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

                        echo " Created local config: $CONFIG_FILE"
                        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
                        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
                        echo " Copied config into pod: $POD_NAME"
                        kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
                        echo " Reloaded nginx in pod: $POD_NAME"
                        release_lock $POD
                        break
                    else
                        echo "Lock was not acquired. retrying.."
                        continue 1
                    fi
                fi
            done
        """
    elif action == "delete":
        kubectl_command = f"""
        echo "Deleting deployment for {job_name}..."
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        s/{{{{ENV_NAME}}}}/{env_name}/g; \
        s/{{{{hostname}}}}/{hostname}/g; \
        s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
        /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
            s/{{{{ENV_NAME}}}}/{env_name}/g" \
            /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
        kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
        kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {namespace} --ignore-not-found=true
        echo "Deleting previous job: {previous_create_job}..."
        kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
        echo "Cleanup completed!"
        """

    # Define the job manifest
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": "duploservices-kavia-beta-edit-user",
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "************.dkr.ecr.us-west-2.amazonaws.com/codegen-job:custom",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": env_name},
                                {"name": "NAMESPACE", "value": namespace},
                            ],
                            "volumeMounts": [
                                {"name": "codegenservicedeploymentbeta", "mountPath": "/app"},
                                {"name": "ingressservicebeta", "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": "codegenservicedeploymentbeta",
                            "configMap": {"name": "codegenservicedeploymentbeta"},
                        },
                        {
                            "name": "ingressservicebeta",
                            "configMap": {"name": "ingressservicebeta"},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    # Check if the job already exists
    job_exists, existing_job = check_job_exists(job_name, namespace)
    
    if job_exists:
        print(f"Job {job_name} already exists")
        
        # If job is already completed, get logs and extract pod ID
        if existing_job.status.succeeded:
            print(f"Job {job_name} is already completed")
            code_gen_pod_id, _ = get_job_logs(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
        else:
            # Job exists but not completed yet, wait for it
            print(f"Job {job_name} exists but not completed, waiting...")
            code_gen_pod_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
    
    # Create the job if it doesn't exist
    try:
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        code_gen_id = wait_for_job_completion(job_name, namespace)
        
        if action == "create":
            print(f"Ingress hostname: {hostname}")

        return code_gen_id
    
    except ApiException as e:
        # If job is created by another process while we're checking
        if e.status == 409:  # Conflict error - already exists
            print(f"Job {job_name} was created by another process, waiting for completion...")
            code_gen_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_id
        else:
            print(f"An error occurred: {e}")
            raise

def create_project_for_qa(project_id):
    # Initialize timing dictionary
    timing_data = {
        "total_time": 0,
        "sections": {
            "initialization": 0,
            "kubernetes_config": 0,
            "job_check": 0,
            "job_creation": 0,
            "job_wait": 0,
            "log_extraction": 0
        },
        "functions": {}
    }
    
    start_total = time.time()
    
    # Initialization section timing
    start_init = time.time()
    project_id = str(project_id).lower()
    env_name = 'qa'
    action = 'create'
    timing_data["sections"]["initialization"] = time.time() - start_init
    
    # Kubernetes configuration section timing
    start_kube_config = time.time()
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            # Fall back to local kubeconfig
            try:
                config.load_kube_config()
            except config.ConfigException:
                # Fall back to kubeconfig from SSM
                kubeconfig = load_ssm_qa_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        raise
    
    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()
    core_v1 = client.CoreV1Api()
    
    # Define the job name and namespace
    job_name = f"codegen-{project_id}-{env_name}-{action}"
    namespace = "duploservices-kavia-qa"
    previous_create_job = f"codegen-{project_id}-{env_name}-create"
    hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
    timing_data["sections"]["kubernetes_config"] = time.time() - start_kube_config
    
    # Function to extract pod ID from logs
    def extract_pod_id_from_logs(logs):
        start_time = time.time()
        # Pattern to match pod ID in format like "5824-dev-7d54b64dff-q6sjv"
        pod_pattern = r"Using pod: (\d+)-qa-[a-z0-9]+-[a-z0-9]+"
        match = re.search(pod_pattern, logs)
        
        result = None
        if match:
            result = match.group(1)  # Return just the numeric ID part
            
        elapsed = time.time() - start_time
        timing_data["functions"]["extract_pod_id_from_logs"] = elapsed
        return result

    # Function to check if a job exists
    def check_job_exists(job_name, namespace):
        start_time = time.time()
        try:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            result = (True, job)
        except ApiException as e:
            if e.status == 404:
                result = (False, None)
            else:
                raise e
                
        elapsed = time.time() - start_time
        timing_data["functions"]["check_job_exists"] = elapsed
        return result

    # Function to get logs from a job
    def get_job_logs(job_name, namespace):
        start_time = time.time()
        try:
            # Get pods associated with the job
            pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                label_selector=f"job-name={job_name}"
            )
            
            code_gen_pod_id = None
            job_logs = ""
            
            # Get logs from each pod and extract pod ID
            for pod in pods.items:
                pod_name = pod.metadata.name
                try:
                    logs = core_v1.read_namespaced_pod_log(
                        name=pod_name,
                        namespace=namespace
                    )
                    job_logs += logs
                    print(f"Logs from pod {pod_name}:")
                    print(logs)
                    
                    # Extract the pod ID from logs
                    temp_id = extract_pod_id_from_logs(logs)
                    if temp_id:
                        code_gen_pod_id = temp_id
                        print(f"Extracted code_gen_pod_id = {code_gen_pod_id}")
                    
                except Exception as e:
                    print(f"Error retrieving logs for pod {pod_name}: {e}")
            
            result = (code_gen_pod_id, job_logs)
        except ApiException as e:
            print(f"Error getting job logs: {e}")
            result = (None, "")
            
        elapsed = time.time() - start_time
        timing_data["functions"]["get_job_logs"] = elapsed
        return result

    # Function to wait for job completion
    def wait_for_job_completion(job_name, namespace):
        start_time = time.time()
        job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
        code_gen_pod_id, _ = get_job_logs(job_name, namespace)
        print("Code Gen Pod ID = ", code_gen_pod_id)
        elapsed = time.time() - start_time
        timing_data["functions"]["wait_for_job_completion"] = elapsed

        return code_gen_pod_id
            # if job.status.succeeded:
            #     elapsed = time.time() - start_time
            #     timing_data["functions"]["wait_for_job_completion"] = elapsed

            #     code_gen_pod_id, _ = get_job_logs(job_name, namespace)
            #     return code_gen_pod_id
            #time.sleep(0.1)
            
    # Define the command based on the action
    if action == "create":
        kubectl_command = f"""
            # Redis connection details
            REDIS_HOST="rediscodegen"
            REDIS_PORT=6379
            LOCK_KEY_PREFIX="pod_lock_"
            LOCK_VALUE="locked"
            LOCK_TIMEOUT=10  # seconds

            # Kubernetes namespace and label
            NAMESPACE="duploservices-kavia-qa"
            LABEL_SELECTOR="pod-status=available"
            STATUSES="Running ContainerCreating Pending"  # Correct syntax for /bin/sh

            acquire_lock() {{
                pod_name=$1
                lock_key="$LOCK_KEY_PREFIX${{pod_name}}"
                echo "Running: redis-cli -h $REDIS_HOST -p $REDIS_PORT SETNX $lock_key $LOCK_VALUE"

                # Capture Redis return value
                result=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" SETNX "$lock_key" "$LOCK_VALUE")

                if [ "$result" -eq 1 ]; then
                    # Optional: set expiration
                    # redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" EXPIRE "$lock_key" "$LOCK_TIMEOUT"
                    echo "Lock set"
                    return 0
                else
                    echo "Lock already exists"
                    return 1
                fi
            }}

            release_lock() {{
                pod_name=$1
                lock_key="$LOCK_KEY_PREFIX$pod_name"

                # Delete the lock (DEL command)
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" DEL "$lock_key" > /dev/null
            }}

        
            # Define statuses
            STATUSES="Running ContainerCreating Pending"
            for STATUS in $STATUSES; do
                POD_APP=$(kubectl get pods -n "$NAMESPACE" -l pod-status=available --sort-by=.metadata.creationTimestamp \
                -o jsonpath="{{range .items[?(@.status.phase=='${{STATUS}}')]}}{{.metadata.name}} {{.metadata.labels.app}}\\\\n{{end}}" | head -n 1 ) 

                if [ -n "$POD_APP" ]; then

                    # Extract pod name and app label from the selected pod
                    POD=$(echo "$POD_APP" | head -n1 | awk '{{print $1}}')
                    APP=$(echo "$POD_APP" | head -n1 | awk '{{print $2}}')

                    echo " Using pod: $POD"
                    echo " App label: $APP"
                    echo " Environment: $ENV_NAME"

                    if acquire_lock "$POD"; then
                        echo "Lock acquired: $POD"
                        # servicename = f"internal-{project_id}-{env_name}"
                        echo "Creating deployment for {job_name}..."
                        echo " Labeling pod '$POD' as used..."
                        kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
                        kubectl label deployment "$APP" -n "$NAMESPACE" pod-status=used --overwrite
            
                        echo " Starting ingress update script..."
                        CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
                        POD_NAME="nginx-0"
                        echo $NAMESPACE

                        SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.qa.cloud.kavia.ai"
                        PROXY_port1="http://internal-clusterip-${{APP}}:3000"
                        PROXY_port2="http://internal-clusterip-${{APP}}:3001"
                        PROXY_port3="http://internal-clusterip-${{APP}}:3002"
                        PROXY_port4="http://internal-clusterip-${{APP}}:3003"
                        PROXY_port5="http://internal-clusterip-${{APP}}:3004"
                        PROXY_port6="http://internal-clusterip-${{APP}}:3005"
                        PROXY_port7="http://internal-clusterip-${{APP}}:3006"
                        PROXY_port8="http://internal-clusterip-${{APP}}:3007"
                        PROXY_port9="http://internal-clusterip-${{APP}}:3008"
                        PROXY_port10="http://internal-clusterip-${{APP}}:3009"
                        PROXY_port11="http://internal-clusterip-${{APP}}:3010"
                        PROXY_port12="http://internal-clusterip-${{APP}}:5000"
                        PROXY_port13="http://internal-clusterip-${{APP}}:5001"
                        PROXY_port14="http://internal-clusterip-${{APP}}:5002"
                        PROXY_port15="http://internal-clusterip-${{APP}}:5003"
                        PROXY_port16="http://internal-clusterip-${{APP}}:5004"
                        PROXY_port17="http://internal-clusterip-${{APP}}:5005"
                        PROXY_port18="http://internal-clusterip-${{APP}}:5006"
                        PROXY_port19="http://internal-clusterip-${{APP}}:5007"
                        PROXY_port20="http://internal-clusterip-${{APP}}:5008"
                        PROXY_port21="http://internal-clusterip-${{APP}}:5009"
                        PROXY_port22="http://internal-clusterip-${{APP}}:5010"
                        PROXY_port23="http://internal-clusterip-${{APP}}:8000"
                        PROXY_port24="http://internal-clusterip-${{APP}}:8001"
                        PROXY_port25="http://internal-clusterip-${{APP}}:8002"
                        PROXY_port26="http://internal-clusterip-${{APP}}:8003"
                        PROXY_port27="http://internal-clusterip-${{APP}}:8004"
                        PROXY_port28="http://internal-clusterip-${{APP}}:8005"
                        PROXY_port29="http://internal-clusterip-${{APP}}:8006"
                        PROXY_port30="http://internal-clusterip-${{APP}}:8007"
                        PROXY_port31="http://internal-clusterip-${{APP}}:8008"
                        PROXY_port32="http://internal-clusterip-${{APP}}:8009"
                        PROXY_port33="http://internal-clusterip-${{APP}}:8010"
                        PROXY="http://internal-${{APP}}:8080"
                        echo " SERVER_NAME to be added: $SERVER_NAME"
                        echo " PROXY to be routed: $PROXY"
                        sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \
                        s|{{{{PROXY}}}}|${{PROXY}}|g; \
                        s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \
                        s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \
                        s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \
                        s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \
                        s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \
                        s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \
                        s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \
                        s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \
                        s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \
                        s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \
                        s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \
                        s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \
                        s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \
                        s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \
                        s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \
                        s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \
                        s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \
                        s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \
                        s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \
                        s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \
                        s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \
                        s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \
                        s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \
                        s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \
                        s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \
                        s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \
                        s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \
                        s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \
                        s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \
                        s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \
                        s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \
                        s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \
                        s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

                        echo " Created local config: $CONFIG_FILE"
                        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
                        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
                        echo " Copied config into pod: $POD_NAME"
                        kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
                        echo " Reloaded nginx in pod: $POD_NAME"
                        release_lock $POD
                        break
                    else
                        echo "Lock was not acquired. retrying.."
                        continue 1
                    fi
                fi
            done
        """
    elif action == "delete":
        kubectl_command = f"""
        echo "Deleting deployment for {job_name}..."
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        s/{{{{ENV_NAME}}}}/{env_name}/g; \
        s/{{{{hostname}}}}/{hostname}/g; \
        s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
        /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
            s/{{{{ENV_NAME}}}}/{env_name}/g" \
            /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
        kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
        kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {namespace} --ignore-not-found=true
        echo "Deleting previous job: {previous_create_job}..."
        kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
        echo "Cleanup completed!"
        """

    # Define the job manifest
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": "duploservices-kavia-qa-edit-user",
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "************.dkr.ecr.us-west-2.amazonaws.com/codegen-job:custom",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": env_name},
                                {"name": "NAMESPACE", "value": namespace},
                            ],
                            "volumeMounts": [
                                {"name": "codegenserviceqa1", "mountPath": "/app"},
                                {"name": "ingressserviceqa", "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": "codegenserviceqa1",
                            "configMap": {"name": "codegenserviceqa1"},
                        },
                        {
                            "name": "ingressserviceqa",
                            "configMap": {"name": "ingressserviceqa"},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    start_job_check = time.time()
    job_exists, existing_job = check_job_exists(job_name, namespace)
    timing_data["sections"]["job_check"] = time.time() - start_job_check
    
    if job_exists:
        print(f"Job {job_name} already exists")
        
        # If job is already completed, get logs and extract pod ID
        if existing_job.status.succeeded:
            print(f"Job {job_name} is already completed")
            start_log_extraction = time.time()
            code_gen_pod_id, _ = get_job_logs(job_name, namespace)
            timing_data["sections"]["log_extraction"] = time.time() - start_log_extraction
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            timing_data["total_time"] = time.time() - start_total
            print(f"Timing data: {json.dumps(timing_data, indent=2)}")
            return code_gen_pod_id
        else:
            # Job exists but not completed yet, wait for it
            print(f"Job {job_name} exists but not completed, waiting...")
            start_job_wait = time.time()
            code_gen_pod_id = wait_for_job_completion(job_name, namespace)
            timing_data["sections"]["job_wait"] = time.time() - start_job_wait
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
            
            timing_data["total_time"] = time.time() - start_total
            print(f"Timing data: {json.dumps(timing_data, indent=2)}")
            return code_gen_pod_id
    
    # Create the job if it doesn't exist - timing the job creation section
    start_job_creation = time.time()
    try:
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        timing_data["sections"]["job_creation"] = time.time() - start_job_creation
        
        start_job_wait = time.time()
        code_gen_id = wait_for_job_completion(job_name, namespace)
        timing_data["sections"]["job_wait"] = time.time() - start_job_wait
        
        if action == "create":
            print(f"Ingress hostname: {hostname}")

        timing_data["total_time"] = time.time() - start_total
        print(f"Timing data: {json.dumps(timing_data, indent=2)}")
        return code_gen_id
    
    except ApiException as e:
        # If job is created by another process while we're checking
        if e.status == 409:  # Conflict error - already exists
            print(f"Job {job_name} was created by another process, waiting for completion...")
            timing_data["sections"]["job_creation"] = time.time() - start_job_creation
            
            start_job_wait = time.time()
            code_gen_id = wait_for_job_completion(job_name, namespace)
            timing_data["sections"]["job_wait"] = time.time() - start_job_wait
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
            
            timing_data["total_time"] = time.time() - start_total
            print(f"Timing data: {json.dumps(timing_data, indent=2)}")
            return code_gen_id
        else:
            print(f"An error occurred: {e}")
            timing_data["total_time"] = time.time() - start_total
            print(f"Timing data: {json.dumps(timing_data, indent=2)}")
            raise
        
def create_project_for_dev(project_id):
    project_id = str(project_id).lower()
    env_name = 'dev'
    action = 'create'

    # Load kubeconfig from SSM Parameter Store

    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            # Fall back to local kubeconfig
            try:
                config.load_kube_config()
            except config.ConfigException:
                # Fall back to kubeconfig from SSM
                kubeconfig = load_ssm_dev_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        raise
    
    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()
    core_v1 = client.CoreV1Api()

    # Define the job name and namespace
    job_name = f"codegen-{project_id}-{env_name}-{action}"
    namespace = "duploservices-kavia-dev"
    previous_create_job = f"codegen-{project_id}-{env_name}-create"
    hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
    
    # Function to extract pod ID from logs
    def extract_pod_id_from_logs(logs):
        # Pattern to match pod ID in format like "5824-dev-7d54b64dff-q6sjv"
        pod_pattern = r"Using pod: (\d+)-dev-[a-z0-9]+-[a-z0-9]+"
        match = re.search(pod_pattern, logs)
        
        if match:
            return match.group(1)  # Return just the numeric ID part
        return None

    # Function to check if a job exists
    def check_job_exists(job_name, namespace):
        try:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            return True, job
        except ApiException as e:
            if e.status == 404:
                return False, None
            raise e

    # Function to get logs from a job
    def get_job_logs(job_name, namespace):
        try:
            # Get pods associated with the job
            pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                label_selector=f"job-name={job_name}"
            )
            
            code_gen_pod_id = None
            job_logs = ""
            
            # Get logs from each pod and extract pod ID
            for pod in pods.items:
                pod_name = pod.metadata.name
                try:
                    logs = core_v1.read_namespaced_pod_log(
                        name=pod_name,
                        namespace=namespace
                    )
                    job_logs += logs
                    print(f"Logs from pod {pod_name}:")
                    print(logs)
                    
                    # Extract the pod ID from logs
                    temp_id = extract_pod_id_from_logs(logs)
                    if temp_id:
                        code_gen_pod_id = temp_id
                        print(f"Extracted code_gen_pod_id = {code_gen_pod_id}")
                    
                except Exception as e:
                    print(f"Error retrieving logs for pod {pod_name}: {e}")
            
            return code_gen_pod_id, job_logs
        except ApiException as e:
            print(f"Error getting job logs: {e}")
            return None, ""

    # Function to wait for job completion
    def wait_for_job_completion(job_name, namespace):
        while True:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            if job.status.succeeded:
                code_gen_pod_id, _ = get_job_logs(job_name, namespace)
                return code_gen_pod_id
            time.sleep(0.1)

    # Define the command based on the action
    if action == "create":
        servicename = f"internal-{project_id}-{env_name}"
        kubectl_command = f"""
        echo "Creating deployment for {job_name}..."
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        # s/{{{{ENV_NAME}}}}/{env_name}/g; \
        # s/{{{{hostname}}}}/{hostname}/g; \
        # s/{{{{servicename}}}}/{servicename}/g" \
        # /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        #     s/{{{{ENV_NAME}}}}/{env_name}/g" \
        #     /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #sleep 99999
        #kubectl apply -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #kubectl apply -f /tmp/codegenservice-{project_id}-{env_name}.yaml
        #kubectl apply -f /tmp/ingress-patch-{project_id}-{env_name}.yaml
        #!/bin/sh
        echo " Starting ingress update script..."
        CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
        POD_NAME="nginx-0"
        echo $NAMESPACE
        echo " Environment: $ENV_NAME"
        echo " Finding oldest pod with label 'pod-status=available'..."
        POD_APP=`kubectl get pods -n "$NAMESPACE" -l pod-status=available \\
        --sort-by=.metadata.creationTimestamp \\
        -o jsonpath="{{.items[0].metadata.name}} {{.items[0].metadata.labels.app}}"`
        POD=`echo "$POD_APP" | awk '{{print $1}}'`
        APP=`echo "$POD_APP" | awk '{{print $2}}'`
        echo " Using pod: $POD"
        echo " App label: $APP"
        SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.dev-vscode.cloud.kavia.ai"
        PROXY_port1="http://internal-clusterip-${{APP}}:3000"
        PROXY_port2="http://internal-clusterip-${{APP}}:3001"
        PROXY_port3="http://internal-clusterip-${{APP}}:3002"
        PROXY_port4="http://internal-clusterip-${{APP}}:3003"
        PROXY_port5="http://internal-clusterip-${{APP}}:3004"
        PROXY_port6="http://internal-clusterip-${{APP}}:3005"
        PROXY_port7="http://internal-clusterip-${{APP}}:3006"
        PROXY_port8="http://internal-clusterip-${{APP}}:3007"
        PROXY_port9="http://internal-clusterip-${{APP}}:3008"
        PROXY_port10="http://internal-clusterip-${{APP}}:3009"
        PROXY_port11="http://internal-clusterip-${{APP}}:3010"
        PROXY_port12="http://internal-clusterip-${{APP}}:5000"
        PROXY_port13="http://internal-clusterip-${{APP}}:5001"
        PROXY_port14="http://internal-clusterip-${{APP}}:5002"
        PROXY_port15="http://internal-clusterip-${{APP}}:5003"
        PROXY_port16="http://internal-clusterip-${{APP}}:5004"
        PROXY_port17="http://internal-clusterip-${{APP}}:5005"
        PROXY_port18="http://internal-clusterip-${{APP}}:5006"
        PROXY_port19="http://internal-clusterip-${{APP}}:5007"
        PROXY_port20="http://internal-clusterip-${{APP}}:5008"
        PROXY_port21="http://internal-clusterip-${{APP}}:5009"
        PROXY_port22="http://internal-clusterip-${{APP}}:5010"
        PROXY_port23="http://internal-clusterip-${{APP}}:8000"
        PROXY_port24="http://internal-clusterip-${{APP}}:8001"
        PROXY_port25="http://internal-clusterip-${{APP}}:8002"
        PROXY_port26="http://internal-clusterip-${{APP}}:8003"
        PROXY_port27="http://internal-clusterip-${{APP}}:8004"
        PROXY_port28="http://internal-clusterip-${{APP}}:8005"
        PROXY_port29="http://internal-clusterip-${{APP}}:8006"
        PROXY_port30="http://internal-clusterip-${{APP}}:8007"
        PROXY_port31="http://internal-clusterip-${{APP}}:8008"
        PROXY_port32="http://internal-clusterip-${{APP}}:8009"
        PROXY_port33="http://internal-clusterip-${{APP}}:8010"    
        PROXY="http://internal-${{APP}}:8080"
        echo " SERVER_NAME to be added: $SERVER_NAME"
        echo " PROXY to be routed: $PROXY"
        sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \
        s|{{{{PROXY}}}}|${{PROXY}}|g; \
        s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \
        s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \
        s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \
        s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \
        s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \
        s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \
        s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \
        s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \
        s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \
        s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \
        s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \
        s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \
        s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \
        s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \
        s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \
        s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \
        s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \
        s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \
        s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \
        s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \
        s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \
        s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \
        s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \
        s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \
        s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \
        s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \
        s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \
        s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \
        s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \
        s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \
        s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \
        s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \
        s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

        echo " Created local config: $CONFIG_FILE"

        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
        echo " Copied config into pod: $POD_NAME"
        kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
        echo " Reloaded nginx in pod: $POD_NAME"
        echo " Labeling pod '$POD' as used..."
        kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
        """
    elif action == "delete":
        kubectl_command = f"""
        echo "Deleting deployment for {job_name}..."
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        s/{{{{ENV_NAME}}}}/{env_name}/g; \
        s/{{{{hostname}}}}/{hostname}/g; \
        s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
        /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
            s/{{{{ENV_NAME}}}}/{env_name}/g" \
            /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
        kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
        kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {namespace} --ignore-not-found=true
        echo "Deleting previous job: {previous_create_job}..."
        kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
        echo "Cleanup completed!"
        """

    # Define the job manifest
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": "duploservices-kavia-dev-edit-user",
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "************.dkr.ecr.us-west-2.amazonaws.com/codegen-job:custom",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": env_name},
                                {"name": "NAMESPACE", "value": namespace},
                            ],
                            "volumeMounts": [
                                {"name": "codegenservicedeployment4", "mountPath": "/app"},
                                {"name": "ingressservice1", "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": "codegenservicedeployment4",
                            "configMap": {"name": "codegenservicedeployment4"},
                        },
                        {
                            "name": "ingressservice1",
                            "configMap": {"name": "ingressservice1"},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    # Check if the job already exists
    job_exists, existing_job = check_job_exists(job_name, namespace)
    
    if job_exists:
        print(f"Job {job_name} already exists")
        
        # If job is already completed, get logs and extract pod ID
        if existing_job.status.succeeded:
            print(f"Job {job_name} is already completed")
            code_gen_pod_id, _ = get_job_logs(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
        else:
            # Job exists but not completed yet, wait for it
            print(f"Job {job_name} exists but not completed, waiting...")
            code_gen_pod_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
    
    # Create the job if it doesn't exist
    try:
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        code_gen_id = wait_for_job_completion(job_name, namespace)
        
        if action == "create":
            print(f"Ingress hostname: {hostname}")

        return code_gen_id
    
    except ApiException as e:
        # If job is created by another process while we're checking
        if e.status == 409:  # Conflict error - already exists
            print(f"Job {job_name} was created by another process, waiting for completion...")
            code_gen_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_id
        else:
            print(f"An error occurred: {e}")
            raise
  
  
class Environment(Enum):
    """Supported environments"""
    DEV = "dev"
    QA = "qa"
    BETA = "beta"


@dataclass
class EnvironmentConfig:
    """Environment-specific configuration"""
    name: str
    namespace: str
    service_account: str
    configmap_volumes: Dict[str, str]
    domain_suffix: str
    ssm_loader_func: callable
    uses_redis_locking: bool = True
    pod_status_namespace: str = None

    def __post_init__(self):
        """Set pod_status_namespace if not provided"""
        if not self.pod_status_namespace:
            self.pod_status_namespace = f"duploservices-k-{self.name}01"
            
            
def get_environment_config(environment: str) -> EnvironmentConfig:
    """Get environment configuration for the specified environment"""
    configs = {
        Environment.DEV.value: EnvironmentConfig(
            name="dev",
            namespace="duploservices-kavia-dev",
            service_account="duploservices-kavia-dev-edit-user",
            configmap_volumes={
                "codegenservicedeployment4": "/app",
                "ingressservice1": "/app/ingress",
                "codegenpvc": "/app/pvc",
                "nginx": "/app/nginx"
            },
            domain_suffix="dev-vscode.cloud.kavia.ai",
            ssm_loader_func=load_ssm_dev_param,
            uses_redis_locking=False,  # DEV doesn't use Redis locking
            pod_status_namespace="duploservices-k-dev01"
        ),
        Environment.QA.value: EnvironmentConfig(
            name="qa",
            namespace="duploservices-kavia-qa",
            service_account="duploservices-kavia-qa-edit-user",
            configmap_volumes={
                "codegenserviceqa1": "/app",
                "ingressserviceqa": "/app/ingress",
                "codegenpvc": "/app/pvc",
                "nginx": "/app/nginx"
            },
            domain_suffix="qa.cloud.kavia.ai",
            ssm_loader_func=load_ssm_qa_param,
            uses_redis_locking=True,
            pod_status_namespace="duploservices-k-qa01"
        ),
        Environment.BETA.value: EnvironmentConfig(
            name="beta",
            namespace="duploservices-kavia-beta",
            service_account="duploservices-kavia-beta-edit-user",
            configmap_volumes={
                "codegenservicedeploymentbeta": "/app",
                "ingressservicebeta": "/app/ingress",
                "codegenpvc": "/app/pvc",
                "nginx": "/app/nginx"
            },
            domain_suffix="beta.cloud.kavia.ai",
            ssm_loader_func=load_ssm_qa_param,  # Beta uses QA SSM
            uses_redis_locking=True,
            pod_status_namespace="duploservices-k-beta01"
        )
    }
    
    if environment not in configs:
        raise ValueError(f"Unsupported environment: {environment}")
    
    return configs[environment]

        

def change_to_used_for_dev(project_id):
    """
    Update the ConfigMap to mark a pod as used, using the Kubernetes Python API.
    
    Args:
        project_id: The ID of the project (e.g., "02")
        pod_name: The name of the pod (e.g., "02-qa-5f865b5c7d-pg2qp")
    
    Returns:
        The pod name if successful, None otherwise
    """
    
    project_id = str(project_id).lower()
    namespace = "duploservices-k-dev01"
    configmap_name = f"pod-status-{project_id}-dev"
    
    # Initialize Kubernetes client
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            try:
                config.load_kube_config()
            except config.ConfigException:
                kubeconfig = load_ssm_dev_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        print(f"Error initializing Kubernetes config: {e}")
        raise
    
    # Create Kubernetes API client
    core_v1 = client.CoreV1Api()
    
    print(f"ConfigMap name: {configmap_name}")
    
    # Make the update directly instead of in a background thread
    try:
        # Create the strategic merge patch
        patch_body = {
            "data": {
                "pod-status": "used"
            }
        }
        
        # Print debug info
        print(f"Attempting to patch ConfigMap {configmap_name} in namespace {namespace}")
        
        # Patch the ConfigMap with strategic merge
        result = core_v1.patch_namespaced_config_map(
            name=configmap_name,
            namespace=namespace,
            body=patch_body
        )
        
        print(f"ConfigMap {configmap_name} updated successfully")
    except client.exceptions.ApiException as e:
        print(f"Error updating ConfigMap: API error code {e.status}, reason: {e.reason}")
        print(f"API response body: {e.body}")
        return None
    except Exception as e:
        print(f"Unexpected error updating ConfigMap: {str(e)}")
        return None

def change_to_used_for_beta(project_id):
    """
    Update the ConfigMap to mark a pod as used, using the Kubernetes Python API.
    
    Args:
        project_id: The ID of the project (e.g., "02")
        pod_name: The name of the pod (e.g., "02-qa-5f865b5c7d-pg2qp")
    
    Returns:
        The pod name if successful, None otherwise
    """
    
    project_id = str(project_id).lower()
    namespace = "duploservices-k-beta01"
    configmap_name = f"pod-status-{project_id}-beta"
    
    # Initialize Kubernetes client
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            try:
                config.load_kube_config()
            except config.ConfigException:
                kubeconfig = load_ssm_dev_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        print(f"Error initializing Kubernetes config: {e}")
        raise
    
    # Create Kubernetes API client
    core_v1 = client.CoreV1Api()
    
    print(f"ConfigMap name: {configmap_name}")
    
    # Make the update directly instead of in a background thread
    try:
        # Create the strategic merge patch
        patch_body = {
            "data": {
                "pod-status": "used"
            }
        }
        
        # Print debug info
        print(f"Attempting to patch ConfigMap {configmap_name} in namespace {namespace}")
        
        # Patch the ConfigMap with strategic merge
        result = core_v1.patch_namespaced_config_map(
            name=configmap_name,
            namespace=namespace,
            body=patch_body
        )
        
        print(f"ConfigMap {configmap_name} updated successfully")
    except client.exceptions.ApiException as e:
        print(f"Error updating ConfigMap: API error code {e.status}, reason: {e.reason}")
        print(f"API response body: {e.body}")
        return None
    except Exception as e:
        print(f"Unexpected error updating ConfigMap: {str(e)}")
        return None
    

def change_to_used_for_qa(project_id):
    """
    Update the ConfigMap to mark a pod as used, using the Kubernetes Python API.
    
    Args:
        project_id: The ID of the project (e.g., "02")
        pod_name: The name of the pod (e.g., "02-qa-5f865b5c7d-pg2qp")
    
    Returns:
        The pod name if successful, None otherwise
    """
    
    project_id = str(project_id).lower()
    namespace = "duploservices-k-qa01"
    configmap_name = f"pod-status-{project_id}-qa"
    
    # Initialize Kubernetes client
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            try:
                config.load_kube_config()
            except config.ConfigException:
                kubeconfig = load_ssm_dev_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        print(f"Error initializing Kubernetes config: {e}")
        raise
    
    # Create Kubernetes API client
    core_v1 = client.CoreV1Api()
    
    print(f"ConfigMap name: {configmap_name}")
    
    # Make the update directly instead of in a background thread
    try:
        # Create the strategic merge patch
        patch_body = {
            "data": {
                "pod-status": "used"
            }
        }
        
        # Print debug info
        print(f"Attempting to patch ConfigMap {configmap_name} in namespace {namespace}")
        
        # Patch the ConfigMap with strategic merge
        result = core_v1.patch_namespaced_config_map(
            name=configmap_name,
            namespace=namespace,
            body=patch_body
        )
        
        print(f"ConfigMap {configmap_name} updated successfully")
    except client.exceptions.ApiException as e:
        print(f"Error updating ConfigMap: API error code {e.status}, reason: {e.reason}")
        print(f"API response body: {e.body}")
        return None
    except Exception as e:
        print(f"Unexpected error updating ConfigMap: {str(e)}")
        return None
    
def _init_kubernetes_clients(env_config: EnvironmentConfig, use_ssm_loader= False) -> Tuple[client.BatchV1Api, client.CoreV1Api]:
    """Initialize Kubernetes clients with proper configuration"""
    try:
        try:
            if use_ssm_loader:
                kubeconfig = env_config.ssm_loader_func()
                config.load_kube_config_from_dict(kubeconfig)
            else:
                config.load_incluster_config()
        except config.ConfigException:
            try:
                config.load_kube_config()
            except config.ConfigException:
                kubeconfig = env_config.ssm_loader_func()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        raise
    
    return client.BatchV1Api(), client.CoreV1Api()

def change_to_used_unified(
    project_id: str,
    environment: str,
    session_id: Optional[str] = None,
    tenant_id: Optional[str] = None
) -> bool:
    """
    Unified function to update ConfigMap to mark a pod as used across all environments.
    
    Args:
        project_id: The ID of the project (e.g., "02")
        environment: Environment name ('dev', 'qa', 'beta')
        session_id: Optional session ID for tracking
        tenant_id: Optional tenant ID for tracking
    
    Returns:
        True if successful, False otherwise
    """
    project_id = str(project_id).lower()
    env_config = get_environment_config(environment)
    configmap_name = f"pod-status-{project_id}-{environment}"
    
    # Initialize Kubernetes client
    try:
        _, core_v1 = _init_kubernetes_clients(env_config, settings.LOCAL_KUBERNETES_DEBUG)
    except Exception as e:
        print(f"Error initializing Kubernetes config: {e}")
        raise
    
    print(f"ConfigMap name: {configmap_name}")
    
    try:
        # Create the strategic merge patch with session tracking
        patch_data = {
            "pod-status": "used"
        }
        
        # Add session tracking information if provided
        if session_id:
            patch_data["session_id"] = session_id
        if tenant_id:
            patch_data["tenant_id"] = tenant_id
        if session_id or tenant_id:
            patch_data["assigned_date"] = generate_timestamp()
            patch_data["environment"] = environment
        
        patch_body = {"data": patch_data}
        
        # Print debug info
        print(f"Attempting to patch ConfigMap {configmap_name} in namespace {env_config.pod_status_namespace}")
        
        # Patch the ConfigMap with strategic merge
        result = core_v1.patch_namespaced_config_map(
            name=configmap_name,
            namespace=env_config.pod_status_namespace,
            body=patch_body
        )
        
        print(f"ConfigMap {configmap_name} updated successfully with session tracking")
        return True
        
    except ApiException as e:
        print(f"Error updating ConfigMap: API error code {e.status}, reason: {e.reason}")
        print(f"API response body: {e.body}")
        return False
    except Exception as e:
        print(f"Unexpected error updating ConfigMap: {str(e)}")
        return False