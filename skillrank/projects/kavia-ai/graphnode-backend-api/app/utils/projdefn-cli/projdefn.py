import asyncio
import logging
import hashlib
import json
import os
import re
import threading
import time
import fitz
from abc import ABC, abstractmethod
from typing import Dict, Optional
from app.telemetry.logger_config import get_logger


_lock = threading.Lock()

_default_criteria = {
            "description": "- high level descriptions of project",
            "purpose_and_responsibilies": "- the main objectives and functionalities of the project",
            "functional_requirements": "- functional requirements",
            "non_functional_requirements": "- non-functional requirements affecting system performance, scalability, and security",
            "standards_compliance": "- standards compliance requirements",
            "regulatory_compliance": "- regulatory compliance requirements",
            "security_requirements": "- security requirements",
            "subsystem_descriptions": "- detailed descriptions of subsystems or features",
            "component_interactions": "- details of how system components interact with users and external systems",
            "behavior_descriptions": "- details of behavioural responsibilities of key system interactions and functionalities",
            "data_management": "- data storage requirements and data flow between components",
            "error_handling": "- potential error scenarios and strategies for error handling",
            "test_considerations": "- important testing aspects and areas to focus on for testing",
            "input_data":  "- descriptions of required input data",
            "output_data": "- descriptions of desired output data",
            "dependencies": "- list the project's dependencies essential for functionality",
            "code_languages": "- implementation programming languages to be used",
            "frameworks": "- implementation frameworks to be used",
            "algorithns": "- algorithms to use",
            "code_fragments": "- sample code supplied for normative or explanatory purposes",
}


class ProjDefnReporter(ABC):
    def __init__(self):
        self.update_logger = get_logger(__name__)

    @abstractmethod
    def send_agent_message(self, message):
        self.update_logger.info(f"Agent message: {message}")
        pass

    @abstractmethod
    def cost_update_callback(self, all_costs, total_cost ):
        self.update_logger.info(f"Cost update - Total: {total_cost}, All costs: {all_costs}")
        pass

class ProjDefnHelpers(ABC):
    def __init__(self):
        self.update_logger = get_logger(__name__)

    @abstractmethod
    def execute_cmd(self, cmd):
        self.update_logger.info(f"Executing command: {cmd}")
        pass

    @abstractmethod
    def read_file(self, filename):
        self.update_logger.info(f"Reading file: {filename}")
        pass

    @abstractmethod
    def write_file(self, filename, content):
        self.update_logger.info(f"Writing file: {filename}")
        pass

    @abstractmethod
    def list_directory(self, directory):
        self.update_logger.info(f"Listing directory: {directory}")
        pass


class ProjDefnDocPage:
    def __init__(self, number:int, text:str):
        self.page_number = number
        self.page_text = text

class ProjDefnDocSpecifier:
    def __init__(self, pathname, name = None):
        self.name = name
        self.pathname = pathname

class ProjDefnDoc:
    def __init__(self, spec:ProjDefnDocSpecifier):
        self.spec = spec
        self.hash = None
        self.title = "Untitled"
        self.pages = []
        self.chunks = []
        self.doc_info = {}

    def add_page(self, page:ProjDefnDocPage):
        self.pages.append(page)

    def add_chunk(self, page:ProjDefnDocPage):
        self.chunks.append(page)
    
    def page_count(self):
        return len(self.pages)

    def chunk_count(self):
        return len(self.chunks)
    
    def get_chunks(self):
        return self.chunks

    def set_title(self, title:str):
        self.title = title

    def get_title(self) -> str:
        return self.title

    def add_info(self, key:str, value:str):
        self.doc_info[key] = value

    def get_info(self, key:str) -> str:
        info = self.doc_info.get(key)
        return info
    
    def get_doc_info(self) -> dict:
        return self.doc_info
    
    def get_doc_spec(self) -> ProjDefnDocSpecifier:
        return self.spec
    
    def set_doc_hash(self, hash):
        self.hash = hash

    def get_doc_hash(self):
        return self.hash

class ProjDefn:

    _instances = {}

    def __init__(self, id:str, config:dict, session_id: str = None):
        self.id = id
        self.config = config
        self.session_id = session_id  # Store session ID
        self.base_path = config['base_path']
        self.model = config['model']
        self.chunk_size = config['chunk_size']
        self.timeout = config['timeout']
        self.reporter = config['reporter']
        self.helpers = config['helpers']
        self.cost_tracer = config['cost_tracer']
        self._lock = threading.Lock()
        self._lockConsolidation = threading.Lock()
        self.loop_worker = None
        self.thread_worker = None
        self.stop_worker = False
        self.worker_stopped = False
        self._defnLocked = False
        self._ingest_queue = []
        self._defn_docs = {}
        self._need_refresh = False
        self._full_refresh = False
        self._project_definition = {}
        self.logger = self._setup_logger("projdefn", self.base_path)

        # Use session ID in logger name if provided
        logger_name = f"projdefn_{session_id}" if session_id else "projdefn"
        self.logger = self._setup_logger(logger_name, self.base_path)

        if 'documents' in config:
            documents = config['documents']
            for doc in documents:
                self.addToIngestQueue(doc.name, doc.pathname)
        self.criteria = _default_criteria
        if 'criteria' in config:
            self.criteria = config['criteria']

    def _get_session_path(self):
        """Get path for session-specific storage"""
        if self.session_id:
            return os.path.join(self.base_path, f'.projdefn_{self.session_id}')
        return os.path.join(self.base_path, '.projdefn')
    
    def _setup_logger(self, name, base_path, log_level=logging.INFO):
        """
        Set up a logger with DataDog integration
        """
        logger = get_logger(name)
        logger.setLevel(log_level)

        # Create logs directory if it doesn't exist
        log_dir = os.path.join(base_path, "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
        file_handler.setLevel(log_level)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.propagate = False

        return logger

    def _get_file_hash(self, filename):
        hash = None
        content = self.helpers.read_file(filename)
        if content:
            encoded_content = content
            if not isinstance(content,bytes):
                try:
                    encoded_content = content.encode("utf-8")
                except UnicodeEncodeError:
                    encoded_content = content
            hash = hashlib.md5(encoded_content).hexdigest()
        return hash

    def _add_definitions(self,doc:ProjDefnDoc, can_refresh:bool = False):
        doc_spec = doc.get_doc_spec()
        name = doc_spec.name
        if name in self._defn_docs:
            self._defn_docs.pop(name)
            self._need_refresh = True
        elif can_refresh:
            self._need_refresh = True
        self._defn_docs[name] = doc

    def _persist_project_definition(self):
        self.logger.info(f"persist master project definition data for session {self.session_id}")
        base_path = self.base_path
        with self._lock:
            # folder = os.path.join(base_path, '.projdefn')
            folder = self._get_session_path()
            persistent_filename = os.path.join( folder, 'project_definition.json')
            content = json.dumps(self._project_definition)
            self.helpers.write_file( persistent_filename, content)

    def _persist_document_definition(self, doc:ProjDefnDoc):
        defn_info = {}
        doc_spec = doc.get_doc_spec()
        defn_info['name'] = doc_spec.name
        defn_info['pathname'] = doc_spec.pathname
        defn_info['title'] = doc.get_title()
        defn_info['hash'] = doc.get_doc_hash()
        defn_info['doc_info'] = doc.get_doc_info()
        self.logger.info(f"persist ingested definition data for {doc.get_doc_spec().name} in session {self.session_id}")
        base_path = self.base_path
        with self._lock:
            # folder = os.path.join(base_path, '.projdefn')
            folder = self._get_session_path()
            persistent_filename = os.path.join( folder, 'doc__'+doc_spec.name+'.json')
            content = json.dumps(defn_info)
            self.helpers.write_file( persistent_filename, content)

    def _unpersist_document_definition(self, doc:ProjDefnDoc):
        doc_spec = doc.get_doc_spec()
        base_path = self.base_path
        # folder = os.path.join(base_path, '.projdefn')
        folder = self._get_session_path()
        persistent_filename = os.path.join( folder, 'doc__'+doc_spec.name+'.json')
        os.remove(persistent_filename)

    def _load_persisted_project_definition(self):
        # folder_name = os.path.join(self.base_path,".projdefn")
        folder_name = self._get_session_path()
        files = self.helpers.list_directory(folder_name)
        for file in files:
            if file.startswith('.'):
                continue
            if file.endswith('.json'):
                self.logger.info(f"loading persisted definitions ({os.path.basename(file)})")
                try:
                    data = self.helpers.read_file(os.path.join(folder_name,file))
                    defn_info = json.loads(data)
                    if file == 'project_definition.json':
                        self._project_definition = defn_info
                    elif file.startswith('doc__'):
                        name = defn_info['name']
                        pathname = defn_info['pathname']
                        title = defn_info.get('title')
                        hash = defn_info.get('hash')
                        doc_info = defn_info.get('doc_info')
                        doc_spec = ProjDefnDocSpecifier(pathname,name)
                        doc = ProjDefnDoc(doc_spec)
                        doc.set_title(title)
                        doc.set_doc_hash(hash)
                        for key,value in doc_info.items():
                            doc.add_info( key, value)
                        self._add_definitions(doc)

                        if 'hash' in defn_info:
                            hash = self._get_file_hash(pathname)
                            if hash and hash != defn_info['hash']:
                                self.logger.info(f'modified document ({name}) detected: hash mismatch')
                                self.addToIngestQueue(doc_spec)
                except Exception as e:
                    self.logger.error(f"_load_peristed_project_definition had exception {str(e)} for file {file}")
                    pass
                self.logger.info(f"done loading persisted definitions ({os.path.basename(file)})")
        if not self._project_definition:
            self._need_refresh = True

    def _examine_document(self, doc:ProjDefnDoc):
        import litellm
        title = doc.get_title()
        self.logger.info(f"start examination of ({title})")
        doc_info = None
        criteria_list = self.criteria
        for criteria_key,criteria_value in criteria_list.items():
            self.logger.info(f"examine doc ({title}) for criteria \'{criteria_key}\'")
            chunk_num = 0
            chunks = doc.get_chunks()
            num_chunks = len(chunks)
            for chunk in chunks:
                chunk_num += 1
                model_name = self.model
                system_prompt = "You are an expert software architect."
                user_prompt = "Your task is to read the supplied text from a document which is being provided to help " \
                            f"define a software development project.  The text provided in this request is part {chunk_num} of {num_chunks} of " \
                            f"a document with the title '{title}'.\n " \
                            "As you read this text extract key information that defines the project in terms of the following critera: \n" \
                            f"{criteria_value}\n" \
                            "Please format your final response as a JSON object with the following structure:\n" \
                            "{{ " \
                            "   'relevant_data': '<string: extracted data matching criteria>'" \
                            "}}" \
                            f"If your response contains multiline text be sure to format it properly for JSON.\n" \
                            f"The current document text to examine is: ({chunk}).\n"
                current_info = doc.get_info(criteria_key)
                if current_info:
                    user_prompt += f"\nRelevant data from all prior document chunks for this criteria is {current_info}. Consolidate this with new information extracted from the current text."
                try:
                    response = litellm.completion(
                        model=model_name,
                        response_format={"type": "json_object"},
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ]
                    )
                    output = response.choices[0].message.content

                    prompt_tokens = int(response.usage.prompt_tokens)
                    completion_tokens = int(response.usage.completion_tokens)

                    if self.cost_tracer:
                        self.cost_tracer.add_cost_by_tokens("Projdefn", model_name, prompt_tokens, completion_tokens)
                        if self.reporter.cost_update_callback:
                            all_costs = self.cost_tracer.get_all_costs()
                            total_cost = self.cost_tracer.get_total_cost()
                            self.reporter.cost_update_callback(all_costs, total_cost)

                    try:
                        doc_info = json.loads(output)
                    except json.JSONDecodeError:
                        pass
                except Exception as e:
                    self.logger.error(f"_examine_document had exception processing doc ({title}) for criteria \'{criteria_key}\':  {str(e)}")
                    pass
                doc.add_info(criteria_key, doc_info['relevant_data'])
        self.logger.info(f"done examination of ({title})")

    def _parse_document(self, docspec:ProjDefnDocSpecifier) -> ProjDefnDoc:
        doc = None
        pathname = docspec.pathname
        parsed_doc = fitz.open(pathname)
        if parsed_doc:
            doc = ProjDefnDoc(docspec)
            hash = self._get_file_hash(pathname)
            doc.set_doc_hash(hash)
            total_bytes = 0
            chunk_bytes = 0
            chunk = []
            for parsed_page in parsed_doc:
                page_text = parsed_page.get_text()
                page_bytes = len(page_text)
                if chunk_bytes + page_bytes > self.chunk_size:
                    doc.add_chunk('\n'.join(chunk))
                    chunk = []
                    chunk_bytes = 0
                chunk.append(page_text)
                chunk_bytes += page_bytes
                total_bytes += page_bytes
                page = ProjDefnDocPage(parsed_page.number, page_text)
                doc.add_page(page)
            if chunk:
                doc.add_chunk('\n'.join(chunk))
            title = os.path.basename(pathname)
            metadata = parsed_doc.metadata
            if 'title' in metadata and len(metadata['title']) > 0:
                title = metadata['title']
            doc.set_title(title)
            self.logger.info(f"doc: {title} pages {doc.page_count()} chunks {doc.chunk_count()} total bytes: {total_bytes}")
        return doc

    def _consolidate_definitions(self):
        import litellm
        self.logger.info("begin consolidation of project definitions")
        if self._full_refresh:
            self._project_definition.clear()
            self._full_refresh = False
        for doc in self._defn_docs.values():
            title = doc.get_title()
            doc_info = doc.get_doc_info()
            for criteria_key, criteria_value in doc_info.items():
                master_value = self._project_definition.get(criteria_key)
                if not master_value:
                    master_value = criteria_value
                else:
                    model_name = self.model
                    system_prompt = "You are an expert software architect."
                    user_prompt = "Your task is to examine project definition data extracted from different documents and " \
                                "consolidate them into a master project definition.  You are provided with text representing " \
                                "project definition data that has been extracted from a definition document for a certain criteria " \
                                "and also the text representing the current master definition data for that same criteria.  You need " \
                                "to update the master data by combining the new data with the current data such that no information is " \
                                "lost while eliminating any redunancy.  If the new data conflicts with the current data flag this by " \
                                "setting the 'conflicts' field in your final reponse to text that describes the conflict but update the " \
                                "master definition to be consistent with the new data. If there are no conflicts omit the 'conflicts' field " \
                                "from your response. Differences should only be considered a conflict if there is an actual contridiction " \
                                "(for example the master data says 'use X' but the new data says to 'not use X').  Otherwise the updated " \
                                "master text should be a combination of the old master and the new value.\n " \
                                "The definition criteria for the supplied new text and current master text:\n " \
                                f"{criteria_key}\n " \
                                f"The new text: ({criteria_value})\n " \
                                f"The current master text: ({master_value})\n " \
                                "Please format your final response as a JSON object with the following structure:\n" \
                                "{{ " \
                                "   'new_master': '<string: consolidated data for this criteria>' " \
                                "   'conflicts': '<string describing any conflicts detected during consolidation>' " \
                                "}}" \
                                f"If your response contains multiline text be sure to format it properly for JSON.\n"
                    try:
                        response = litellm.completion(
                            model=model_name,
                            response_format={"type": "json_object"},
                            messages=[
                                {"role": "system", "content": system_prompt},
                                {"role": "user", "content": user_prompt}
                            ]
                        )
                        output = response.choices[0].message.content

                        prompt_tokens = int(response.usage.prompt_tokens)
                        completion_tokens = int(response.usage.completion_tokens)

                        if self.cost_tracer:
                            self.cost_tracer.add_cost_by_tokens("Projdefn", model_name, prompt_tokens, completion_tokens)
                            if self.reporter.cost_update_callback:
                                all_costs = self.cost_tracer.get_all_costs()
                                total_cost = self.cost_tracer.get_total_cost()
                                self.reporter.cost_update_callback(all_costs, total_cost)

                        try:
                            doc_info = json.loads(output)
                        except json.JSONDecodeError:
                            pass
                    except Exception as e:
                        self.logger.error(f"_consolidate_definition had exception processing doc ({title}) for criteria \'{criteria_key}\':  {str(e)}")
                        pass
                    conflicts = doc_info.get('conflicts')
                    if conflicts:
                        self.logger.warning(f"_consolidate_definition: definition conflict: {conflicts} with doc ({title} for criteria \'{criteria_key}\'")
                    master_value = doc_info.get('new_master')
                if master_value:
                    self._project_definition[criteria_key] = master_value
        self.logger.info("done consolidation of project definitions")
        self.reporter.send_agent_message("Project definition creation complete")
        self._persist_project_definition()
        self._need_refresh = False


    def _ingest_document(self, docspec:ProjDefnDocSpecifier):
        doc = self._parse_document(docspec)
        if doc:
            self._examine_document(doc)
            self._persist_document_definition(doc)
            self._add_definitions(doc,True)

    def _service_ingest_queue(self):
        docspec = None
        with self._lock:
            if self._ingest_queue:
                docspec = self._ingest_queue[0]
                self._ingest_queue= self._ingest_queue[1:]
        
        if docspec:
            self._ingest_document(docspec)
        return

    def _worker_loop(self):
        self.loop_worker = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop_worker)
        self._load_persisted_project_definition()
        if self._project_definition and not self._ingest_queue:
            self.reporter.send_agent_message("Project definition creation complete")

        while True:
            if not self._defnLocked:
                try:
                    self._service_ingest_queue()
                except Exception as e:
                    self.logger.error(f"Exception servicing ingest queue {str(e)}")

            if self.stop_worker:
                self.worker_stopped = True
                break

            with self._lockConsolidation:
                if not self._defnLocked:
                    need_refresh = False
                    with self._lock:
                        if not self._ingest_queue:
                            need_refresh = self._need_refresh

                if need_refresh:
                    self._consolidate_definitions()

            self.loop_worker.run_until_complete( asyncio.sleep(1) )

        return None

    def _is_ingestible(self, item_path):
        item = os.path.basename(item_path)
        ingestible = True
        if item.startswith('.'):
            ingestible = False
        good_suffix = False
        for suffix in ['.pdf']:
            if item_path.lower().endswith(suffix):
                good_suffix = True
                break
        if not good_suffix:
            ingestible = False
        return ingestible

    def _is_unit_match(self, term, text):
        # Create a pattern with word boundaries around term
        pattern = r'\b' + re.escape(term) + r'\b'
        
        # Search for the pattern in text, ignoring case
        return bool(re.search(pattern, text, re.IGNORECASE))

    def findRelevantKeys(self, search_terms, and_search):
        relevant_keys = []

        for key, value in self._project_definition.items():
            matchWithKey = False

            def search_value(key, value, term):
                match = False
                if isinstance(value,dict):
                    domain = value
                    domain_keys = domain.keys()
                    for key in domain_keys:
                        match = search_value(key, domain[key], term)
                        if match:
                            break
                elif isinstance(value,list):
                    match = self._is_unit_match(term,f"{value}")
                elif isinstance(value,str):
                    match = self._is_unit_match(term,value)
                if match:
                    self.logger.debug(f'task knowledge match key {key} with term {term}')
                return match

            for term in search_terms:
                match = search_value(key,value,term)

                matchWithKey |= match
                if and_search and not match:
                    matchWithKey = False
                    break
                if not and_search and match:
                    break

            if matchWithKey:
                if not key in relevant_keys:
                    relevant_keys.append(key)

        results = []
        for key in relevant_keys:
            results.append( key )

        return results

    def getKeys(self):
        items = []
        for key in self._project_definition.keys():
            items.append(key)
        return items


    def getKeyValues(self, keys):
        values = []
        with self._lock:
            for key in keys:
                if key in self._project_definition.keys():
                    value = self._project_definition[key]
                    values.append( f"{key}: [{value}]")
        return ', '.join(values)


    def start(self):
        if not self.thread_worker:
            self.thread_worker= threading.Thread(target=self._worker_loop, daemon=True)
            self.thread_worker.start()

    def lockDefinitions(self):
        with self._lockConsolidation:
            self.logger.info("lockDefinitions")
            self._defnLocked = True

    def addToIngestQueue(self, name, pathname):
        if self._is_ingestible(pathname):
            doc = ProjDefnDocSpecifier(pathname,name)
            with self._lock:
                if pathname not in self._ingest_queue:
                    self.logger.info(f"adding {pathname} to ingest queue")
                    self._ingest_queue.append(doc)

    def removeDocument(self, name, pathname):
        self.logger.info(f"removeDocument: {name}, {pathname}")
        remove_spec = ProjDefnDocSpecifier(name,pathname)
        with self._lock:
            if remove_spec in self._ingest_queue:
                self._ingest_queue.remove(remove_spec)
            if name in self._defn_docs:
                doc = self._defn_docs.pop(name)
                if doc:
                    self._unpersist_document_definition(doc)
                self._need_refresh = True
                self._full_refresh = True

    def unlockDefinitions(self):
        with self._lockConsolidation:
            self.logger.info("unlockDefinitions")
            self._defnLocked = False

    @staticmethod
    def getInstance(config : dict = None, id: str = "default", session_id: str = None):
        instance = None
        with _lock:
            instance_key = f"{id}_{session_id}" if session_id else id
            if not instance_key in ProjDefn._instances:
                if config:
                    required_keys = ["base_path", "model", "timeout", "chunk_size",
                                   "cost_tracer", "reporter", "helpers"]
                    for key in required_keys:
                        if key not in config:
                            raise ValueError(f"Missing required key: {key}")
                    ProjDefn._instances[instance_key] = ProjDefn(id, config, session_id)
            if instance_key in ProjDefn._instances:
                instance = ProjDefn._instances[instance_key]
        return instance


    @staticmethod
    def releaseInstance(id: str, session_id: str = None):
        with _lock:
            instance_key = f"{id}_{session_id}" if session_id else id
            if instance_key in ProjDefn._instances:
                instance = ProjDefn._instances[instance_key]
                instance.stop_worker = True
                while not instance.worker_stopped:
                    time.sleep(1)
                ProjDefn._instances.pop(instance_key)

