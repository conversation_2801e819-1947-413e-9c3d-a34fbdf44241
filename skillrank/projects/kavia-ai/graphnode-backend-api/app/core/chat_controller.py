# New file: app/core/chat_thread.py

import threading
import asyncio
import concurrent.futures
from app.connection.establish_db_connection import get_mongo_db
from app.services.notification_service import NotificationService
from app.core.constants import TASKS_COLLECTION_NAME
from app.connection.tenant_middleware import get_tenant_id
from app.utils.code_generation_utils import custom_asdict_factory
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionControl
from code_generation_core_agent.llm.chat_interface import ChatInterface
from code_generation_core_agent.chat.cga_chat import ChatMessage
from app.core.websocket.client import WebSocketClient
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.core.constants import TASKS_COLLECTION_NAME
from dataclasses import asdict
from app.routes.users_route import format_response_user
from dataclasses import asdict
from app.models.notification_model import NotificationModel, CodeGenerationNotificationData
from app.utils.datetime_utils import generate_timestamp, to_utc
from app.core.git_controller import G<PERSON><PERSON><PERSON><PERSON><PERSON>

# Create a global thread pool executor
_thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)



def update_message_in_db(db, task_id, message_id, message_dict):
    try:
        # First try to update existing message
        result = db[TASKS_COLLECTION_NAME].update_one(
            {
                "_id": task_id,
                "messages.id": message_id
            },
            {
                "$set": {"messages.$": message_dict}
            }
        )
        
        # If no document was modified, message doesn't exist, so push it
        if result.modified_count == 0:
            db[TASKS_COLLECTION_NAME].update_one(
                {"_id": task_id},
                {"$push": {"messages": message_dict}}
            )
    except Exception as e:
        print(f"Exception in update message in db: {e}")
        pass


def schedule_db_update(db, task_id, message_id, message_dict):
    """Schedule the database update to run in background"""
    if db is not None:
        _thread_pool.submit(
            update_message_in_db,
            db,
            task_id,
            message_id,
            message_dict
        )
    else:
        db = get_mongo_db(db_name=get_tenant_id()).db
        _thread_pool.submit(
            update_message_in_db,
            db,
            task_id,
            message_id,
            message_dict
        )   



class ChatInterfaceThread:

    def __init__(self, task_id: str, ws_client: WebSocketClient, db=None):
        self.task_id = task_id
        self.ws_client = ws_client
        self.chat_interface = ChatInterface()
        self._thread = None
        self._running = False
        self._message_queue = asyncio.Queue()
        self.cached_user_details = {}
        self.user_manager = None
        self.db = db
        self.got_first_message = False
        self.code_generation_path = None
        self.controller:TaskExecutionControl = None
        self.git_controller: GitController = None
        self.current_message_id = None

        #Register callbacks
        self.chat_interface.add_callback("message_added", self.handle_message_added)
        self.chat_interface.add_callback("message_resolved", self.handle_message_resolved)
        self.chat_interface.add_callback("needs_response", self.handle_message_needs_response)
        self.chat_interface.add_callback("stream_chunk", self.handle_stream_chunk)
    
    def set_code_generation_path(self, code_generation_path: str):
        """Set the code generation path"""
        self.code_generation_path = code_generation_path

    def set_git_controller(self, git_controller: GitController):
        """Set the git controller instance"""
        self.git_controller = git_controller

    def set_user_manager(self, user_manager: CognitoUserManager):
        """Set the user manager instance"""
        self.user_manager = user_manager

    def set_controller(self, controller):
        """Set the controller instance"""
        self.controller = controller


    def update_activity(self):
        try:
            if self.controller:
                self.controller._update_activity()
        except Exception as e:
            print(f"Error updating activity: {e}")
            pass

    def handle_message_added(self, message: ChatMessage):
        self.update_activity()
        if message.id:
            self.current_message_id = message.id
        if message.content:
            if "Starting to clone git repositories…" in message.content or "Cloning repository:" in message.content or "already exists and is not an empty directory." in message.content:
                return
        print("Message added", message)
        if not self.got_first_message:
            print("PHASE FIRST MESSAGE, START TIME: ", generate_timestamp())
            self.got_first_message = True
        if message.timestamp:
            message.timestamp = to_utc(message.timestamp)
        message_dict = asdict(message, dict_factory=custom_asdict_factory)
        self.ws_client.send_message("message_added", message_dict)
        try:
            # Make sure metadata exists and has user_id
            metadata = message_dict.get("metadata", {})
            if metadata and isinstance(metadata, dict) and metadata.get("user_id"):
                user_id = metadata.get("user_id")
                if user_id not in self.cached_user_details and self.user_manager:
                    user_details = self.user_manager.get_user_by_identifier(user_id)
                    user_details = format_response_user(user_details)
                    self.cached_user_details[user_id] = user_details
                    print("User details", self.cached_user_details[user_id])

                message_dict["user_details"] = {
                    "name": self.cached_user_details[user_id].get("Name", ""),
                    "email": self.cached_user_details[user_id].get("Email", ""),
                    "designation": self.cached_user_details[user_id].get("Designation", ""),
                    "department": self.cached_user_details[user_id].get("Department", "")
                }
                self.ws_client.send_message("user_message", message_dict)
                if self.db is not None:
                    schedule_db_update(self.db, self.task_id, message_dict["id"], message_dict)
                return
        except Exception as e:
            print(f"Error handling message added: {e}")
            pass
        
        print("Message dict", message_dict)
        self.ws_client.send_message("agent_message", message_dict)

        if self.db is not None:
            schedule_db_update(self.db, self.task_id, message_dict["id"], message_dict)


    def handle_message_resolved(self, message: ChatMessage):
        self.update_activity()
        if message.content:
            if "Starting to clone git repositories…" in message.content or "Cloning repository:" in message.content or "already exists and is not an empty directory." in message.content:
                return
        if message.timestamp:
            message.timestamp = to_utc(message.timestamp)
        print("Message resolved", message)
        message_dict = asdict(message, dict_factory=custom_asdict_factory)
        self.ws_client.send_message("agent_message", message_dict)
        self.ws_client.send_message("message_resolved", message_dict)

        if self.db is not None:
            schedule_db_update(self.db, self.task_id, message_dict["id"], message_dict)

    def handle_message_needs_response(self, message: ChatMessage):
        self.update_activity()
        if message.timestamp:
            message.timestamp = to_utc(message.timestamp)
        print("Message needs response", message)
        message_dict = asdict(message, dict_factory=custom_asdict_factory)
        self.ws_client.send_message("agent_message", message_dict)
        self.ws_client.send_message("needs_response", message_dict)


    def handle_stream_chunk(self, message:ChatMessage, chunk):
        try:
            self.update_activity()
            # Then process the message as usual
            if message.timestamp:
                message.timestamp = to_utc(message.timestamp)
            message_dict = asdict(message, dict_factory=custom_asdict_factory)
            self.ws_client.send_message("agent_message", message_dict)
            
            print("CHAT CONTROLLER HANDLING STREAM CHUNK:", message_dict)
            # Store in database like other handlers do
            if self.db is not None:
                schedule_db_update(self.db, self.task_id, message_dict["id"], message_dict)
        except Exception as e:
            print(f"Error handling stream chunk: {e}")

        
    def start(self):
        """Start the chat interface thread"""
        if not self._thread:
            self._running = True
            self._thread = threading.Thread(target=self._run, daemon=True)
            self._thread.start()
            self.ws_client.connect()
            self.ws_client.add_message_handler(self._handle_message)
            self.ws_client.start_message_handler()

    def stop(self):
        """Stop the chat interface thread"""
        self._running = False
        if self.ws_client:
            self.ws_client.stop_message_handler()
            self.ws_client.disconnect()
        if self._thread:
            self._thread.join()
            self._thread = None

    def _run(self):
        """Main thread loop that processes messages"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        while self._running:
            try:
                # Process any messages in the queue
                while not self._message_queue.empty():
                    message = loop.run_until_complete(self._message_queue.get())
                    self._process_message(message)
                
                loop.run_until_complete(asyncio.sleep(0.1))
            except Exception as e:
                print(f"Error in chat interface thread: {e}")

        loop.close()

    async def _handle_message(self, data):
        """Handle incoming WebSocket messages"""
        try:
            if data.get('type') == 'user_input':
                await self._message_queue.put({
                    'content': data.get('input'),
                    'parent_id': data.get('parent_id'),
                    'user_id': data.get('user_id'),
                    'attachments': data.get('attachments')
                })
            
            elif data.get('type') == 'send_message':
                await self._message_queue.put({
                    'content': data.get('content'),
                    'parent_id': data.get('parent_id'),
                    'user_id': data.get('user_id'),
                    'attachments': data.get('attachments')
                })
                
            elif data.get('type') == 'execute_command':
                response = self.chat_interface.user_interface.execute_command(data.get('command'),data.get('parent_id'))
                message_dict = asdict(response, dict_factory=custom_asdict_factory)
                self.ws_client.send_message("command_response", message_dict)
            
            elif data.get('type') == 'initial_messages':
                messages = [asdict(m, dict_factory=custom_asdict_factory) for m in self.chat_interface.messages.values()]
                self.ws_client.send_message("initial_messages", messages)
        except Exception as e:
            print(f"Error handling message in chat thread: {e}")


    def process_attachment_paths(self, attachments):
        """Process the attachment paths"""
        print("Processing attachments", attachments)
        processed_attachments = []

        for attachment in attachments:
            try:
                attachment_path = attachment.get("path")
                attachment_path = self.controller.target_attachments_folder + "/" + attachment.get("filename")
                print("Processed attachment path", attachment_path)
                attachment["path"] = attachment_path
                processed_attachments.append(attachment)
            except Exception as e:
                print(f"Error processing attachment: {e}")
                pass
    
        return processed_attachments
    
    def _process_message(self, message_data):
        """Process a message from the queue"""
        try:
            content = message_data.get('content')
            parent_id = message_data.get('parent_id')
            user_id = message_data.get('user_id')
            metadata = {"user_id": user_id}
            attachments = message_data.get('attachments', [])
            # Process the input through chat interface
            try:
                attachments = self.process_attachment_paths(attachments)
                print("Attachments sent to chat interface", attachments)   
            except Exception as e:
                print(f"Error processing attachments: {e}")
                attachments = []
                pass

            if attachments:
                chat_response = self.chat_interface.process_input(content, parent_id, metadata, attachments)
            else:
                chat_response = self.chat_interface.process_input(content, parent_id, metadata)
            
            if chat_response:
                # Add user metadata
                chat_response.metadata = {"user_id": user_id}
                
                # Convert to dict for websocket transmission
                self.handle_message_added(chat_response)
        except Exception as e:
            print(f"Error processing message in chat thread: {e}")

    def get_chat_interface(self) -> ChatInterface:
        """Get the underlying chat interface"""
        return self.chat_interface

    def set_user_details(self, user_id: str, user_details: dict):
        """Cache user details for a user"""
        self.cached_user_details[user_id] = {
            "name": user_details.get("Name", ""),
            "email": user_details.get("Email", ""),
            "designation": user_details.get("Designation", ""),
            "department": user_details.get("Department", "")
        }

    def _send_notification_sync(self, task_id: str, db, content: str):
        """Synchronous version of notification sending"""
        try:
            # Get task info synchronously
            task = db[TASKS_COLLECTION_NAME].find_one(
                {"_id": task_id},
                projection={
                    "project_id": 1,
                    "architecture_id": 1,
                    "user_id": 1,
                    "_id": 0
                }
            )
            
 
            if task:
                project_id = task.get("project_id")
                design_id = task.get("architecture_id")
                task_id = self.task_id
                receiver_id = task.get("user_id")
                notification_data = NotificationModel(
                    receiver_id=receiver_id,
                    type="code_generation",
                    action="code_generation",
                    data=CodeGenerationNotificationData(
                    message=f"Task ID: {task_id} - Waiting for user input to continue",
                    link=f"/projects/{project_id}/architecture/design?task_id={task_id}",
                    project_id=project_id,
                    task_id=task_id,
                    design_id=design_id
                )
                )
                
                # Now try to send push notification if needed
                try:
                    notification_service = NotificationService()
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(notification_service.send_notification(notification_data))
                    loop.close()
                except Exception as e:
                    print(f"Error sending push notification (but DB storage succeeded): {e}")
                
        except Exception as e:
            print(f"Error in sync notification handling: {e}")
   
