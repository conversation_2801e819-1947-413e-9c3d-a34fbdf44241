import time
import json
from fastapi import Request
from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode
from starlette.middleware.base import BaseHTTPMiddleware
from app.telemetry.logger_config import get_logger

logger = get_logger(__name__)
tracer = trace.get_tracer(__name__)

class APITrackingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Start a new span for this request
        with tracer.start_as_current_span("api_request") as span:
            try:
                # Process request first to populate request.state
                response = await call_next(request)
                
                # Now extract user information after auth middleware has run
                user_info = getattr(request.state, 'user', {})
                tenant_id = getattr(request.state, 'tenant_id', None)
                
                # Calculate processing time
                process_time = time.time() - start_time
                
                # Helper function to safely get values
                def safe_get(obj, key, default=""):
                    try:
                        if isinstance(obj, dict):
                            return obj.get(key, default)
                        return getattr(obj, key, default)
                    except (Attribute<PERSON><PERSON>r, KeyError, TypeError):
                        return default
                
                # Extract JWT tenant_id as fallback
                jwt_tenant_id = None
                authorization_header = request.headers.get("Authorization", "")
                if authorization_header:
                    try:
                        import jwt
                        token = authorization_header.replace('Bearer ', '') if authorization_header.startswith('Bearer ') else authorization_header
                        decoded = jwt.decode(token, options={"verify_signature": False})
                        jwt_tenant_id = decoded.get('custom:tenant_id', 'unknown')
                    except:
                        jwt_tenant_id = 'jwt_decode_failed'
                
                # Determine final tenant_id
                final_tenant_id = tenant_id or jwt_tenant_id or request.headers.get("X-Tenant-Id") or request.headers.get("selected_tenant_id") or "unknown"
                
                # Set comprehensive span attributes
                span.set_attributes({
                    "http.method": request.method,
                    "http.url": str(request.url),
                    "http.scheme": request.url.scheme,
                    "http.host": safe_get(request.url, 'hostname', 'unknown'),
                    "http.target": request.url.path,
                    "http.user_agent": request.headers.get("user-agent", ""),
                    "user.id": safe_get(user_info, "sub", "anonymous"),
                    "user.email": safe_get(user_info, "email", ""),
                    "user.username": safe_get(user_info, "cognito:username", ""),
                    "tenant.id": final_tenant_id,
                    "tenant.from_jwt": jwt_tenant_id or "not_found",
                    "tenant.from_state": tenant_id or "not_set",
                    "client.ip": safe_get(request.client, 'host', 'unknown') if request.client else "unknown",
                    "request.headers.authorization": "Bearer ***" if authorization_header else "None",
                    "request.headers.content_type": request.headers.get("content-type", ""),
                    "request.headers.is_public_selected": request.headers.get("is_public_selected", ""),
                    "request.headers.selected_tenant_id": request.headers.get("selected_tenant_id", ""),
                    "request.headers.selected_project_creator_email": request.headers.get("selected_project_creator_email", ""),
                    "http.status_code": response.status_code,
                    "http.response.size": safe_get(response.headers, "content-length", 0),
                    "duration_ms": round(process_time * 1000, 2),
                })
                
                # Add query parameters if any
                if request.query_params:
                    span.set_attribute("http.query_string", str(request.query_params))
                
                # Set span status based on response code
                if response.status_code >= 400:
                    span.set_status(Status(StatusCode.ERROR, f"HTTP {response.status_code}"))
                else:
                    span.set_status(Status(StatusCode.OK))
                
                # Create comprehensive tracking data
                tracking_data = {
                    "timestamp": time.time(),
                    "method": request.method,
                    "url": str(request.url),
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "duration_ms": round(process_time * 1000, 2),
                    "user_id": safe_get(user_info, "sub", "anonymous"),
                    "user_email": safe_get(user_info, "email", ""),
                    "username": safe_get(user_info, "cognito:username", ""),
                    "tenant_id": final_tenant_id,
                    "tenant_from_jwt": jwt_tenant_id or "not_found",
                    "tenant_from_state": tenant_id or "not_set",
                    "client_ip": safe_get(request.client, 'host', 'unknown') if request.client else "unknown",
                    "user_agent": request.headers.get("user-agent", ""),
                    "content_type": request.headers.get("content-type", ""),
                    "query_params": dict(request.query_params) if request.query_params else {},
                    "has_auth": bool(authorization_header),
                    "response_size": safe_get(response.headers, "content-length", 0),
                    "custom_headers": {
                        "is_public_selected": request.headers.get("is_public_selected", ""),
                        "selected_tenant_id": request.headers.get("selected_tenant_id", ""),
                        "selected_project_creator_email": request.headers.get("selected_project_creator_email", ""),
                        "bypass_credit_check": request.headers.get("Bypass-Credit-Check", ""),
                    },
                    "jwt_claims": {
                        "is_admin": safe_get(user_info, "custom:is_admin", ""),
                        "free_user": safe_get(user_info, "custom:free_user", ""),
                        "auth_provider": safe_get(user_info, "auth_provider", ""),
                        "email_verified": safe_get(user_info, "email_verified", ""),
                        "token_use": safe_get(user_info, "token_use", ""),
                    } if user_info else {}
                }
                
                # Only print detailed tracking for non-OPTIONS requests to reduce noise
                if request.method != "OPTIONS":
                    print("=" * 100)
                    print(f"🚀 API REQUEST TRACKING - {request.method} {request.url.path}")
                    print("=" * 100)
                    
                    # Core request info
                    print(f"📍 ENDPOINT: {request.method} {request.url.path}")
                    print(f"⏱️  DURATION: {round(process_time * 1000, 2)}ms")
                    print(f"📊 STATUS: {response.status_code}")
                    print(f"📦 SIZE: {safe_get(response.headers, 'content-length', 0)} bytes")
                    
                    # User & Tenant info
                    print(f"👤 USER: {safe_get(user_info, 'email', 'anonymous')} (ID: {safe_get(user_info, 'sub', 'anonymous')})")
                    print(f"🏢 TENANT: {final_tenant_id} (JWT: {jwt_tenant_id or 'N/A'}, State: {tenant_id or 'N/A'})")
                    
                    # Request details
                    print(f"🌐 CLIENT: {safe_get(request.client, 'host', 'unknown') if request.client else 'unknown'}")
                    print(f"🔐 AUTH: {'✅ Bearer Token' if authorization_header else '❌ None'}")
                    
                    # Custom headers (only if present)
                    custom_headers = tracking_data["custom_headers"]
                    active_headers = {k: v for k, v in custom_headers.items() if v}
                    if active_headers:
                        print(f"📋 CUSTOM HEADERS: {active_headers}")
                    
                    # Query params (only if present)
                    if request.query_params:
                        print(f"🔍 QUERY PARAMS: {dict(request.query_params)}")
                    
                    # JWT Claims (only if user is authenticated)
                    if user_info and any(tracking_data["jwt_claims"].values()):
                        jwt_info = {k: v for k, v in tracking_data["jwt_claims"].items() if v}
                        print(f"🎫 JWT CLAIMS: {jwt_info}")
                    
                    print("=" * 100)
                
                # Log using structured logging (all requests)
                logger.info("API request tracked", extra=tracking_data)
                
                return response
                
            except Exception as e:
                # Handle errors
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR, str(e)))
                logger.error(f"Error in tracking middleware: {str(e)}")
                
                # Still return the response even if tracking fails
                if 'response' in locals():
                    return response
                else:
                    # If error occurred before getting response, still try to get it
                    return await call_next(request)