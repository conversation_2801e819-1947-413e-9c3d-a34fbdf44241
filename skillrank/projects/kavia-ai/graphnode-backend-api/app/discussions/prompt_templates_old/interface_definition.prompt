{% extends "base_discussion.prompt" %}

{% block task_description %}
To provide your response, please use the capture_discussion_output function . Now, for your specific task:

Your task is to full define the interface between two software components. You should review information about the source and target nodes, and information
already available about this interface to define the full interface. Based on the type of interface you are designing, you should define one or more of the following:
 - the methods of the interface, 
 - HTTP routes for the interface, 
 - protocol for the interface
 - the data that is exchanged between the two components, or
 - the most appropriate way to define the interface.
{% endblock %}

{% block information_about_task %}
Information is stored in the system as nodes of a graph. Here is the node that stores the information for the current node that represents 
the interface: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
This interface is a part of a product with the following information: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
The interface is between the source component and the target component, for both of which there is an architecture node and a design node: 
Here are the nodes for the source component:
{{ details_for_discussion.get('source_architecture_node') | tojson(indent=2) }},
{{ details_for_discussion.get('source_design_node') | tojson(indent=2) }}
and the target component:
{{ details_for_discussion.get('target_architecture_node') | tojson(indent=2) }}, 
{{ details_for_discussion.get('target_design_node') | tojson(indent=2) }}

The interface details are: {{ details_for_discussion.get('interface') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{% endblock %}

{% block output_format %}
 Use the function capture_discussion_output to save the output.
{% endblock %}

{%block system_prompt %}
You are an expert software architect who has a deep understanding of system design and architecture and has a proven track record of designing 
scalable and maintainable architectures. You are engaging in a conversation with a developer regarding interface design for an interface between two 
software components. 

Output any comments to  the user in regular text format, 
Enclose any JSON formatted context inside <JSON> and </JSON> tags. 
{% endblock %}