import os
import base64
import shlex

from projdefn import <PERSON>j<PERSON><PERSON>n
from projdefn import <PERSON>jDefnReporter
from projdefn import ProjDefnHelpers

class Reporter(ProjDefnReporter):
    def __init__(self):
        self.ready = False
        pass

    def send_agent_message(self, message):
        if 'Project definition creation complete' in message:
            self.ready = True
        pass

    def cost_update_callback(self, all_costs, total_cost ):
        pass

    def is_ready(self):
        return self.ready

class Helpers(ProjDefnHelpers):
    def __init__(self, base_path):
        self.base_path = base_path

    def execute_cmd(self, cmd):
        import subprocess
        try:
            result = subprocess.run(cmd, capture_output=True, shell=True, text=True)
            return result.stdout, result.returncode
        except Exception as e:
            print(f"Error: cmd {cmd} had exception {e}")
        return None, None

    def read_file(self, filename):
        contents = None
        try:
            with open(filename, 'r') as file:
                contents = file.read()
        except UnicodeDecodeError:
            with open(filename, 'rb') as file:
                contents = file.read()
        except FileNotFoundError:
            pass
        except UnicodeDecodeError:
            pass
        return contents
    
    def write_file(self, filename, content):
        escaped_path = shlex.quote(filename)
        encoded_new_content = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        cmd = f"mkdir -p $(dirname {escaped_path}) && echo '{encoded_new_content}' | base64 -d > {escaped_path}"
        output, returncode = self.execute_cmd(cmd)
        return returncode

    def list_directory(self, directory):
        list = []
        try:
            list = os.listdir(directory)
        except FileNotFoundError:
            pass
        except NotADirectoryError:
            pass
        return list

class ProjDefn_Helper:
    def __init__(self, reporter, base_path, documents = None, criteria = None):
        configuration = {
            "base_path" : base_path,
            "model" : "gpt-4o-mini",
            "timeout": 60,
            "chunk_size": 64*1024,
            "cost_tracer" : None,
            "reporter" : reporter,
            "helpers" : Helpers(base_path)
        }
        if documents:
            configuration['documents'] = documents
        if criteria:
            configuration['criteria'] = criteria

        self.knowledge = ProjDefn.getInstance(configuration)

