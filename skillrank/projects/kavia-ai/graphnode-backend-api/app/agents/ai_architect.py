import asyncio
from fastapi import HTT<PERSON>Exception
from jinja2 import Environment, FileSystemLoader
import json
import time
from app.agents.agent import Agent
from app.models.node_model import ConfigureNodeRequest
from app.discussions.discussion_util import  conduct_discussion,create_root_node_properties
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.logs_utils import get_path
from app.utils.node_utils import get_node_type
from app.connection.establish_db_connection import get_node_db
from app.discussions.discussion import Discussion
from app.discussions.types.architecture_design_details import ArchitectureDesignDetails
from llm_wrapper.core.llm_interface import LLMInterface
from app.telemetry.logger_config import get_logger, set_task_id
from app.connection.establish_db_connection import get_mongo_db
import datetime
from app.core.task_framework import TaskStatus


class ArchitectureAgent(Agent): 
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.architecture_root_id = None
        self.queue: asyncio.Queue = asyncio.Queue()
        self.architecture_model = 'c4'

        #TODO: we should pass the user_id as dynamic
        self.llm = LLMInterface(get_path(), 'ai_architech', '550e8400-e29b-41d4-a716-446655440000', node_id, 'Discussion_'+self.discussion_type, mongo_handler=get_mongo_db())
        self.update_logger_agent = get_logger(__name__)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None

  
    async def set_architecture_model(self, model):
        if model not in ['legacy', 'c4']:
            raise ValueError("Invalid architecture model. Choose 'legacy' or 'c4'.")
        self.architecture_model = model
        await self.mongo_handler.update_by_task_id(self.task_id, {'architecture_model': model})
        await self.send_update(self.task_id)

    async def run(self):
        while True:
            try:
                self.current_work_item = await self.queue.get()
                await self.process_work_item(self.current_work_item)
            except Exception as e:
                self.update_logger_agent.error(f"Error in run method: {str(e)}", exc_info=True)
            finally:
                self.queue.task_done()
        
    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            self.update_logger_agent.info(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
            
            if work_item_dict.entry == "get_architecture_root":
                await self.get_architecture_root(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id)
            elif work_item_dict.entry == "capture_requirements_for_architecture":
                await self.capture_requirements_for_architecture(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id)
            elif work_item_dict.entry == "configure_system_context":
                await self.configure_system_context(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id)
            elif work_item_dict.entry == "configure_containers":
                await self.configure_containers(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id)
            elif work_item_dict.entry == "configure_components":
                await self.create_components_config(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id)
            elif work_item_dict.entry == "get_deployment_root":
                await self.get_deployment_root(work_item_dict.node_id, work_item_dict.node_type,work_item_dict.project_id)
            elif work_item_dict.entry == "configure_deployment":
                await self.configure_deployment(work_item_dict.node_id, work_item_dict.node_type)
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found for agent {self.name}")
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)

    async def check_parent_configurations(self, node_id):
        """
        Check if all parent nodes up to the project node are properly configured.
        
        Args:
            node_id: ID of the node to check
            
        Returns:
            tuple: (is_valid, message) where:
                - is_valid is a boolean indicating if all parents are configured
                - message is a string containing error details or None if valid
        """
        try:
            # Get the current node
            node = await self.db.get_node_by_id(node_id)
            if not node:
                return False, f"Node {node_id} not found"
                
            # Initialize list to track unconfigured parents
            unconfigured_parents = []
            
            # Start with the immediate parent
            current_id = node_id
            
            # Check all parents up to project node
            while current_id:
                parent_node = await self.db.get_parent_node(current_id)
                
                # If no parent found, we've reached the top of the hierarchy
                if not parent_node:
                    break
                    
                # Check if parent is configured
                parent_state = parent_node.get('properties', {}).get('configuration_state')
                if parent_state != 'configured':
                    parent_type = Discussion.get_specific_node_type(parent_node['labels'])
                    parent_title = parent_node.get('properties', {}).get('Title', f"Node {parent_node['id']}")
                    unconfigured_parents.append(f"{parent_type}: {parent_title}")
                    
                # Move up to next parent
                current_id = parent_node['id']
                
                # Stop if we reach a Project node (top of hierarchy)
                if 'Project' in parent_node['labels']:
                    break
            
            # Determine result
            if unconfigured_parents:
                return False, f"The following parent nodes must be configured first: {', '.join(unconfigured_parents)}"
            
            return True, None
            
        except Exception as e:
            self.update_logger_agent.error(f"Error checking parent configurations: {str(e)}", exc_info=True)
            return False, f"Error checking parent configurations: {str(e)}"

    async def configure_system_context(self, node_id, node_type, project_id):
        """Configure system context in C4 model"""
        try:
            # Get or create system context
            system_context = await self.db.get_child_nodes(project_id, "SystemContext")

            # Add parent configuration check
            # parents_configured, message = await self.check_parent_configurations(system_context['id'])
            # if not parents_configured:
            #     self.update_logger_agent.warning(f"Cannot process work item: {message}")
            #     # Notify supervisor that this task cannot proceed
            #     await self.supervisor.notify_complete(self.current_work_item, success=False, error_message=message)
            #     return
            
            if not system_context:
                properties = {
                    "Title": "System Context",
                    "Description": "High-level view of the system",
                    "Type": "SystemContext"
                }
                system_context = await self.db.create_node(["SystemContext"], properties, project_id)
            else:
                system_context = system_context[0]

            # Configure the system context overview first
            await self.configure_node(system_context['id'], "SystemContext", self.root_node_type, "system_context_overview")
            
            # Then configure the system context containers
            await self.configure_node(system_context['id'], "SystemContext", self.root_node_type, "system_context_containers")
            
            # Notify completion
            self.current_work_item.node_id = system_context['id']
            self.current_work_item.node_type = "SystemContext"
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring system context: {str(e)}", exc_info=True)
            raise

    async def configure_containers(self, node_id, node_type, project_id):
        """Configure containers in C4 model"""
        try:
            # Get system context
            system_context = await self.db.get_child_nodes(project_id, "SystemContext")
            if not system_context:
                raise ValueError("System context not found")
            
            system_context = system_context[0]
            
            # Get existing containers
            containers = await self.db.get_child_nodes(system_context['id'], "Container")

            # Add parent configuration check
            # parents_configured, message = await self.check_parent_configurations(containers[0]['id'])
            # if not parents_configured:
            #     self.update_logger_agent.warning(f"Cannot process work item: {message}")
            #     # Notify supervisor that this task cannot proceed
            #     await self.supervisor.notify_complete(self.current_work_item, success=False, error_message=message)
            #     return
            
            # Configure each container
            for container in containers:
                # Configure the container
                await self.configure_node(container['id'], "Container", self.root_node_type, "configuration")
                
                # Check if container needs database configuration
                container_node = await self.db.get_node_by_id(container['id'])
                if container_node['properties'].get('HasDatabase', False): 
                    database_node = await self.db.get_child_nodes(container['id'], 'Database')
                    if database_node:
                        await self.configure_node(database_node[0]['id'], "Database", self.root_node_type, "configuration")
                    else:
                        self.update_logger_agent.info(f"Container {container['id']} does not have database")
                else:
                    self.update_logger_agent.info(f"Container {container['id']} does not have database")
                await self.autoconfig_interfaces(container['id'], "Container")
            
            # Notify completion
            self.current_work_item.node_id = system_context['id']
            self.current_work_item.node_type = "SystemContext"
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring containers: {str(e)}", exc_info=True)
            raise

    async def check_configuration(self, node):
        try:
            unconfigured_items = []
            parent_node = await self.db.get_parent_node(node['id'])
            
            project_configuration_state = parent_node.get('properties', {}).get('configuration_state')
            if project_configuration_state != 'configured':
                unconfigured_items.append('Project')

            requirement_root_children = await self.db.get_child_nodes(parent_node['id'], "RequirementRoot")
            if requirement_root_children and len(requirement_root_children) > 0:
                requirement_root = requirement_root_children[0]
                requirement_root_configuration_state = requirement_root.get('properties', {}).get('configuration_state')
                if requirement_root_configuration_state != 'configured':
                    unconfigured_items.append('Requirement')

            if unconfigured_items:
                message = "Please configure the following before proceeding: " + ", ".join(unconfigured_items)
                return False, message
            
            return True, None
        except Exception as e:
            self.update_logger_agent.error(f"Error checking configuration: {str(e)}", exc_info=True)
            raise

    async def get_architecture_root(self, node_id, node_type, project_id):
        try:
            self.node_id = node_id
            self.node_type = node_type

            node = await self.db.get_node_by_id(node_id)
            parent_node = await self.db.get_node_by_id(project_id)
            child_nodes = await self.db.get_child_nodes(project_id, "ArchitectureRoot")

            if child_nodes and len(child_nodes) > 0:
                node_to_configure = child_nodes[0]
            elif "ArchitectureRoot" in node['labels']:
                parent_node = await self.db.get_parent_node(self.node_id)
                project_configuration_state = parent_node.get('properties', {}).get('configuration_state')
                requirementRootChild = await self.db.get_child_nodes(parent_node['id'], "RequirementRoot")
                # requirementRoot_configuration_state = requirementRootChild[0].get('properties', {}).get('configuration_state')
                if(project_configuration_state != 'configured'):
                    self.update_logger_agent.warning("Please configure project")
                    return
                else:
                    node_to_configure = node
            else:
                node_type = "Architecture"
                properties = create_root_node_properties(node_type, self.root_node_type, parent_node)
                node_to_configure = await self.db.create_node([node_type, f'{node_type}Root'], properties, project_id)

            if not node_to_configure:
                error_message = f"Failed to create {node_type}"
                self.update_logger_agent.error(error_message)
                self.queue_messages.append({"error": error_message})
                await self.mongo_handler.push_to_array({'task_id': self.task_id}, 'queue_messages', {"error": error_message})
                await self.send_update(self.task_id)
                return None
            
            architecture_root_id = node_to_configure['id']
            self.current_work_item.node_id = architecture_root_id
            self.current_work_item.node_type = "Architecture"
            
            await self.supervisor.notify_complete(self.current_work_item)
            return
        except Exception as e:
            self.update_logger_agent.error(f"Error getting architecture root: {str(e)}", exc_info=True)
            raise

    async def get_deployment_root(self, node_id, node_type, project_id):
        """Create or retrieve the deployment root node."""
        try:
            self.node_id = node_id
            self.node_type = node_type
            
            # Get architecture root
            arch_root = await self.db.get_child_nodes(project_id, "ArchitectureRoot")
            if not arch_root:
                self.update_logger_agent.error("Architecture root not found")
                return
            
            arch_root = arch_root[0]
            
            # Get or create deployment root
            deployment_roots = await self.db.get_child_nodes(arch_root['id'], "DeploymentRoot")
            if deployment_roots:
                deployment_root = deployment_roots[0]
            else:
                properties = {
                    'Title': f"Deployment Configuration for {arch_root['properties']['Title']}",
                    'Description': "Root node for deployment configurations"
                }
                deployment_root = await self.db.create_node(["DeploymentRoot"], properties, arch_root['id'])

            self.current_work_item.node_id = deployment_root['id']
            self.current_work_item.node_type = "DeploymentRoot"
            await self.supervisor.notify_complete(self.current_work_item)

        except Exception as e:
            self.update_logger_agent.error(f"Error in get_deployment_root: {str(e)}")
            raise

    async def configure_deployment(self, node_id, node_type):
        """Configure deployment settings through discussion."""
        try:
            self.node_id = node_id
            self.node_type = node_type
            
            # Start deployment configuration discussion
            await self.configure_node(
                node_id=self.node_id,
                node_type="DeploymentRoot",
                root_node_type="Project",
                configuration_type="configuration"
            )
            
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error in configure_deployment: {str(e)}")
            raise


    async def capture_requirements_for_architecture(self, node_id, node_type, project_id):
        try:
            self.node = await self.db.get_node_by_id(node_id)
            parent_node = await self.db.get_parent_node(self.node_id)

            if 'ArchitectureRoot' in self.node['labels']:
                arch_req_nodes = await self.db.get_child_nodes(node_id, "ArchitecturalRequirement")

                # Add parent configuration check
                # parents_configured, message = await self.check_parent_configurations(arch_req_nodes[0]['id'] if arch_req_nodes else node_id)
                # if not parents_configured:
                #     self.update_logger_agent.warning(f"Cannot process work item: {message}")
                #     # Notify supervisor that this task cannot proceed
                #     await self.supervisor.notify_complete(self.current_work_item, success=False, error_message=message)
                #     return
                if not arch_req_nodes:
                    properties = {
                        "Title": "Requirements for Architecture",
                        "Description": "Summarized architectural and functional requirements extracted from top-level requirements",
                        "functional_requirements": "",
                        "architectural_requirements": ""
                    }
                    arch_req_node = await self.db.create_node(["ArchitecturalRequirement", "Requirement"], properties, node_id)
                    arch_req_node_id = arch_req_node['id']
                else:
                    arch_req_node_id = arch_req_nodes[0]['id']
                
                await self.configure_node(arch_req_node_id, "ArchitecturalRequirement", self.root_node_type, "architecture_requirement")
            else:
                self.update_logger_agent.warning("Not an architecture root")

            self.current_work_item.node_id = node_id
            self.current_work_item.node_type = "Architecture"
            await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error capturing requirements for architecture: {str(e)}", exc_info=True)
            raise
    
    async def create_components_config(self, node_id, node_type, project_id):
        """Helper method to configure all components in C4 model"""
        try:
            self.update_logger_agent.info(f"Starting component configuration for project {project_id}")
            
            # Get all components across all containers
            query = """
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
            -[:HAS_CHILD]->(container:Container)
            -[:HAS_CHILD]->(component:Component)
            WHERE ID(p) = $project_id
            RETURN DISTINCT ID(component) as id, component.Title as title
            """
            components = await self.db.async_run(query, project_id=project_id)
            
            # Add parent configuration check
            # parents_configured, message = await self.check_parent_configurations(components[0]['id'])
            # if not parents_configured:
            #     self.update_logger_agent.warning(f"Cannot process work item: {message}")
            #     # Notify supervisor that this task cannot proceed
            #     await self.supervisor.notify_complete(self.current_work_item, success=False, error_message=message)
            #     return
            
            for component in components.data():
                component_id = component['id']
                self.update_logger_agent.info(f"Configuring component {component['title']} ({component_id})")
                
                # Step 1: Create design details
                await self.create_design_details(component_id, "Component")
                
                # Step 2: Configure interfaces
                await self.autoconfig_interfaces(component_id, "Component")

                # Step 3: Configure component
                await self.configure(component_id, "Component", project_id)

               
                
            self.update_logger_agent.info("Completed configuring all components")

            self.current_work_item.node_id = component_id
            self.current_work_item.node_type = "Architecture"
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_components_config: {str(e)}")
            raise
        
    async def create_design_details(self, node_id, node_type):
        try:
            self.node_id = node_id
            self.node_type = node_type
            
            await self.configure_node(node_id, "Architecture", "Project", "design_details")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error creating design details: {str(e)}", exc_info=True)
            raise

    async def autoconfig_interfaces(self, node_id, node_type):
        """
        Process interfaces if they exist, gracefully handle components without interfaces
        """
        self.node_id = node_id
        self.node_type = node_type

        # Get the current node
        node = await self.db.get_node_by_id(node_id)
        if not node:
            self.update_logger_agent.warning(f"Node {node_id} not found")
            # await self.supervisor.notify_complete(self.current_work_item)
            return

        # Check for interface nodes
        interface_nodes = await self.db.get_child_nodes(self.node_id, "Interface")
        
        if not interface_nodes:
            self.update_logger_agent.info(f"No interfaces found for component {node_id} - skipping interface configuration")
            # await self.supervisor.notify_complete(self.current_work_item)
            return

        interface_node = interface_nodes[0]
        incoming_interfaces = interface_node['properties'].get('incoming_interfaces', [])
        if not incoming_interfaces:
            incoming_interfaces = json.loads(interface_node['properties'].get('incoming_interfaces', '[]'))

        if not incoming_interfaces:
            self.update_logger_agent.info(f"No incoming interfaces for component {node_id} - skipping interface configuration")
            # await self.supervisor.notify_complete(self.current_work_item)
            return

        # Process interfaces only if they exist
        try:
            # Configure design details for the interface node
            await self.configure_node(
                interface_node['id'], 
                "Interface", 
                self.root_node_type, 
                "design_details"
            )
            
            # Process source nodes if needed
            for interface in incoming_interfaces:
                source_id = interface.get('source_node_id')
                if source_id:
                    source_node = await self.db.get_node_by_id(source_id)
                    if source_node:
                        source_node_state = source_node['properties'].get('design_details_state')
                        if source_node_state != 'configured':
                            await self.configure_node(
                                source_id,
                                source_node['labels'][0],
                                self.root_node_type,
                                "design_details"
                            )

        except Exception as e:
            self.update_logger_agent.error(f"Error processing interfaces for node {node_id}: {str(e)}")
            # Continue flow even if there's an error
        finally:
            self.update_logger_agent.error(f"completed autoconfig interfaces {node_id}")
            # Always notify completion to continue the flow
            # await self.supervisor.notify_complete(self.current_work_item)

    async def create_interface_node(self, source_component_id, target_component_id, interface_properties={}):
        """
        Creates an interface node between two components and establishes the necessary relationships.
        """
        result = await self.db.create_interface_node(source_component_id, target_component_id, interface_properties)
        if result:
            self.update_logger.info(f"Created interface node between components {source_component_id} and {target_component_id}")
        else:
            self.update_logger.warning(f"Failed to create interface node between components {source_component_id} and {target_component_id}")
        return result

    async def configure(self, node_id, node_type, project_id):
        """Configure components the same way as legacy"""
        try:
            await self.configure_node(node_id, node_type, self.root_node_type, "configuration")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring: {str(e)}")
            raise

    async def configure_node(self, node_id, node_type, root_node_type, configuration_type):
        """
        Configure or reconfigure a requirement root node. This method handles both initial configuration 
        and subsequent reconfigurations based on system changes.
        
        Args:
            node_id: The ID of the node to configure
            node_type: The type of the node (RequirementRoot in this case)
            root_node_type: The type of the root node (typically Project)
            configuration_type: The type of configuration to perform
        """
        try:
            # Log the start of configuration process
            self.update_logger_agent.info(f"Starting configuration process for requirement root node: {node_id}")

            # Get or create task configuration from MongoDB
            # This maintains the state and progress of the configuration task
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            await self.send_update(self.task_id)
            
            if not task_configuration:
                # Initialize new task configuration if none exists
                task_configuration = {
                    'task_id': self.task_id, 
                    'queue_messages': [], 
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            # Extract task tracking information
            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']

            # Retrieve the node to be configured
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, 
                                                        {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            # Update configuration status tracking
            # This helps in monitoring the progress of configuration
            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'

            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)
            # Get the specific node type from the node's labels
            # This ensures we're using the correct type for the discussion
            node_type = Discussion.get_specific_node_type(node['labels'])
            
            # Initiate the configuration discussion
            # This will handle both initial configuration and reconfiguration
            # The discussion class will manage version comparison and change detection
            await conduct_discussion(
                node_id=node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=configuration_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )

            # Update the configuration status to reflect completion
            self.configuration_status[str(node_id)][configuration_type] = 'configured'
            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)
            
            # Log successful completion
            self.update_logger_agent.info(f"Successfully completed configuration for node {node_id}")
            
            # Retrieve and return the updated node
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            # Log any errors that occur during configuration
            self.update_logger_agent.error(f"Error in configure_node for node {node_id}: {str(e)}", 
                                        exc_info=True)
            raise

    async def create_or_get_system_context(self, project_id):
        system_context = await self.db.get_child_nodes(project_id, "SystemContext")
        if not system_context:
            properties = {
                "Title": "System Context",
                "Description": "High-level view of the system",
                "Type": "SystemContext"
            }
            system_context = await self.db.create_node(["SystemContext"], properties, project_id)
        else:
            system_context = system_context[0]
        return system_context

    async def get_or_create_containers(self, system_context_id):
        containers = await self.db.get_child_nodes(system_context_id, "Container")
        if not containers:
            # Create default containers (you can adjust this based on your needs)
            default_containers = [
                {"Title": "Web Application", "Description": "Provides functionality to users", "Technology": "React"},
                {"Title": "API Application", "Description": "Handles business logic", "Technology": "Node.js"},
                {"Title": "Database", "Description": "Stores system data", "Technology": "PostgreSQL"}
            ]
            containers = []
            for container_info in default_containers:
                container = await self.db.create_node(["Container"], container_info, system_context_id)
                containers.append(container)
        return containers

    async def get_or_create_components(self, container_id):
        components = await self.db.get_child_nodes(container_id, "Component")
        if not components:
            # Create default components (you can adjust this based on your needs)
            default_components = [
                {"Title": "User Interface", "Description": "Handles user interactions", "Technology": "React Components"},
                {"Title": "API Controller", "Description": "Manages API endpoints", "Technology": "Express.js"},
                {"Title": "Data Access Layer", "Description": "Interacts with the database", "Technology": "Sequelize ORM"}
            ]
            components = []
            for component_info in default_components:
                component = await self.db.create_node(["Component"], component_info, container_id)
                components.append(component)
        return components
    