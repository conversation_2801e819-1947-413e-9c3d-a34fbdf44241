from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.utils.code_generation_utils import yaml_to_json, json_to_yaml
from app.core.constants import REPOSITORIES_COLLECTION
import json


async def get_project_manifest_for_maintenance(project_id: int, repository_ids = [], all_repositories=False, node_db=None):
    mongo_db = get_mongo_db().db
    
    if not node_db:
        node_db = get_node_db()
    
    project = await node_db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    project_repos = mongo_db[REPOSITORIES_COLLECTION].find_one({
            "project_id": project_id
        }
        )
    project_manifest = project_repos.get("project_manifest", "")
    
    if project_manifest and not repository_ids:
        return project_manifest
    
    if repository_ids or all_repositories:
        container_manifests = []
        third_party_services = []
        for repository in project_repos.get("repositories"):
            if (repository.get("repo_id") in repository_ids) or all_repositories:
                current_manifest = repository.get("project_manifest")
                if current_manifest:
                    container_manifest = yaml_to_json(current_manifest)
                    containers = container_manifest.get("containers", [])
                    third_party_services.extend(container_manifest.get("third_party_services", []))
                    container_manifests.extend(containers)
                    
    if not container_manifests:
        return ""
    final_manifest = {
        "overview" : {
            "project_name" : project_details.get("Title", ""),
            "description" : project_details.get("Description", ""),
            "third_party_services" : list(set(third_party_services))
        },
        "containers" : container_manifests
    }
    
    return json_to_yaml(json.dumps(final_manifest))

    